/**
 *
 * Tickets
 *
 */

import React from 'react';
import { Route, Switch } from 'react-router-dom';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { compose } from 'redux';
import classNames from 'classnames';
import Modal from 'react-modal';

import { Button, FormWithSingleInput, Loader } from '../../components';

import { DEFAULT_TICKET_PATH, TICKET_PATH } from '../App/routes';
import injectSaga from '../../utils/injectSaga';
import injectReducer from '../../utils/injectReducer';

import TicketInfo from './TicketInfo';
import SideNavbar from './SideNavbar';
import TicketAndUserInfo from './TicketAndUserInfo';

import { setTicketId as setTicketIdApi, submitTicketId as submitTicketIdApi,
    addNewTicket as addNewTicket<PERSON><PERSON>, setActiveTicket as setActiveTicket<PERSON><PERSON>,
    getInitialAuthFactor as getInitial<PERSON>uthFactor<PERSON><PERSON>, setAuthFactorValue as setAuthFactorValue<PERSON>pi,
    verifyAuthFactor as verifyAuthFactorApi, setAsyncAuthInfo as setAsyncAuthInfoApi,
    setAuthExpiredScreen as setAuthExpiredScreenApi,
    closeTicket as closeTicketApi, setErrTicketInfo as setErrTicketInfoApi,
} from './actions';
import { makeSelectTickets, makeSelectLoading } from './selectors';
import reducer from './reducer';
import saga from './saga';

import './style.scss';

const customStylesForModal = {
    overlay: {
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
        zIndex: 1
    },
    content : {
        top: '25%',
        left: '50%',
        right: 'auto',
        bottom: 'auto',
        padding: 40,
        transform: 'translateX(-50%) translateX(120px)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        fontSize: 16,
        width: 600,
        border: 0,
        boxShadow: 'rgba(0, 0, 0, 0.15) 0px 1px 15px',
    }
};

class Tickets extends React.Component {
    constructor(props) {
        super(props);

        this.state = {
            showCloseModal: false,
            closeTicketIndex: 0
        };
    }

    getTicketInfo = () => {
        const { ticketList } = this.props;
        const activeIndex = ticketList.findIndex(item => item.isActive);
        const activeTicket = ticketList[activeIndex];

        return {
            activeTicket,
            activeIndex,
        };
    }

    handleChange = index => event => {
        const { setTicketId } = this.props;

        setTicketId({ ticketId: event.target.value, index });
    }

    handleSubmit = (ticketId, index) => event => {
        const { submitTicketId, setErrTicketInfo } = this.props;

        if (!ticketId) {
            setErrTicketInfo({ type: 'ticket', index, err: 'Please enter a valid ticket id' });
            return;
        }
        
        submitTicketId({ ticketId, index });
    }

    addNewTicket = () => {
        const { addNewTicket, history } = this.props;

        addNewTicket();
        history.push(DEFAULT_TICKET_PATH);
    }

    setActiveTicket = (index) => {
        const { history, setActiveTicket, ticketList } = this.props;

        setActiveTicket({ index });

        const { isAuthorizedForSensitiveScreen, isAuthorizedForLessSensitiveScreen, ticketId } = ticketList[index];
        const isAuthorizedForScreens = isAuthorizedForLessSensitiveScreen || isAuthorizedForSensitiveScreen;

        const TICKET_PATH = `${DEFAULT_TICKET_PATH}${ticketId}`;

        if (isAuthorizedForScreens) {
            history.push(`${TICKET_PATH}/banking/profile`);
        } else {
            history.push(TICKET_PATH);
        }
    }

    renderDefaultRoute = (ticket, index) => {
        const { ticketId, errLabel } = ticket;

        return (
            <FormWithSingleInput
                label='Ticket ID'
                labelId='ticket'
                extClasses={{
                    container: 'ticket-form-container',
                    label: 'ticket-form-container__label',
                    errLabel: 'ticket-form-container__err-label'
                }}
                input={{
                    value: ticketId,
                    pattern: '\\d+',
                    onChange: this.handleChange(index)
                }}
                cta={{
                    primaryLabel: 'Submit',
                    onPrimaryClick: this.handleSubmit(ticketId, index)
                }}
                errLabel={errLabel}
            />
        );
    }

    renderRoutes = (ticket, index) => {
        const { setAuthFactorValue, verifyAuthFactor, setAsyncAuthInfo } = this.props;

        const ticketInfoProps = {
            setAuthFactorValue, verifyAuthFactor, setAsyncAuthInfo,
        };

        return (
            <Switch>
                <Route
                    path={TICKET_PATH}
                    render={
                        props => (
                            <TicketInfo
                                {...props}
                                {...ticketInfoProps}
                                ticket={ticket}
                                index={index}
                                submitTicketId={this.handleSubmit}
                                toggleCloseTicketModal={this.toggleCloseTicketModal}
                            />
                        )
                    }
                />
                <Route
                    exact
                    path={DEFAULT_TICKET_PATH}
                    render={() => this.renderDefaultRoute(ticket, index)}
                />
            </Switch>
        );
    }

    toggleCloseTicketModal = (index, flag) => (e) => {
        e.preventDefault();
        e.stopPropagation();

        this.setState({
            showCloseModal: flag,
            closeTicketIndex: index
        });
    }

    closeTicket = (index) => {
        const { closeTicket, ticketList } = this.props;
        const { activeIndex } = this.getTicketInfo();

        if (activeIndex === index) { // closing active tab
            if (ticketList.length - 1 === index) { // last tab
                this.addNewTicket();
            } else { // make the next tab active
                this.setActiveTicket(index + 1);
            }
        }

        this.setState({
            showCloseModal: false,
            closeTicketIndex: 0
        });
        setTimeout(() => closeTicket({ index }), 200);
    }

    renderTabs = () => {
        const { ticketList } = this.props;
        const showNewTab = !!Object.keys(ticketList[ticketList.length - 1].basicInfo).length;

        return (
            <div className='ticket-tabs'>
                {
                    ticketList.map((item, index) => (
                        <div
                            key = {item.ticketId}
                            className={classNames({
                                'ticket-tab': true,
                                'ticket-tab--active': item.isActive
                            })}
                            onClick={() => this.setActiveTicket(index)}
                        >
                            {
                                Object.keys(item.basicInfo).length ? (
                                    <React.Fragment>
                                        <span>{`ID - ${item.ticketId}`}</span>
                                        <div className='ticket-tab__cb' onClick={this.toggleCloseTicketModal(index, true)}>X</div>
                                    </React.Fragment>
                                ) : (
                                    <span>New Ticket</span>
                                )
                            }
                        </div>
                    ))
                }
                {
                    showNewTab ? (
                        <div
                            className='ticket-tab'
                            onClick={this.addNewTicket}
                        >
                            <span>Add Ticket Id</span>
                        </div>
                    ) : null
                }
            </div>
        );
    }

    renderAuthExpiredScreen = (ticket, index) => {
        const { setAuthExpiredScreen, getInitialAuthFactor, history } = this.props;
        const { isAuthExpiredScreen, ticketId, userId } = ticket;

        return (
            <Modal
                isOpen={isAuthExpiredScreen}
                style={customStylesForModal}
                contentLabel='Auth Expired Screen Modal'
            >
                <div className='ticket-modal__heading'>
                    User Authorisation for this action has expired. You need to collect authorisation from the user again.
                </div>
                <div className='ticket-modal__subheading'>Press button below to go to the authorisation flow</div>
                <Button
                    primary
                    label='Go to authorisation flow'
                    onClick={() => {
                        setAuthExpiredScreen({ flag: false, index });
                        getInitialAuthFactor({ ticketId, userId, index });
                        history.push(`${DEFAULT_TICKET_PATH}${ticketId}`);
                    }}
                />
            </Modal>
        );
    }

    renderCloseTicketScreen = () => {
        const { showCloseModal, closeTicketIndex } = this.state;
        const { ticketList } = this.props;
        const ticket = ticketList[closeTicketIndex];
        const { ticketId, basicInfo: { source, phoneNumber, ticketPhoneNumber, name },
            hasConfirmedUserInfo, userInfo } = ticket;
        const ticketAndUserInfo = {
            ticketId,
            source,
            phoneNumber: ticketPhoneNumber || phoneNumber,
            name: hasConfirmedUserInfo ? userInfo.name : name
        };

        return (
            <Modal
                isOpen={showCloseModal}
                style={customStylesForModal}
                contentLabel='Close Ticket Screen Modal'
            >
                <TicketAndUserInfo {...ticketAndUserInfo} extClasses='ticket-modal__ctw' />
                <div className='ticket-modal__heading'>
                    Are you sure you want to close this ticket?
                </div>
                <div className='frcsbWrapper ticket-modal__ct-ctas'>
                    <Button
                        secondary
                        label='Cancel'
                        onClick={this.toggleCloseTicketModal(closeTicketIndex, false)}
                    />
                    <Button
                        primary
                        label='Yes, close'
                        onClick={() => this.closeTicket(closeTicketIndex)}
                    />
                </div>
            </Modal>
        );
    }

    render() {
        const { activeTicket, activeIndex } = this.getTicketInfo();
        const { loading } = this.props;

        const isLoading = loading.basicInfo || loading.userInfo || loading.authInfo;
        const { ticketId, isAuthorizedForSensitiveScreen, isAuthorizedForLessSensitiveScreen } = activeTicket;
        const isAuthorizedForScreens = isAuthorizedForLessSensitiveScreen || isAuthorizedForSensitiveScreen;

        return (
            <React.Fragment>
                <div className='ticket-container'>
                    {this.renderTabs()}
                    <div className='ticket-wrapper'>
                        <SideNavbar
                            isAuthorizedForLessSensitiveScreen={isAuthorizedForLessSensitiveScreen}
                            ticketId={isAuthorizedForScreens && ticketId}
                        />
                        <div className='ticket-content-container'>{this.renderRoutes(activeTicket, activeIndex)}</div>
                    </div>
                </div>
                {this.renderAuthExpiredScreen(activeTicket, activeIndex)}
                {this.renderCloseTicketScreen()}
                <Loader visible={isLoading} />
            </React.Fragment>
        );
    }
}

const mapStateToProps = createStructuredSelector({
    loading: makeSelectLoading(),
    ticketList: makeSelectTickets(),
});

const mapDispatchToProps = dispatch => ({
    setTicketId: data => dispatch(setTicketIdApi(data)),
    submitTicketId: data => dispatch(submitTicketIdApi(data)),
    addNewTicket: data => dispatch(addNewTicketApi(data)),
    closeTicket: data => dispatch(closeTicketApi(data)),
    setActiveTicket: data => dispatch(setActiveTicketApi(data)),
    getInitialAuthFactor: data => dispatch(getInitialAuthFactorApi(data)),
    setAuthFactorValue: data => dispatch(setAuthFactorValueApi(data)),
    verifyAuthFactor: data => dispatch(verifyAuthFactorApi(data)),
    setAsyncAuthInfo: data => dispatch(setAsyncAuthInfoApi(data)),
    setAuthExpiredScreen: data => dispatch(setAuthExpiredScreenApi(data)),
    setErrTicketInfo: data => dispatch(setErrTicketInfoApi(data)),
});

const withConnect = connect(mapStateToProps, mapDispatchToProps);

const withReducer = injectReducer({ key: 'tickets', reducer });
const withSaga = injectSaga({ key: 'tickets', saga });

export default compose(
    withReducer,
    withSaga,
    withConnect,
)(Tickets);
