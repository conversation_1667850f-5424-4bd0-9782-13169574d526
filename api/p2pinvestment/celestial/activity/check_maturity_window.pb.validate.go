// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/p2pinvestment/celestial/activity/check_maturity_window.proto

package activity

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CheckMaturityWindowRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckMaturityWindowRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckMaturityWindowRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckMaturityWindowRequestMultiError, or nil if none found.
func (m *CheckMaturityWindowRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckMaturityWindowRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckMaturityWindowRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckMaturityWindowRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckMaturityWindowRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OriginalTxnId

	if len(errors) > 0 {
		return CheckMaturityWindowRequestMultiError(errors)
	}

	return nil
}

// CheckMaturityWindowRequestMultiError is an error wrapping multiple
// validation errors returned by CheckMaturityWindowRequest.ValidateAll() if
// the designated constraints aren't met.
type CheckMaturityWindowRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckMaturityWindowRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckMaturityWindowRequestMultiError) AllErrors() []error { return m }

// CheckMaturityWindowRequestValidationError is the validation error returned
// by CheckMaturityWindowRequest.Validate if the designated constraints aren't met.
type CheckMaturityWindowRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckMaturityWindowRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckMaturityWindowRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckMaturityWindowRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckMaturityWindowRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckMaturityWindowRequestValidationError) ErrorName() string {
	return "CheckMaturityWindowRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckMaturityWindowRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckMaturityWindowRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckMaturityWindowRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckMaturityWindowRequestValidationError{}

// Validate checks the field values on CheckMaturityWindowResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckMaturityWindowResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckMaturityWindowResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckMaturityWindowResponseMultiError, or nil if none found.
func (m *CheckMaturityWindowResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckMaturityWindowResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckMaturityWindowResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckMaturityWindowResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckMaturityWindowResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaturityStatus

	if all {
		switch v := interface{}(m.GetMaturityStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckMaturityWindowResponseValidationError{
					field:  "MaturityStartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckMaturityWindowResponseValidationError{
					field:  "MaturityStartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaturityStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckMaturityWindowResponseValidationError{
				field:  "MaturityStartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckMaturityWindowResponseMultiError(errors)
	}

	return nil
}

// CheckMaturityWindowResponseMultiError is an error wrapping multiple
// validation errors returned by CheckMaturityWindowResponse.ValidateAll() if
// the designated constraints aren't met.
type CheckMaturityWindowResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckMaturityWindowResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckMaturityWindowResponseMultiError) AllErrors() []error { return m }

// CheckMaturityWindowResponseValidationError is the validation error returned
// by CheckMaturityWindowResponse.Validate if the designated constraints
// aren't met.
type CheckMaturityWindowResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckMaturityWindowResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckMaturityWindowResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckMaturityWindowResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckMaturityWindowResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckMaturityWindowResponseValidationError) ErrorName() string {
	return "CheckMaturityWindowResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckMaturityWindowResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckMaturityWindowResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckMaturityWindowResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckMaturityWindowResponseValidationError{}
