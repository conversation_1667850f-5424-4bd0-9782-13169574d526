//go:generate gen_sql -types=LoanApplicationStatus,LoanApplicationSubStatus,LoanApplicationStageName,LoanApplicationStageStatus,LoanApplicationStageSubStatus

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/stockguardian/sgapplication/enums/enums.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LoanApplicationStatus int32

const (
	LoanApplicationStatus_LOAN_APPLICATION_STATUS_UNSPECIFIED         LoanApplicationStatus = 0
	LoanApplicationStatus_LOAN_APPLICATION_STATUS_CREATED             LoanApplicationStatus = 1
	LoanApplicationStatus_LOAN_APPLICATION_STATUS_IN_PROGRESS         LoanApplicationStatus = 2
	LoanApplicationStatus_LOAN_APPLICATION_STATUS_MANUAL_INTERVENTION LoanApplicationStatus = 3
	LoanApplicationStatus_LOAN_APPLICATION_STATUS_SUCCESS             LoanApplicationStatus = 4
	LoanApplicationStatus_LOAN_APPLICATION_STATUS_FAILED              LoanApplicationStatus = 5
	LoanApplicationStatus_LOAN_APPLICATION_STATUS_CANCELLED           LoanApplicationStatus = 6
)

// Enum value maps for LoanApplicationStatus.
var (
	LoanApplicationStatus_name = map[int32]string{
		0: "LOAN_APPLICATION_STATUS_UNSPECIFIED",
		1: "LOAN_APPLICATION_STATUS_CREATED",
		2: "LOAN_APPLICATION_STATUS_IN_PROGRESS",
		3: "LOAN_APPLICATION_STATUS_MANUAL_INTERVENTION",
		4: "LOAN_APPLICATION_STATUS_SUCCESS",
		5: "LOAN_APPLICATION_STATUS_FAILED",
		6: "LOAN_APPLICATION_STATUS_CANCELLED",
	}
	LoanApplicationStatus_value = map[string]int32{
		"LOAN_APPLICATION_STATUS_UNSPECIFIED":         0,
		"LOAN_APPLICATION_STATUS_CREATED":             1,
		"LOAN_APPLICATION_STATUS_IN_PROGRESS":         2,
		"LOAN_APPLICATION_STATUS_MANUAL_INTERVENTION": 3,
		"LOAN_APPLICATION_STATUS_SUCCESS":             4,
		"LOAN_APPLICATION_STATUS_FAILED":              5,
		"LOAN_APPLICATION_STATUS_CANCELLED":           6,
	}
)

func (x LoanApplicationStatus) Enum() *LoanApplicationStatus {
	p := new(LoanApplicationStatus)
	*p = x
	return p
}

func (x LoanApplicationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanApplicationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[0].Descriptor()
}

func (LoanApplicationStatus) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[0]
}

func (x LoanApplicationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanApplicationStatus.Descriptor instead.
func (LoanApplicationStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_enums_enums_proto_rawDescGZIP(), []int{0}
}

type LoanApplicationSubStatus int32

const (
	LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_UNSPECIFIED LoanApplicationSubStatus = 0
)

// Enum value maps for LoanApplicationSubStatus.
var (
	LoanApplicationSubStatus_name = map[int32]string{
		0: "LOAN_APPLICATION_SUB_STATUS_UNSPECIFIED",
	}
	LoanApplicationSubStatus_value = map[string]int32{
		"LOAN_APPLICATION_SUB_STATUS_UNSPECIFIED": 0,
	}
)

func (x LoanApplicationSubStatus) Enum() *LoanApplicationSubStatus {
	p := new(LoanApplicationSubStatus)
	*p = x
	return p
}

func (x LoanApplicationSubStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanApplicationSubStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[1].Descriptor()
}

func (LoanApplicationSubStatus) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[1]
}

func (x LoanApplicationSubStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanApplicationSubStatus.Descriptor instead.
func (LoanApplicationSubStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_enums_enums_proto_rawDescGZIP(), []int{1}
}

type LoanApplicationStageName int32

const (
	LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_UNSPECIFIED      LoanApplicationStageName = 0
	LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_KYC              LoanApplicationStageName = 1
	LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_OFFER_GENERATION LoanApplicationStageName = 2
	LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_DRAWDOWN         LoanApplicationStageName = 3
	LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_MANDATE          LoanApplicationStageName = 4
	LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_ESIGN            LoanApplicationStageName = 5
	LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_DISBURSEMENT     LoanApplicationStageName = 6
	LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_PENNY_DROP       LoanApplicationStageName = 7
)

// Enum value maps for LoanApplicationStageName.
var (
	LoanApplicationStageName_name = map[int32]string{
		0: "LOAN_APPLICATION_STAGE_NAME_UNSPECIFIED",
		1: "LOAN_APPLICATION_STAGE_NAME_KYC",
		2: "LOAN_APPLICATION_STAGE_NAME_OFFER_GENERATION",
		3: "LOAN_APPLICATION_STAGE_NAME_DRAWDOWN",
		4: "LOAN_APPLICATION_STAGE_NAME_MANDATE",
		5: "LOAN_APPLICATION_STAGE_NAME_ESIGN",
		6: "LOAN_APPLICATION_STAGE_NAME_DISBURSEMENT",
		7: "LOAN_APPLICATION_STAGE_NAME_PENNY_DROP",
	}
	LoanApplicationStageName_value = map[string]int32{
		"LOAN_APPLICATION_STAGE_NAME_UNSPECIFIED":      0,
		"LOAN_APPLICATION_STAGE_NAME_KYC":              1,
		"LOAN_APPLICATION_STAGE_NAME_OFFER_GENERATION": 2,
		"LOAN_APPLICATION_STAGE_NAME_DRAWDOWN":         3,
		"LOAN_APPLICATION_STAGE_NAME_MANDATE":          4,
		"LOAN_APPLICATION_STAGE_NAME_ESIGN":            5,
		"LOAN_APPLICATION_STAGE_NAME_DISBURSEMENT":     6,
		"LOAN_APPLICATION_STAGE_NAME_PENNY_DROP":       7,
	}
)

func (x LoanApplicationStageName) Enum() *LoanApplicationStageName {
	p := new(LoanApplicationStageName)
	*p = x
	return p
}

func (x LoanApplicationStageName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanApplicationStageName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[2].Descriptor()
}

func (LoanApplicationStageName) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[2]
}

func (x LoanApplicationStageName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanApplicationStageName.Descriptor instead.
func (LoanApplicationStageName) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_enums_enums_proto_rawDescGZIP(), []int{2}
}

type LoanApplicationStageStatus int32

const (
	LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_UNSPECIFIED         LoanApplicationStageStatus = 0
	LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_CREATED             LoanApplicationStageStatus = 1
	LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_IN_PROGRESS         LoanApplicationStageStatus = 2
	LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_SUCCESS             LoanApplicationStageStatus = 3
	LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_FAILED              LoanApplicationStageStatus = 4
	LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_MANUAL_INTERVENTION LoanApplicationStageStatus = 5
	LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_EXPIRED             LoanApplicationStageStatus = 6
	LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_CANCELLED           LoanApplicationStageStatus = 7
)

// Enum value maps for LoanApplicationStageStatus.
var (
	LoanApplicationStageStatus_name = map[int32]string{
		0: "LOAN_APPLICATION_STAGE_STATUS_UNSPECIFIED",
		1: "LOAN_APPLICATION_STAGE_STATUS_CREATED",
		2: "LOAN_APPLICATION_STAGE_STATUS_IN_PROGRESS",
		3: "LOAN_APPLICATION_STAGE_STATUS_SUCCESS",
		4: "LOAN_APPLICATION_STAGE_STATUS_FAILED",
		5: "LOAN_APPLICATION_STAGE_STATUS_MANUAL_INTERVENTION",
		6: "LOAN_APPLICATION_STAGE_STATUS_EXPIRED",
		7: "LOAN_APPLICATION_STAGE_STATUS_CANCELLED",
	}
	LoanApplicationStageStatus_value = map[string]int32{
		"LOAN_APPLICATION_STAGE_STATUS_UNSPECIFIED":         0,
		"LOAN_APPLICATION_STAGE_STATUS_CREATED":             1,
		"LOAN_APPLICATION_STAGE_STATUS_IN_PROGRESS":         2,
		"LOAN_APPLICATION_STAGE_STATUS_SUCCESS":             3,
		"LOAN_APPLICATION_STAGE_STATUS_FAILED":              4,
		"LOAN_APPLICATION_STAGE_STATUS_MANUAL_INTERVENTION": 5,
		"LOAN_APPLICATION_STAGE_STATUS_EXPIRED":             6,
		"LOAN_APPLICATION_STAGE_STATUS_CANCELLED":           7,
	}
)

func (x LoanApplicationStageStatus) Enum() *LoanApplicationStageStatus {
	p := new(LoanApplicationStageStatus)
	*p = x
	return p
}

func (x LoanApplicationStageStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanApplicationStageStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[3].Descriptor()
}

func (LoanApplicationStageStatus) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[3]
}

func (x LoanApplicationStageStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanApplicationStageStatus.Descriptor instead.
func (LoanApplicationStageStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_enums_enums_proto_rawDescGZIP(), []int{3}
}

type LoanApplicationStageSubStatus int32

const (
	LoanApplicationStageSubStatus_LOAN_APPLICATION_STAGE_SUB_STATUS_UNSPECIFIED                         LoanApplicationStageSubStatus = 0
	LoanApplicationStageSubStatus_LOAN_APPLICATION_STAGE_SUB_STATUS_OFFER_REJECTED                      LoanApplicationStageSubStatus = 1
	LoanApplicationStageSubStatus_LOAN_APPLICATION_STAGE_SUB_STATUS_OFFER_EXPIRED                       LoanApplicationStageSubStatus = 2
	LoanApplicationStageSubStatus_LOAN_APPLICATION_STAGE_SUB_STATUS_OFFER_LOCKED_BY_ANOTHER_APPLICATION LoanApplicationStageSubStatus = 3
	LoanApplicationStageSubStatus_LOAN_APPLICATION_STAGE_SUB_STATUS_DRAWDOWN_VALIDATION_FAILED          LoanApplicationStageSubStatus = 4
	LoanApplicationStageSubStatus_LOAN_APPLICATION_STAGE_SUB_STATUS_KYC_ALREADY_COMPLETED               LoanApplicationStageSubStatus = 5
	LoanApplicationStageSubStatus_LOAN_APPLICATION_STAGE_SUB_STATUS_OFFER_INACTIVE                      LoanApplicationStageSubStatus = 6
	LoanApplicationStageSubStatus_LOAN_APPLICATION_STAGE_SUB_STATUS_CKYC_GENDER_INVALID                 LoanApplicationStageSubStatus = 7
	LoanApplicationStageSubStatus_LOAN_APPLICATION_STAGE_SUB_STATUS_DISBURSAL_MANUAL_RETRY              LoanApplicationStageSubStatus = 8
)

// Enum value maps for LoanApplicationStageSubStatus.
var (
	LoanApplicationStageSubStatus_name = map[int32]string{
		0: "LOAN_APPLICATION_STAGE_SUB_STATUS_UNSPECIFIED",
		1: "LOAN_APPLICATION_STAGE_SUB_STATUS_OFFER_REJECTED",
		2: "LOAN_APPLICATION_STAGE_SUB_STATUS_OFFER_EXPIRED",
		3: "LOAN_APPLICATION_STAGE_SUB_STATUS_OFFER_LOCKED_BY_ANOTHER_APPLICATION",
		4: "LOAN_APPLICATION_STAGE_SUB_STATUS_DRAWDOWN_VALIDATION_FAILED",
		5: "LOAN_APPLICATION_STAGE_SUB_STATUS_KYC_ALREADY_COMPLETED",
		6: "LOAN_APPLICATION_STAGE_SUB_STATUS_OFFER_INACTIVE",
		7: "LOAN_APPLICATION_STAGE_SUB_STATUS_CKYC_GENDER_INVALID",
		8: "LOAN_APPLICATION_STAGE_SUB_STATUS_DISBURSAL_MANUAL_RETRY",
	}
	LoanApplicationStageSubStatus_value = map[string]int32{
		"LOAN_APPLICATION_STAGE_SUB_STATUS_UNSPECIFIED":                         0,
		"LOAN_APPLICATION_STAGE_SUB_STATUS_OFFER_REJECTED":                      1,
		"LOAN_APPLICATION_STAGE_SUB_STATUS_OFFER_EXPIRED":                       2,
		"LOAN_APPLICATION_STAGE_SUB_STATUS_OFFER_LOCKED_BY_ANOTHER_APPLICATION": 3,
		"LOAN_APPLICATION_STAGE_SUB_STATUS_DRAWDOWN_VALIDATION_FAILED":          4,
		"LOAN_APPLICATION_STAGE_SUB_STATUS_KYC_ALREADY_COMPLETED":               5,
		"LOAN_APPLICATION_STAGE_SUB_STATUS_OFFER_INACTIVE":                      6,
		"LOAN_APPLICATION_STAGE_SUB_STATUS_CKYC_GENDER_INVALID":                 7,
		"LOAN_APPLICATION_STAGE_SUB_STATUS_DISBURSAL_MANUAL_RETRY":              8,
	}
)

func (x LoanApplicationStageSubStatus) Enum() *LoanApplicationStageSubStatus {
	p := new(LoanApplicationStageSubStatus)
	*p = x
	return p
}

func (x LoanApplicationStageSubStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanApplicationStageSubStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[4].Descriptor()
}

func (LoanApplicationStageSubStatus) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[4]
}

func (x LoanApplicationStageSubStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanApplicationStageSubStatus.Descriptor instead.
func (LoanApplicationStageSubStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_enums_enums_proto_rawDescGZIP(), []int{4}
}

type LoanApplicationFieldMask int32

const (
	LoanApplicationFieldMask_LOAN_APPLICATION_FIELD_MASK_UNSPECIFIED LoanApplicationFieldMask = 0
	LoanApplicationFieldMask_LOAN_APPLICATION_FIELD_MASK_EXTERNAL_ID LoanApplicationFieldMask = 1
	LoanApplicationFieldMask_LOAN_APPLICATION_FIELD_MASK_DETAILS     LoanApplicationFieldMask = 2
	LoanApplicationFieldMask_LOAN_APPLICATION_FIELD_MASK_STATUS      LoanApplicationFieldMask = 3
	LoanApplicationFieldMask_LOAN_APPLICATION_FIELD_MASK_SUB_STATUS  LoanApplicationFieldMask = 4
)

// Enum value maps for LoanApplicationFieldMask.
var (
	LoanApplicationFieldMask_name = map[int32]string{
		0: "LOAN_APPLICATION_FIELD_MASK_UNSPECIFIED",
		1: "LOAN_APPLICATION_FIELD_MASK_EXTERNAL_ID",
		2: "LOAN_APPLICATION_FIELD_MASK_DETAILS",
		3: "LOAN_APPLICATION_FIELD_MASK_STATUS",
		4: "LOAN_APPLICATION_FIELD_MASK_SUB_STATUS",
	}
	LoanApplicationFieldMask_value = map[string]int32{
		"LOAN_APPLICATION_FIELD_MASK_UNSPECIFIED": 0,
		"LOAN_APPLICATION_FIELD_MASK_EXTERNAL_ID": 1,
		"LOAN_APPLICATION_FIELD_MASK_DETAILS":     2,
		"LOAN_APPLICATION_FIELD_MASK_STATUS":      3,
		"LOAN_APPLICATION_FIELD_MASK_SUB_STATUS":  4,
	}
)

func (x LoanApplicationFieldMask) Enum() *LoanApplicationFieldMask {
	p := new(LoanApplicationFieldMask)
	*p = x
	return p
}

func (x LoanApplicationFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanApplicationFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[5].Descriptor()
}

func (LoanApplicationFieldMask) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[5]
}

func (x LoanApplicationFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanApplicationFieldMask.Descriptor instead.
func (LoanApplicationFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_enums_enums_proto_rawDescGZIP(), []int{5}
}

type LoanApplicationStageFieldMask int32

const (
	LoanApplicationStageFieldMask_LOAN_APPLICATION_STAGE_FIELD_MASK_UNSPECIFIED  LoanApplicationStageFieldMask = 0
	LoanApplicationStageFieldMask_LOAN_APPLICATION_STAGE_FIELD_MASK_EXTERNAL_ID  LoanApplicationStageFieldMask = 1
	LoanApplicationStageFieldMask_LOAN_APPLICATION_STAGE_FIELD_MASK_DETAILS      LoanApplicationStageFieldMask = 2
	LoanApplicationStageFieldMask_LOAN_APPLICATION_STAGE_FIELD_MASK_STATUS       LoanApplicationStageFieldMask = 3
	LoanApplicationStageFieldMask_LOAN_APPLICATION_STAGE_FIELD_MASK_SUB_STATUS   LoanApplicationStageFieldMask = 4
	LoanApplicationStageFieldMask_LOAN_APPLICATION_STAGE_FIELD_MASK_COMPLETED_AT LoanApplicationStageFieldMask = 5
)

// Enum value maps for LoanApplicationStageFieldMask.
var (
	LoanApplicationStageFieldMask_name = map[int32]string{
		0: "LOAN_APPLICATION_STAGE_FIELD_MASK_UNSPECIFIED",
		1: "LOAN_APPLICATION_STAGE_FIELD_MASK_EXTERNAL_ID",
		2: "LOAN_APPLICATION_STAGE_FIELD_MASK_DETAILS",
		3: "LOAN_APPLICATION_STAGE_FIELD_MASK_STATUS",
		4: "LOAN_APPLICATION_STAGE_FIELD_MASK_SUB_STATUS",
		5: "LOAN_APPLICATION_STAGE_FIELD_MASK_COMPLETED_AT",
	}
	LoanApplicationStageFieldMask_value = map[string]int32{
		"LOAN_APPLICATION_STAGE_FIELD_MASK_UNSPECIFIED":  0,
		"LOAN_APPLICATION_STAGE_FIELD_MASK_EXTERNAL_ID":  1,
		"LOAN_APPLICATION_STAGE_FIELD_MASK_DETAILS":      2,
		"LOAN_APPLICATION_STAGE_FIELD_MASK_STATUS":       3,
		"LOAN_APPLICATION_STAGE_FIELD_MASK_SUB_STATUS":   4,
		"LOAN_APPLICATION_STAGE_FIELD_MASK_COMPLETED_AT": 5,
	}
)

func (x LoanApplicationStageFieldMask) Enum() *LoanApplicationStageFieldMask {
	p := new(LoanApplicationStageFieldMask)
	*p = x
	return p
}

func (x LoanApplicationStageFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanApplicationStageFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[6].Descriptor()
}

func (LoanApplicationStageFieldMask) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[6]
}

func (x LoanApplicationStageFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanApplicationStageFieldMask.Descriptor instead.
func (LoanApplicationStageFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_enums_enums_proto_rawDescGZIP(), []int{6}
}

type ConsentType int32

const (
	ConsentType_CONSENT_TYPE_UNSPECIFIED               ConsentType = 0
	ConsentType_CONSENT_TYPE_CKYC_DOWNLOAD             ConsentType = 1
	ConsentType_CONSENT_TYPE_CREDIT_REPORT_HARD_PULL   ConsentType = 2
	ConsentType_CONSENT_TYPE_DATA_USAGE_FOR_PROMOTIONS ConsentType = 3
)

// Enum value maps for ConsentType.
var (
	ConsentType_name = map[int32]string{
		0: "CONSENT_TYPE_UNSPECIFIED",
		1: "CONSENT_TYPE_CKYC_DOWNLOAD",
		2: "CONSENT_TYPE_CREDIT_REPORT_HARD_PULL",
		3: "CONSENT_TYPE_DATA_USAGE_FOR_PROMOTIONS",
	}
	ConsentType_value = map[string]int32{
		"CONSENT_TYPE_UNSPECIFIED":               0,
		"CONSENT_TYPE_CKYC_DOWNLOAD":             1,
		"CONSENT_TYPE_CREDIT_REPORT_HARD_PULL":   2,
		"CONSENT_TYPE_DATA_USAGE_FOR_PROMOTIONS": 3,
	}
)

func (x ConsentType) Enum() *ConsentType {
	p := new(ConsentType)
	*p = x
	return p
}

func (x ConsentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConsentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[7].Descriptor()
}

func (ConsentType) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[7]
}

func (x ConsentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConsentType.Descriptor instead.
func (ConsentType) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_enums_enums_proto_rawDescGZIP(), []int{7}
}

type UpdateUserDetailsFieldMask int32

const (
	UpdateUserDetailsFieldMask_UPDATE_USER_DETAILS_FIELD_MASK_UNSPECIFIED          UpdateUserDetailsFieldMask = 0
	UpdateUserDetailsFieldMask_UPDATE_USER_DETAILS_FIELD_MASK_ADDRESS              UpdateUserDetailsFieldMask = 1
	UpdateUserDetailsFieldMask_UPDATE_USER_DETAILS_FIELD_MASK_EMPLOYMENT_DETAILS   UpdateUserDetailsFieldMask = 2
	UpdateUserDetailsFieldMask_UPDATE_USER_DETAILS_FIELD_MASK_BANK_ACCOUNT_DETAILS UpdateUserDetailsFieldMask = 3
	UpdateUserDetailsFieldMask_UPDATE_USER_DETAILS_FIELD_MASK_PERSONAL_DETAILS     UpdateUserDetailsFieldMask = 4
	UpdateUserDetailsFieldMask_UPDATE_USER_DETAILS_FIELD_MASK_LOCATION_DETAILS     UpdateUserDetailsFieldMask = 5
)

// Enum value maps for UpdateUserDetailsFieldMask.
var (
	UpdateUserDetailsFieldMask_name = map[int32]string{
		0: "UPDATE_USER_DETAILS_FIELD_MASK_UNSPECIFIED",
		1: "UPDATE_USER_DETAILS_FIELD_MASK_ADDRESS",
		2: "UPDATE_USER_DETAILS_FIELD_MASK_EMPLOYMENT_DETAILS",
		3: "UPDATE_USER_DETAILS_FIELD_MASK_BANK_ACCOUNT_DETAILS",
		4: "UPDATE_USER_DETAILS_FIELD_MASK_PERSONAL_DETAILS",
		5: "UPDATE_USER_DETAILS_FIELD_MASK_LOCATION_DETAILS",
	}
	UpdateUserDetailsFieldMask_value = map[string]int32{
		"UPDATE_USER_DETAILS_FIELD_MASK_UNSPECIFIED":          0,
		"UPDATE_USER_DETAILS_FIELD_MASK_ADDRESS":              1,
		"UPDATE_USER_DETAILS_FIELD_MASK_EMPLOYMENT_DETAILS":   2,
		"UPDATE_USER_DETAILS_FIELD_MASK_BANK_ACCOUNT_DETAILS": 3,
		"UPDATE_USER_DETAILS_FIELD_MASK_PERSONAL_DETAILS":     4,
		"UPDATE_USER_DETAILS_FIELD_MASK_LOCATION_DETAILS":     5,
	}
)

func (x UpdateUserDetailsFieldMask) Enum() *UpdateUserDetailsFieldMask {
	p := new(UpdateUserDetailsFieldMask)
	*p = x
	return p
}

func (x UpdateUserDetailsFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateUserDetailsFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[8].Descriptor()
}

func (UpdateUserDetailsFieldMask) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[8]
}

func (x UpdateUserDetailsFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateUserDetailsFieldMask.Descriptor instead.
func (UpdateUserDetailsFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_enums_enums_proto_rawDescGZIP(), []int{8}
}

type SubmitApplicationFieldMask int32

const (
	SubmitApplicationFieldMask_SUBMIT_APPLICATION_FIELD_MASK_UNSPECIFIED                 SubmitApplicationFieldMask = 0
	SubmitApplicationFieldMask_SUBMIT_APPLICATION_FIELD_MASK_USER_DETAILS                SubmitApplicationFieldMask = 1
	SubmitApplicationFieldMask_SUBMIT_APPLICATION_FIELD_MASK_KYC_DETAILS                 SubmitApplicationFieldMask = 2
	SubmitApplicationFieldMask_SUBMIT_APPLICATION_FIELD_MASK_MANDATE_DETAILS             SubmitApplicationFieldMask = 3
	SubmitApplicationFieldMask_SUBMIT_APPLICATION_FIELD_MASK_ESIGN_DETAILS               SubmitApplicationFieldMask = 4
	SubmitApplicationFieldMask_SUBMIT_APPLICATION_FIELD_MASK_LOAN_REQUIREMENT_DETAILS    SubmitApplicationFieldMask = 5
	SubmitApplicationFieldMask_SUBMIT_APPLICATION_FIELD_MASK_BRE_PARAMS                  SubmitApplicationFieldMask = 6
	SubmitApplicationFieldMask_SUBMIT_APPLICATION_FIELD_MASK_PRODUCT_SPECIFIC_BRE_PARAMS SubmitApplicationFieldMask = 7
	SubmitApplicationFieldMask_SUBMIT_APPLICATION_FIELD_MASK_CONSENT_DETAILS             SubmitApplicationFieldMask = 8
	SubmitApplicationFieldMask_SUBMIT_APPLICATION_FIELD_MASK_VERIFICATION_DETAILS        SubmitApplicationFieldMask = 9
)

// Enum value maps for SubmitApplicationFieldMask.
var (
	SubmitApplicationFieldMask_name = map[int32]string{
		0: "SUBMIT_APPLICATION_FIELD_MASK_UNSPECIFIED",
		1: "SUBMIT_APPLICATION_FIELD_MASK_USER_DETAILS",
		2: "SUBMIT_APPLICATION_FIELD_MASK_KYC_DETAILS",
		3: "SUBMIT_APPLICATION_FIELD_MASK_MANDATE_DETAILS",
		4: "SUBMIT_APPLICATION_FIELD_MASK_ESIGN_DETAILS",
		5: "SUBMIT_APPLICATION_FIELD_MASK_LOAN_REQUIREMENT_DETAILS",
		6: "SUBMIT_APPLICATION_FIELD_MASK_BRE_PARAMS",
		7: "SUBMIT_APPLICATION_FIELD_MASK_PRODUCT_SPECIFIC_BRE_PARAMS",
		8: "SUBMIT_APPLICATION_FIELD_MASK_CONSENT_DETAILS",
		9: "SUBMIT_APPLICATION_FIELD_MASK_VERIFICATION_DETAILS",
	}
	SubmitApplicationFieldMask_value = map[string]int32{
		"SUBMIT_APPLICATION_FIELD_MASK_UNSPECIFIED":                 0,
		"SUBMIT_APPLICATION_FIELD_MASK_USER_DETAILS":                1,
		"SUBMIT_APPLICATION_FIELD_MASK_KYC_DETAILS":                 2,
		"SUBMIT_APPLICATION_FIELD_MASK_MANDATE_DETAILS":             3,
		"SUBMIT_APPLICATION_FIELD_MASK_ESIGN_DETAILS":               4,
		"SUBMIT_APPLICATION_FIELD_MASK_LOAN_REQUIREMENT_DETAILS":    5,
		"SUBMIT_APPLICATION_FIELD_MASK_BRE_PARAMS":                  6,
		"SUBMIT_APPLICATION_FIELD_MASK_PRODUCT_SPECIFIC_BRE_PARAMS": 7,
		"SUBMIT_APPLICATION_FIELD_MASK_CONSENT_DETAILS":             8,
		"SUBMIT_APPLICATION_FIELD_MASK_VERIFICATION_DETAILS":        9,
	}
)

func (x SubmitApplicationFieldMask) Enum() *SubmitApplicationFieldMask {
	p := new(SubmitApplicationFieldMask)
	*p = x
	return p
}

func (x SubmitApplicationFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubmitApplicationFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[9].Descriptor()
}

func (SubmitApplicationFieldMask) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[9]
}

func (x SubmitApplicationFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubmitApplicationFieldMask.Descriptor instead.
func (SubmitApplicationFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_enums_enums_proto_rawDescGZIP(), []int{9}
}

type KycOrchestrationFlow int32

const (
	KycOrchestrationFlow_KYC_ORCHESTRATION_FLOW_UNSPECIFIED      KycOrchestrationFlow = 0
	KycOrchestrationFlow_KYC_ORCHESTRATION_FLOW_LENDING_FULL_KYC KycOrchestrationFlow = 1
	KycOrchestrationFlow_KYC_ORCHESTRATION_FLOW_OFFLINE_KYC      KycOrchestrationFlow = 2
)

// Enum value maps for KycOrchestrationFlow.
var (
	KycOrchestrationFlow_name = map[int32]string{
		0: "KYC_ORCHESTRATION_FLOW_UNSPECIFIED",
		1: "KYC_ORCHESTRATION_FLOW_LENDING_FULL_KYC",
		2: "KYC_ORCHESTRATION_FLOW_OFFLINE_KYC",
	}
	KycOrchestrationFlow_value = map[string]int32{
		"KYC_ORCHESTRATION_FLOW_UNSPECIFIED":      0,
		"KYC_ORCHESTRATION_FLOW_LENDING_FULL_KYC": 1,
		"KYC_ORCHESTRATION_FLOW_OFFLINE_KYC":      2,
	}
)

func (x KycOrchestrationFlow) Enum() *KycOrchestrationFlow {
	p := new(KycOrchestrationFlow)
	*p = x
	return p
}

func (x KycOrchestrationFlow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KycOrchestrationFlow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[10].Descriptor()
}

func (KycOrchestrationFlow) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes[10]
}

func (x KycOrchestrationFlow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KycOrchestrationFlow.Descriptor instead.
func (KycOrchestrationFlow) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_enums_enums_proto_rawDescGZIP(), []int{10}
}

var File_api_stockguardian_sgapplication_enums_enums_proto protoreflect.FileDescriptor

var file_api_stockguardian_sgapplication_enums_enums_proto_rawDesc = []byte{
	0x0a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2f, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x21, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69,
	0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2a, 0xaf, 0x02, 0x0a, 0x15, 0x4c, 0x6f, 0x61, 0x6e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x27,
	0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x2f, 0x0a, 0x2b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56,
	0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x04, 0x12, 0x22, 0x0a,
	0x1e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x05, 0x12, 0x25, 0x0a, 0x21, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x4e,
	0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x06, 0x2a, 0x47, 0x0a, 0x18, 0x4c, 0x6f, 0x61, 0x6e,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x62, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x27, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50,
	0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x2a, 0xf2, 0x02, 0x0a, 0x18, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2b,
	0x0a, 0x27, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x01,
	0x12, 0x30, 0x0a, 0x2c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x02, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x44, 0x52, 0x41, 0x57, 0x44, 0x4f, 0x57, 0x4e, 0x10, 0x03, 0x12, 0x27, 0x0a, 0x23,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x44,
	0x41, 0x54, 0x45, 0x10, 0x04, 0x12, 0x25, 0x0a, 0x21, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50,
	0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x10, 0x05, 0x12, 0x2c, 0x0a, 0x28,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x42,
	0x55, 0x52, 0x53, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x06, 0x12, 0x2a, 0x0a, 0x26, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x54, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x50, 0x45, 0x4e, 0x4e, 0x59, 0x5f,
	0x44, 0x52, 0x4f, 0x50, 0x10, 0x07, 0x2a, 0x89, 0x03, 0x0a, 0x1a, 0x4c, 0x6f, 0x61, 0x6e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50,
	0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50,
	0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12,
	0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x29,
	0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x04, 0x12, 0x35, 0x0a, 0x31, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x58, 0x50, 0x49,
	0x52, 0x45, 0x44, 0x10, 0x06, 0x12, 0x2b, 0x0a, 0x27, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50,
	0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44,
	0x10, 0x07, 0x2a, 0xb6, 0x04, 0x0a, 0x1d, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x75, 0x62, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x31, 0x0a, 0x2d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50,
	0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x34, 0x0a, 0x30, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x33, 0x0a,
	0x2f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44,
	0x10, 0x02, 0x12, 0x49, 0x0a, 0x45, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x4c, 0x4f,
	0x43, 0x4b, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x41, 0x4e, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f,
	0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x12, 0x40, 0x0a,
	0x3c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x44, 0x52, 0x41, 0x57, 0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12,
	0x3b, 0x0a, 0x37, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59,
	0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x05, 0x12, 0x34, 0x0a, 0x30,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45,
	0x10, 0x06, 0x12, 0x39, 0x0a, 0x35, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x47, 0x45, 0x4e,
	0x44, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x07, 0x12, 0x3c, 0x0a,
	0x38, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x42, 0x55, 0x52, 0x53, 0x41, 0x4c, 0x5f, 0x4d, 0x41, 0x4e,
	0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x59, 0x10, 0x08, 0x2a, 0xf1, 0x01, 0x0a, 0x18,
	0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x2b, 0x0a, 0x27, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2b, 0x0a, 0x27, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50,
	0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x49, 0x44,
	0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x02, 0x12, 0x26, 0x0a, 0x22, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x10, 0x03, 0x12, 0x2a, 0x0a, 0x26, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x04, 0x2a,
	0xc8, 0x02, 0x0a, 0x1d, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73,
	0x6b, 0x12, 0x31, 0x0a, 0x2d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x31, 0x0a, 0x2d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50,
	0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x10, 0x02, 0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x10, 0x03, 0x12, 0x30, 0x0a, 0x2c, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50,
	0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x10, 0x04, 0x12, 0x32, 0x0a, 0x2e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x05, 0x2a, 0xa1, 0x01, 0x0a, 0x0b, 0x43,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4f,
	0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4e, 0x53,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x44, 0x4f,
	0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x01, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x4f, 0x4e, 0x53,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f,
	0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x48, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x55, 0x4c, 0x4c,
	0x10, 0x02, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x55, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x50, 0x52, 0x4f, 0x4d, 0x4f, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x03, 0x2a, 0xd2,
	0x02, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x2e, 0x0a,
	0x2a, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2a, 0x0a,
	0x26, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x35, 0x0a, 0x31, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x4d, 0x50, 0x4c,
	0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x02,
	0x12, 0x37, 0x0a, 0x33, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x03, 0x12, 0x33, 0x0a, 0x2f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x45, 0x52, 0x53,
	0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x04, 0x12, 0x33,
	0x0a, 0x2f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x10, 0x05, 0x2a, 0xa2, 0x04, 0x0a, 0x1a, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x12, 0x2d, 0x0a, 0x29, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x5f, 0x41, 0x50, 0x50,
	0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x2e, 0x0a, 0x2a, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x4c,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0x01, 0x12, 0x2d, 0x0a, 0x29, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x4c,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x02,
	0x12, 0x31, 0x0a, 0x2d, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x10, 0x03, 0x12, 0x2f, 0x0a, 0x2b, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x5f, 0x41, 0x50,
	0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49,
	0x4c, 0x53, 0x10, 0x04, 0x12, 0x3a, 0x0a, 0x36, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x5f, 0x41,
	0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49,
	0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x05,
	0x12, 0x2c, 0x0a, 0x28, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x42, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x53, 0x10, 0x06, 0x12, 0x3d,
	0x0a, 0x39, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x43,
	0x5f, 0x42, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x53, 0x10, 0x07, 0x12, 0x31, 0x0a,
	0x2d, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43,
	0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x08,
	0x12, 0x36, 0x0a, 0x32, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x09, 0x2a, 0x93, 0x01, 0x0a, 0x14, 0x4b, 0x79, 0x63,
	0x4f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6c, 0x6f,
	0x77, 0x12, 0x26, 0x0a, 0x22, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x52, 0x43, 0x48, 0x45, 0x53, 0x54,
	0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2b, 0x0a, 0x27, 0x4b, 0x59, 0x43,
	0x5f, 0x4f, 0x52, 0x43, 0x48, 0x45, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x4c, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x55, 0x4c, 0x4c,
	0x5f, 0x4b, 0x59, 0x43, 0x10, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x52,
	0x43, 0x48, 0x45, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x02, 0x42, 0x82,
	0x01, 0x0a, 0x3f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x72, 0x69, 0x6e, 0x67, 0x6f, 0x74, 0x74, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73,
	0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x72, 0x69, 0x6e, 0x67, 0x6f, 0x74, 0x74, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2f,
	0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_stockguardian_sgapplication_enums_enums_proto_rawDescOnce sync.Once
	file_api_stockguardian_sgapplication_enums_enums_proto_rawDescData = file_api_stockguardian_sgapplication_enums_enums_proto_rawDesc
)

func file_api_stockguardian_sgapplication_enums_enums_proto_rawDescGZIP() []byte {
	file_api_stockguardian_sgapplication_enums_enums_proto_rawDescOnce.Do(func() {
		file_api_stockguardian_sgapplication_enums_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_stockguardian_sgapplication_enums_enums_proto_rawDescData)
	})
	return file_api_stockguardian_sgapplication_enums_enums_proto_rawDescData
}

var file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 11)
var file_api_stockguardian_sgapplication_enums_enums_proto_goTypes = []interface{}{
	(LoanApplicationStatus)(0),         // 0: stockguardian.sgapplication.enums.LoanApplicationStatus
	(LoanApplicationSubStatus)(0),      // 1: stockguardian.sgapplication.enums.LoanApplicationSubStatus
	(LoanApplicationStageName)(0),      // 2: stockguardian.sgapplication.enums.LoanApplicationStageName
	(LoanApplicationStageStatus)(0),    // 3: stockguardian.sgapplication.enums.LoanApplicationStageStatus
	(LoanApplicationStageSubStatus)(0), // 4: stockguardian.sgapplication.enums.LoanApplicationStageSubStatus
	(LoanApplicationFieldMask)(0),      // 5: stockguardian.sgapplication.enums.LoanApplicationFieldMask
	(LoanApplicationStageFieldMask)(0), // 6: stockguardian.sgapplication.enums.LoanApplicationStageFieldMask
	(ConsentType)(0),                   // 7: stockguardian.sgapplication.enums.ConsentType
	(UpdateUserDetailsFieldMask)(0),    // 8: stockguardian.sgapplication.enums.UpdateUserDetailsFieldMask
	(SubmitApplicationFieldMask)(0),    // 9: stockguardian.sgapplication.enums.SubmitApplicationFieldMask
	(KycOrchestrationFlow)(0),          // 10: stockguardian.sgapplication.enums.KycOrchestrationFlow
}
var file_api_stockguardian_sgapplication_enums_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_stockguardian_sgapplication_enums_enums_proto_init() }
func file_api_stockguardian_sgapplication_enums_enums_proto_init() {
	if File_api_stockguardian_sgapplication_enums_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_stockguardian_sgapplication_enums_enums_proto_rawDesc,
			NumEnums:      11,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_stockguardian_sgapplication_enums_enums_proto_goTypes,
		DependencyIndexes: file_api_stockguardian_sgapplication_enums_enums_proto_depIdxs,
		EnumInfos:         file_api_stockguardian_sgapplication_enums_enums_proto_enumTypes,
	}.Build()
	File_api_stockguardian_sgapplication_enums_enums_proto = out.File
	file_api_stockguardian_sgapplication_enums_enums_proto_rawDesc = nil
	file_api_stockguardian_sgapplication_enums_enums_proto_goTypes = nil
	file_api_stockguardian_sgapplication_enums_enums_proto_depIdxs = nil
}
