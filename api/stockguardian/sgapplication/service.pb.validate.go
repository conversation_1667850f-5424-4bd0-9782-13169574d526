// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/stockguardian/sgapplication/service.proto

package sgapplication

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gringott/api/stockguardian/sgapplication/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.SubmitApplicationFieldMask(0)
)

// Validate checks the field values on GetLoanApplicationsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanApplicationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanApplicationsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanApplicationsRequestMultiError, or nil if none found.
func (m *GetLoanApplicationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanApplicationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicantId

	// no validation rules for ClientId

	// no validation rules for ProductId

	// no validation rules for LoanApplicationId

	if len(errors) > 0 {
		return GetLoanApplicationsRequestMultiError(errors)
	}

	return nil
}

// GetLoanApplicationsRequestMultiError is an error wrapping multiple
// validation errors returned by GetLoanApplicationsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetLoanApplicationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanApplicationsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanApplicationsRequestMultiError) AllErrors() []error { return m }

// GetLoanApplicationsRequestValidationError is the validation error returned
// by GetLoanApplicationsRequest.Validate if the designated constraints aren't met.
type GetLoanApplicationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanApplicationsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanApplicationsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanApplicationsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanApplicationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanApplicationsRequestValidationError) ErrorName() string {
	return "GetLoanApplicationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanApplicationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanApplicationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanApplicationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanApplicationsRequestValidationError{}

// Validate checks the field values on GetLoanApplicationsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanApplicationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanApplicationsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanApplicationsResponseMultiError, or nil if none found.
func (m *GetLoanApplicationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanApplicationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanApplicationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanApplicationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanApplicationsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLoanApplications() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanApplicationsResponseValidationError{
						field:  fmt.Sprintf("LoanApplications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanApplicationsResponseValidationError{
						field:  fmt.Sprintf("LoanApplications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanApplicationsResponseValidationError{
					field:  fmt.Sprintf("LoanApplications[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetLoanApplicationsResponseMultiError(errors)
	}

	return nil
}

// GetLoanApplicationsResponseMultiError is an error wrapping multiple
// validation errors returned by GetLoanApplicationsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetLoanApplicationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanApplicationsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanApplicationsResponseMultiError) AllErrors() []error { return m }

// GetLoanApplicationsResponseValidationError is the validation error returned
// by GetLoanApplicationsResponse.Validate if the designated constraints
// aren't met.
type GetLoanApplicationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanApplicationsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanApplicationsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanApplicationsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanApplicationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanApplicationsResponseValidationError) ErrorName() string {
	return "GetLoanApplicationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanApplicationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanApplicationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanApplicationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanApplicationsResponseValidationError{}

// Validate checks the field values on GetKycApplicationIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKycApplicationIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKycApplicationIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKycApplicationIdRequestMultiError, or nil if none found.
func (m *GetKycApplicationIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKycApplicationIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanApplicationId

	if len(errors) > 0 {
		return GetKycApplicationIdRequestMultiError(errors)
	}

	return nil
}

// GetKycApplicationIdRequestMultiError is an error wrapping multiple
// validation errors returned by GetKycApplicationIdRequest.ValidateAll() if
// the designated constraints aren't met.
type GetKycApplicationIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKycApplicationIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKycApplicationIdRequestMultiError) AllErrors() []error { return m }

// GetKycApplicationIdRequestValidationError is the validation error returned
// by GetKycApplicationIdRequest.Validate if the designated constraints aren't met.
type GetKycApplicationIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKycApplicationIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKycApplicationIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKycApplicationIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKycApplicationIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKycApplicationIdRequestValidationError) ErrorName() string {
	return "GetKycApplicationIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetKycApplicationIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKycApplicationIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKycApplicationIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKycApplicationIdRequestValidationError{}

// Validate checks the field values on GetKycApplicationIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKycApplicationIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKycApplicationIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKycApplicationIdResponseMultiError, or nil if none found.
func (m *GetKycApplicationIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKycApplicationIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKycApplicationIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKycApplicationIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKycApplicationIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KycApplicationId

	if len(errors) > 0 {
		return GetKycApplicationIdResponseMultiError(errors)
	}

	return nil
}

// GetKycApplicationIdResponseMultiError is an error wrapping multiple
// validation errors returned by GetKycApplicationIdResponse.ValidateAll() if
// the designated constraints aren't met.
type GetKycApplicationIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKycApplicationIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKycApplicationIdResponseMultiError) AllErrors() []error { return m }

// GetKycApplicationIdResponseValidationError is the validation error returned
// by GetKycApplicationIdResponse.Validate if the designated constraints
// aren't met.
type GetKycApplicationIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKycApplicationIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKycApplicationIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKycApplicationIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKycApplicationIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKycApplicationIdResponseValidationError) ErrorName() string {
	return "GetKycApplicationIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetKycApplicationIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKycApplicationIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKycApplicationIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKycApplicationIdResponseValidationError{}

// Validate checks the field values on InitDisbursalRetryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitDisbursalRetryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitDisbursalRetryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitDisbursalRetryRequestMultiError, or nil if none found.
func (m *InitDisbursalRetryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitDisbursalRetryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return InitDisbursalRetryRequestMultiError(errors)
	}

	return nil
}

// InitDisbursalRetryRequestMultiError is an error wrapping multiple validation
// errors returned by InitDisbursalRetryRequest.ValidateAll() if the
// designated constraints aren't met.
type InitDisbursalRetryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitDisbursalRetryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitDisbursalRetryRequestMultiError) AllErrors() []error { return m }

// InitDisbursalRetryRequestValidationError is the validation error returned by
// InitDisbursalRetryRequest.Validate if the designated constraints aren't met.
type InitDisbursalRetryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitDisbursalRetryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitDisbursalRetryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitDisbursalRetryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitDisbursalRetryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitDisbursalRetryRequestValidationError) ErrorName() string {
	return "InitDisbursalRetryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitDisbursalRetryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitDisbursalRetryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitDisbursalRetryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitDisbursalRetryRequestValidationError{}

// Validate checks the field values on InitDisbursalRetryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitDisbursalRetryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitDisbursalRetryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitDisbursalRetryResponseMultiError, or nil if none found.
func (m *InitDisbursalRetryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitDisbursalRetryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitDisbursalRetryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitDisbursalRetryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitDisbursalRetryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitDisbursalRetryResponseMultiError(errors)
	}

	return nil
}

// InitDisbursalRetryResponseMultiError is an error wrapping multiple
// validation errors returned by InitDisbursalRetryResponse.ValidateAll() if
// the designated constraints aren't met.
type InitDisbursalRetryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitDisbursalRetryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitDisbursalRetryResponseMultiError) AllErrors() []error { return m }

// InitDisbursalRetryResponseValidationError is the validation error returned
// by InitDisbursalRetryResponse.Validate if the designated constraints aren't met.
type InitDisbursalRetryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitDisbursalRetryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitDisbursalRetryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitDisbursalRetryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitDisbursalRetryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitDisbursalRetryResponseValidationError) ErrorName() string {
	return "InitDisbursalRetryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitDisbursalRetryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitDisbursalRetryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitDisbursalRetryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitDisbursalRetryResponseValidationError{}

// Validate checks the field values on StartApplicationRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StartApplicationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartApplicationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StartApplicationRequestMultiError, or nil if none found.
func (m *StartApplicationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StartApplicationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientId

	// no validation rules for ProductId

	// no validation rules for ApplicantId

	if len(errors) > 0 {
		return StartApplicationRequestMultiError(errors)
	}

	return nil
}

// StartApplicationRequestMultiError is an error wrapping multiple validation
// errors returned by StartApplicationRequest.ValidateAll() if the designated
// constraints aren't met.
type StartApplicationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartApplicationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartApplicationRequestMultiError) AllErrors() []error { return m }

// StartApplicationRequestValidationError is the validation error returned by
// StartApplicationRequest.Validate if the designated constraints aren't met.
type StartApplicationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartApplicationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartApplicationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartApplicationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartApplicationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartApplicationRequestValidationError) ErrorName() string {
	return "StartApplicationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StartApplicationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartApplicationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartApplicationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartApplicationRequestValidationError{}

// Validate checks the field values on StartApplicationResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StartApplicationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartApplicationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StartApplicationResponseMultiError, or nil if none found.
func (m *StartApplicationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StartApplicationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartApplicationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartApplicationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartApplicationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicationId

	if len(errors) > 0 {
		return StartApplicationResponseMultiError(errors)
	}

	return nil
}

// StartApplicationResponseMultiError is an error wrapping multiple validation
// errors returned by StartApplicationResponse.ValidateAll() if the designated
// constraints aren't met.
type StartApplicationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartApplicationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartApplicationResponseMultiError) AllErrors() []error { return m }

// StartApplicationResponseValidationError is the validation error returned by
// StartApplicationResponse.Validate if the designated constraints aren't met.
type StartApplicationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartApplicationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartApplicationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartApplicationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartApplicationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartApplicationResponseValidationError) ErrorName() string {
	return "StartApplicationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StartApplicationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartApplicationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartApplicationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartApplicationResponseValidationError{}

// Validate checks the field values on SubmitApplicationRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitApplicationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitApplicationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubmitApplicationRequestMultiError, or nil if none found.
func (m *SubmitApplicationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitApplicationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	if all {
		switch v := interface{}(m.GetPersonalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "PersonalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "PersonalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPersonalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitApplicationRequestValidationError{
				field:  "PersonalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddressDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "AddressDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "AddressDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddressDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitApplicationRequestValidationError{
				field:  "AddressDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmploymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "EmploymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "EmploymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmploymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitApplicationRequestValidationError{
				field:  "EmploymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLocationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "LocationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "LocationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLocationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitApplicationRequestValidationError{
				field:  "LocationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConsentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "ConsentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "ConsentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitApplicationRequestValidationError{
				field:  "ConsentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetKycDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "KycDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "KycDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKycDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitApplicationRequestValidationError{
				field:  "KycDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRecurringPaymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "RecurringPaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "RecurringPaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurringPaymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitApplicationRequestValidationError{
				field:  "RecurringPaymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEsignDocumentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "EsignDocumentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "EsignDocumentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEsignDocumentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitApplicationRequestValidationError{
				field:  "EsignDocumentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanOfferDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "LoanOfferDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitApplicationRequestValidationError{
					field:  "LoanOfferDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanOfferDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitApplicationRequestValidationError{
				field:  "LoanOfferDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubmitApplicationRequestMultiError(errors)
	}

	return nil
}

// SubmitApplicationRequestMultiError is an error wrapping multiple validation
// errors returned by SubmitApplicationRequest.ValidateAll() if the designated
// constraints aren't met.
type SubmitApplicationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitApplicationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitApplicationRequestMultiError) AllErrors() []error { return m }

// SubmitApplicationRequestValidationError is the validation error returned by
// SubmitApplicationRequest.Validate if the designated constraints aren't met.
type SubmitApplicationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitApplicationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitApplicationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitApplicationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitApplicationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitApplicationRequestValidationError) ErrorName() string {
	return "SubmitApplicationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitApplicationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitApplicationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitApplicationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitApplicationRequestValidationError{}

// Validate checks the field values on SubmitApplicationResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitApplicationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitApplicationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubmitApplicationResponseMultiError, or nil if none found.
func (m *SubmitApplicationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitApplicationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitApplicationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitApplicationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitApplicationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubmitApplicationResponseMultiError(errors)
	}

	return nil
}

// SubmitApplicationResponseMultiError is an error wrapping multiple validation
// errors returned by SubmitApplicationResponse.ValidateAll() if the
// designated constraints aren't met.
type SubmitApplicationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitApplicationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitApplicationResponseMultiError) AllErrors() []error { return m }

// SubmitApplicationResponseValidationError is the validation error returned by
// SubmitApplicationResponse.Validate if the designated constraints aren't met.
type SubmitApplicationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitApplicationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitApplicationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitApplicationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitApplicationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitApplicationResponseValidationError) ErrorName() string {
	return "SubmitApplicationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitApplicationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitApplicationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitApplicationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitApplicationResponseValidationError{}

// Validate checks the field values on CancelApplicationRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelApplicationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelApplicationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelApplicationRequestMultiError, or nil if none found.
func (m *CancelApplicationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelApplicationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	if len(errors) > 0 {
		return CancelApplicationRequestMultiError(errors)
	}

	return nil
}

// CancelApplicationRequestMultiError is an error wrapping multiple validation
// errors returned by CancelApplicationRequest.ValidateAll() if the designated
// constraints aren't met.
type CancelApplicationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelApplicationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelApplicationRequestMultiError) AllErrors() []error { return m }

// CancelApplicationRequestValidationError is the validation error returned by
// CancelApplicationRequest.Validate if the designated constraints aren't met.
type CancelApplicationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelApplicationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelApplicationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelApplicationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelApplicationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelApplicationRequestValidationError) ErrorName() string {
	return "CancelApplicationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CancelApplicationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelApplicationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelApplicationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelApplicationRequestValidationError{}

// Validate checks the field values on CancelApplicationResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelApplicationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelApplicationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelApplicationResponseMultiError, or nil if none found.
func (m *CancelApplicationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelApplicationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CancelApplicationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CancelApplicationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CancelApplicationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CancelApplicationResponseMultiError(errors)
	}

	return nil
}

// CancelApplicationResponseMultiError is an error wrapping multiple validation
// errors returned by CancelApplicationResponse.ValidateAll() if the
// designated constraints aren't met.
type CancelApplicationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelApplicationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelApplicationResponseMultiError) AllErrors() []error { return m }

// CancelApplicationResponseValidationError is the validation error returned by
// CancelApplicationResponse.Validate if the designated constraints aren't met.
type CancelApplicationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelApplicationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelApplicationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelApplicationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelApplicationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelApplicationResponseValidationError) ErrorName() string {
	return "CancelApplicationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CancelApplicationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelApplicationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelApplicationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelApplicationResponseValidationError{}

// Validate checks the field values on GetApplicationStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetApplicationStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetApplicationStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetApplicationStatusRequestMultiError, or nil if none found.
func (m *GetApplicationStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetApplicationStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	if len(errors) > 0 {
		return GetApplicationStatusRequestMultiError(errors)
	}

	return nil
}

// GetApplicationStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetApplicationStatusRequest.ValidateAll() if
// the designated constraints aren't met.
type GetApplicationStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetApplicationStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetApplicationStatusRequestMultiError) AllErrors() []error { return m }

// GetApplicationStatusRequestValidationError is the validation error returned
// by GetApplicationStatusRequest.Validate if the designated constraints
// aren't met.
type GetApplicationStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetApplicationStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetApplicationStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetApplicationStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetApplicationStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetApplicationStatusRequestValidationError) ErrorName() string {
	return "GetApplicationStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetApplicationStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetApplicationStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetApplicationStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetApplicationStatusRequestValidationError{}

// Validate checks the field values on GetApplicationStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetApplicationStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetApplicationStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetApplicationStatusResponseMultiError, or nil if none found.
func (m *GetApplicationStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetApplicationStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetApplicationStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetApplicationStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetApplicationStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetStageStatuses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetApplicationStatusResponseValidationError{
						field:  fmt.Sprintf("StageStatuses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetApplicationStatusResponseValidationError{
						field:  fmt.Sprintf("StageStatuses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetApplicationStatusResponseValidationError{
					field:  fmt.Sprintf("StageStatuses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for LoanApplicationStatus

	if len(errors) > 0 {
		return GetApplicationStatusResponseMultiError(errors)
	}

	return nil
}

// GetApplicationStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetApplicationStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type GetApplicationStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetApplicationStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetApplicationStatusResponseMultiError) AllErrors() []error { return m }

// GetApplicationStatusResponseValidationError is the validation error returned
// by GetApplicationStatusResponse.Validate if the designated constraints
// aren't met.
type GetApplicationStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetApplicationStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetApplicationStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetApplicationStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetApplicationStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetApplicationStatusResponseValidationError) ErrorName() string {
	return "GetApplicationStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetApplicationStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetApplicationStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetApplicationStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetApplicationStatusResponseValidationError{}

// Validate checks the field values on StageStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StageStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StageStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StageStatusMultiError, or
// nil if none found.
func (m *StageStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *StageStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StageName

	// no validation rules for Status

	if len(errors) > 0 {
		return StageStatusMultiError(errors)
	}

	return nil
}

// StageStatusMultiError is an error wrapping multiple validation errors
// returned by StageStatus.ValidateAll() if the designated constraints aren't met.
type StageStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StageStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StageStatusMultiError) AllErrors() []error { return m }

// StageStatusValidationError is the validation error returned by
// StageStatus.Validate if the designated constraints aren't met.
type StageStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StageStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StageStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StageStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StageStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StageStatusValidationError) ErrorName() string { return "StageStatusValidationError" }

// Error satisfies the builtin error interface
func (e StageStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStageStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StageStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StageStatusValidationError{}

// Validate checks the field values on GetLoanOfferRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanOfferRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanOfferRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanOfferRequestMultiError, or nil if none found.
func (m *GetLoanOfferRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanOfferRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	// no validation rules for IsOpenMarketUser

	if all {
		switch v := interface{}(m.GetBreParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanOfferRequestValidationError{
					field:  "BreParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanOfferRequestValidationError{
					field:  "BreParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBreParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanOfferRequestValidationError{
				field:  "BreParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanOfferRequestMultiError(errors)
	}

	return nil
}

// GetLoanOfferRequestMultiError is an error wrapping multiple validation
// errors returned by GetLoanOfferRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLoanOfferRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanOfferRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanOfferRequestMultiError) AllErrors() []error { return m }

// GetLoanOfferRequestValidationError is the validation error returned by
// GetLoanOfferRequest.Validate if the designated constraints aren't met.
type GetLoanOfferRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanOfferRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanOfferRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanOfferRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanOfferRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanOfferRequestValidationError) ErrorName() string {
	return "GetLoanOfferRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanOfferRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanOfferRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanOfferRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanOfferRequestValidationError{}

// Validate checks the field values on GetLoanOfferResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanOfferResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanOfferResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanOfferResponseMultiError, or nil if none found.
func (m *GetLoanOfferResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanOfferResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanOfferResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanOfferResponseValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanOfferResponseValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanOfferResponseValidationError{
				field:  "Details",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanOfferResponseMultiError(errors)
	}

	return nil
}

// GetLoanOfferResponseMultiError is an error wrapping multiple validation
// errors returned by GetLoanOfferResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLoanOfferResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanOfferResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanOfferResponseMultiError) AllErrors() []error { return m }

// GetLoanOfferResponseValidationError is the validation error returned by
// GetLoanOfferResponse.Validate if the designated constraints aren't met.
type GetLoanOfferResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanOfferResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanOfferResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanOfferResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanOfferResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanOfferResponseValidationError) ErrorName() string {
	return "GetLoanOfferResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanOfferResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanOfferResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanOfferResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanOfferResponseValidationError{}

// Validate checks the field values on InitiateKycRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateKycRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateKycRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateKycRequestMultiError, or nil if none found.
func (m *InitiateKycRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateKycRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	if len(errors) > 0 {
		return InitiateKycRequestMultiError(errors)
	}

	return nil
}

// InitiateKycRequestMultiError is an error wrapping multiple validation errors
// returned by InitiateKycRequest.ValidateAll() if the designated constraints
// aren't met.
type InitiateKycRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateKycRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateKycRequestMultiError) AllErrors() []error { return m }

// InitiateKycRequestValidationError is the validation error returned by
// InitiateKycRequest.Validate if the designated constraints aren't met.
type InitiateKycRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateKycRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateKycRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateKycRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateKycRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateKycRequestValidationError) ErrorName() string {
	return "InitiateKycRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateKycRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateKycRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateKycRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateKycRequestValidationError{}

// Validate checks the field values on InitiateKycResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateKycResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateKycResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateKycResponseMultiError, or nil if none found.
func (m *InitiateKycResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateKycResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateKycResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateKycResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateKycResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicantId

	// no validation rules for ClientRefId

	// no validation rules for KycApplicationId

	if len(errors) > 0 {
		return InitiateKycResponseMultiError(errors)
	}

	return nil
}

// InitiateKycResponseMultiError is an error wrapping multiple validation
// errors returned by InitiateKycResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateKycResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateKycResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateKycResponseMultiError) AllErrors() []error { return m }

// InitiateKycResponseValidationError is the validation error returned by
// InitiateKycResponse.Validate if the designated constraints aren't met.
type InitiateKycResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateKycResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateKycResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateKycResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateKycResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateKycResponseValidationError) ErrorName() string {
	return "InitiateKycResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateKycResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateKycResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateKycResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateKycResponseValidationError{}

// Validate checks the field values on UpdateApplicationDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateApplicationDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateApplicationDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateApplicationDetailsRequestMultiError, or nil if none found.
func (m *UpdateApplicationDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateApplicationDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	if all {
		switch v := interface{}(m.GetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateApplicationDetailsRequestValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateApplicationDetailsRequestValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateApplicationDetailsRequestValidationError{
				field:  "Details",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateApplicationDetailsRequestMultiError(errors)
	}

	return nil
}

// UpdateApplicationDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateApplicationDetailsRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateApplicationDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateApplicationDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateApplicationDetailsRequestMultiError) AllErrors() []error { return m }

// UpdateApplicationDetailsRequestValidationError is the validation error
// returned by UpdateApplicationDetailsRequest.Validate if the designated
// constraints aren't met.
type UpdateApplicationDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateApplicationDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateApplicationDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateApplicationDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateApplicationDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateApplicationDetailsRequestValidationError) ErrorName() string {
	return "UpdateApplicationDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateApplicationDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateApplicationDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateApplicationDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateApplicationDetailsRequestValidationError{}

// Validate checks the field values on UpdateApplicationDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateApplicationDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateApplicationDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateApplicationDetailsResponseMultiError, or nil if none found.
func (m *UpdateApplicationDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateApplicationDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateApplicationDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateApplicationDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateApplicationDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateApplicationDetailsResponseMultiError(errors)
	}

	return nil
}

// UpdateApplicationDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateApplicationDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateApplicationDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateApplicationDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateApplicationDetailsResponseMultiError) AllErrors() []error { return m }

// UpdateApplicationDetailsResponseValidationError is the validation error
// returned by UpdateApplicationDetailsResponse.Validate if the designated
// constraints aren't met.
type UpdateApplicationDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateApplicationDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateApplicationDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateApplicationDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateApplicationDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateApplicationDetailsResponseValidationError) ErrorName() string {
	return "UpdateApplicationDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateApplicationDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateApplicationDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateApplicationDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateApplicationDetailsResponseValidationError{}

// Validate checks the field values on InitDrawDownRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitDrawDownRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitDrawDownRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitDrawDownRequestMultiError, or nil if none found.
func (m *InitDrawDownRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitDrawDownRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	if len(errors) > 0 {
		return InitDrawDownRequestMultiError(errors)
	}

	return nil
}

// InitDrawDownRequestMultiError is an error wrapping multiple validation
// errors returned by InitDrawDownRequest.ValidateAll() if the designated
// constraints aren't met.
type InitDrawDownRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitDrawDownRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitDrawDownRequestMultiError) AllErrors() []error { return m }

// InitDrawDownRequestValidationError is the validation error returned by
// InitDrawDownRequest.Validate if the designated constraints aren't met.
type InitDrawDownRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitDrawDownRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitDrawDownRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitDrawDownRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitDrawDownRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitDrawDownRequestValidationError) ErrorName() string {
	return "InitDrawDownRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitDrawDownRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitDrawDownRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitDrawDownRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitDrawDownRequestValidationError{}

// Validate checks the field values on InitDrawDownResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitDrawDownResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitDrawDownResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitDrawDownResponseMultiError, or nil if none found.
func (m *InitDrawDownResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitDrawDownResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitDrawDownResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitDrawDownResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitDrawDownResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitDrawDownResponseMultiError(errors)
	}

	return nil
}

// InitDrawDownResponseMultiError is an error wrapping multiple validation
// errors returned by InitDrawDownResponse.ValidateAll() if the designated
// constraints aren't met.
type InitDrawDownResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitDrawDownResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitDrawDownResponseMultiError) AllErrors() []error { return m }

// InitDrawDownResponseValidationError is the validation error returned by
// InitDrawDownResponse.Validate if the designated constraints aren't met.
type InitDrawDownResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitDrawDownResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitDrawDownResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitDrawDownResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitDrawDownResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitDrawDownResponseValidationError) ErrorName() string {
	return "InitDrawDownResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitDrawDownResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitDrawDownResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitDrawDownResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitDrawDownResponseValidationError{}

// Validate checks the field values on InitMandateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitMandateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitMandateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitMandateRequestMultiError, or nil if none found.
func (m *InitMandateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitMandateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	// no validation rules for RedirectLink

	if len(errors) > 0 {
		return InitMandateRequestMultiError(errors)
	}

	return nil
}

// InitMandateRequestMultiError is an error wrapping multiple validation errors
// returned by InitMandateRequest.ValidateAll() if the designated constraints
// aren't met.
type InitMandateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitMandateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitMandateRequestMultiError) AllErrors() []error { return m }

// InitMandateRequestValidationError is the validation error returned by
// InitMandateRequest.Validate if the designated constraints aren't met.
type InitMandateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitMandateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitMandateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitMandateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitMandateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitMandateRequestValidationError) ErrorName() string {
	return "InitMandateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitMandateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitMandateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitMandateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitMandateRequestValidationError{}

// Validate checks the field values on InitMandateResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitMandateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitMandateResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitMandateResponseMultiError, or nil if none found.
func (m *InitMandateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitMandateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitMandateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitMandateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitMandateResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrchId

	if len(errors) > 0 {
		return InitMandateResponseMultiError(errors)
	}

	return nil
}

// InitMandateResponseMultiError is an error wrapping multiple validation
// errors returned by InitMandateResponse.ValidateAll() if the designated
// constraints aren't met.
type InitMandateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitMandateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitMandateResponseMultiError) AllErrors() []error { return m }

// InitMandateResponseValidationError is the validation error returned by
// InitMandateResponse.Validate if the designated constraints aren't met.
type InitMandateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitMandateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitMandateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitMandateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitMandateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitMandateResponseValidationError) ErrorName() string {
	return "InitMandateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitMandateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitMandateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitMandateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitMandateResponseValidationError{}

// Validate checks the field values on InitEsignRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InitEsignRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitEsignRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitEsignRequestMultiError, or nil if none found.
func (m *InitEsignRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitEsignRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	if len(errors) > 0 {
		return InitEsignRequestMultiError(errors)
	}

	return nil
}

// InitEsignRequestMultiError is an error wrapping multiple validation errors
// returned by InitEsignRequest.ValidateAll() if the designated constraints
// aren't met.
type InitEsignRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitEsignRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitEsignRequestMultiError) AllErrors() []error { return m }

// InitEsignRequestValidationError is the validation error returned by
// InitEsignRequest.Validate if the designated constraints aren't met.
type InitEsignRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitEsignRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitEsignRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitEsignRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitEsignRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitEsignRequestValidationError) ErrorName() string { return "InitEsignRequestValidationError" }

// Error satisfies the builtin error interface
func (e InitEsignRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitEsignRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitEsignRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitEsignRequestValidationError{}

// Validate checks the field values on InitEsignResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InitEsignResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitEsignResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitEsignResponseMultiError, or nil if none found.
func (m *InitEsignResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitEsignResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitEsignResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitEsignResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitEsignResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrchId

	if len(errors) > 0 {
		return InitEsignResponseMultiError(errors)
	}

	return nil
}

// InitEsignResponseMultiError is an error wrapping multiple validation errors
// returned by InitEsignResponse.ValidateAll() if the designated constraints
// aren't met.
type InitEsignResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitEsignResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitEsignResponseMultiError) AllErrors() []error { return m }

// InitEsignResponseValidationError is the validation error returned by
// InitEsignResponse.Validate if the designated constraints aren't met.
type InitEsignResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitEsignResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitEsignResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitEsignResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitEsignResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitEsignResponseValidationError) ErrorName() string {
	return "InitEsignResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitEsignResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitEsignResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitEsignResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitEsignResponseValidationError{}

// Validate checks the field values on InitDisbursementRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitDisbursementRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitDisbursementRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitDisbursementRequestMultiError, or nil if none found.
func (m *InitDisbursementRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitDisbursementRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	// no validation rules for InternalApplicationId

	if all {
		switch v := interface{}(m.GetDownPaymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitDisbursementRequestValidationError{
					field:  "DownPaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitDisbursementRequestValidationError{
					field:  "DownPaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDownPaymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitDisbursementRequestValidationError{
				field:  "DownPaymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetServiceProviderDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitDisbursementRequestValidationError{
					field:  "ServiceProviderDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitDisbursementRequestValidationError{
					field:  "ServiceProviderDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetServiceProviderDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitDisbursementRequestValidationError{
				field:  "ServiceProviderDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitDisbursementRequestMultiError(errors)
	}

	return nil
}

// InitDisbursementRequestMultiError is an error wrapping multiple validation
// errors returned by InitDisbursementRequest.ValidateAll() if the designated
// constraints aren't met.
type InitDisbursementRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitDisbursementRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitDisbursementRequestMultiError) AllErrors() []error { return m }

// InitDisbursementRequestValidationError is the validation error returned by
// InitDisbursementRequest.Validate if the designated constraints aren't met.
type InitDisbursementRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitDisbursementRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitDisbursementRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitDisbursementRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitDisbursementRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitDisbursementRequestValidationError) ErrorName() string {
	return "InitDisbursementRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitDisbursementRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitDisbursementRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitDisbursementRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitDisbursementRequestValidationError{}

// Validate checks the field values on InitDisbursementResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitDisbursementResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitDisbursementResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitDisbursementResponseMultiError, or nil if none found.
func (m *InitDisbursementResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitDisbursementResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitDisbursementResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitDisbursementResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitDisbursementResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitDisbursementResponseMultiError(errors)
	}

	return nil
}

// InitDisbursementResponseMultiError is an error wrapping multiple validation
// errors returned by InitDisbursementResponse.ValidateAll() if the designated
// constraints aren't met.
type InitDisbursementResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitDisbursementResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitDisbursementResponseMultiError) AllErrors() []error { return m }

// InitDisbursementResponseValidationError is the validation error returned by
// InitDisbursementResponse.Validate if the designated constraints aren't met.
type InitDisbursementResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitDisbursementResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitDisbursementResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitDisbursementResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitDisbursementResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitDisbursementResponseValidationError) ErrorName() string {
	return "InitDisbursementResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitDisbursementResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitDisbursementResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitDisbursementResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitDisbursementResponseValidationError{}

// Validate checks the field values on UpdateUserDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserDetailsRequestMultiError, or nil if none found.
func (m *UpdateUserDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	// no validation rules for UpdateFieldMask

	switch v := m.Details.(type) {
	case *UpdateUserDetailsRequest_AddressDetails:
		if v == nil {
			err := UpdateUserDetailsRequestValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAddressDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateUserDetailsRequestValidationError{
						field:  "AddressDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateUserDetailsRequestValidationError{
						field:  "AddressDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAddressDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateUserDetailsRequestValidationError{
					field:  "AddressDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UpdateUserDetailsRequest_EmploymentDetails:
		if v == nil {
			err := UpdateUserDetailsRequestValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEmploymentDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateUserDetailsRequestValidationError{
						field:  "EmploymentDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateUserDetailsRequestValidationError{
						field:  "EmploymentDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEmploymentDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateUserDetailsRequestValidationError{
					field:  "EmploymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UpdateUserDetailsRequest_BankAccountDetails:
		if v == nil {
			err := UpdateUserDetailsRequestValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBankAccountDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateUserDetailsRequestValidationError{
						field:  "BankAccountDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateUserDetailsRequestValidationError{
						field:  "BankAccountDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBankAccountDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateUserDetailsRequestValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UpdateUserDetailsRequest_PersonalDetails:
		if v == nil {
			err := UpdateUserDetailsRequestValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPersonalDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateUserDetailsRequestValidationError{
						field:  "PersonalDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateUserDetailsRequestValidationError{
						field:  "PersonalDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPersonalDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateUserDetailsRequestValidationError{
					field:  "PersonalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UpdateUserDetailsRequest_LocationDetails:
		if v == nil {
			err := UpdateUserDetailsRequestValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLocationDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateUserDetailsRequestValidationError{
						field:  "LocationDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateUserDetailsRequestValidationError{
						field:  "LocationDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLocationDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateUserDetailsRequestValidationError{
					field:  "LocationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return UpdateUserDetailsRequestMultiError(errors)
	}

	return nil
}

// UpdateUserDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateUserDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateUserDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserDetailsRequestMultiError) AllErrors() []error { return m }

// UpdateUserDetailsRequestValidationError is the validation error returned by
// UpdateUserDetailsRequest.Validate if the designated constraints aren't met.
type UpdateUserDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserDetailsRequestValidationError) ErrorName() string {
	return "UpdateUserDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserDetailsRequestValidationError{}

// Validate checks the field values on UpdateUserDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserDetailsResponseMultiError, or nil if none found.
func (m *UpdateUserDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateUserDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateUserDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateUserDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateUserDetailsResponseMultiError(errors)
	}

	return nil
}

// UpdateUserDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateUserDetailsResponse.ValidateAll() if the
// designated constraints aren't met.
type UpdateUserDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserDetailsResponseMultiError) AllErrors() []error { return m }

// UpdateUserDetailsResponseValidationError is the validation error returned by
// UpdateUserDetailsResponse.Validate if the designated constraints aren't met.
type UpdateUserDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserDetailsResponseValidationError) ErrorName() string {
	return "UpdateUserDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserDetailsResponseValidationError{}

// Validate checks the field values on RecordConsentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecordConsentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordConsentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecordConsentRequestMultiError, or nil if none found.
func (m *RecordConsentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordConsentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordConsentRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordConsentRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordConsentRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IpAddress

	if len(errors) > 0 {
		return RecordConsentRequestMultiError(errors)
	}

	return nil
}

// RecordConsentRequestMultiError is an error wrapping multiple validation
// errors returned by RecordConsentRequest.ValidateAll() if the designated
// constraints aren't met.
type RecordConsentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordConsentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordConsentRequestMultiError) AllErrors() []error { return m }

// RecordConsentRequestValidationError is the validation error returned by
// RecordConsentRequest.Validate if the designated constraints aren't met.
type RecordConsentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordConsentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordConsentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordConsentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordConsentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordConsentRequestValidationError) ErrorName() string {
	return "RecordConsentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecordConsentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordConsentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordConsentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordConsentRequestValidationError{}

// Validate checks the field values on RecordConsentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecordConsentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordConsentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecordConsentResponseMultiError, or nil if none found.
func (m *RecordConsentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordConsentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordConsentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordConsentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordConsentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecordConsentResponseMultiError(errors)
	}

	return nil
}

// RecordConsentResponseMultiError is an error wrapping multiple validation
// errors returned by RecordConsentResponse.ValidateAll() if the designated
// constraints aren't met.
type RecordConsentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordConsentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordConsentResponseMultiError) AllErrors() []error { return m }

// RecordConsentResponseValidationError is the validation error returned by
// RecordConsentResponse.Validate if the designated constraints aren't met.
type RecordConsentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordConsentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordConsentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordConsentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordConsentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordConsentResponseValidationError) ErrorName() string {
	return "RecordConsentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RecordConsentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordConsentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordConsentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordConsentResponseValidationError{}

// Validate checks the field values on InitPennyDropRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitPennyDropRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitPennyDropRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitPennyDropRequestMultiError, or nil if none found.
func (m *InitPennyDropRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitPennyDropRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	if all {
		switch v := interface{}(m.GetBankAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitPennyDropRequestValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitPennyDropRequestValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitPennyDropRequestValidationError{
				field:  "BankAccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitPennyDropRequestMultiError(errors)
	}

	return nil
}

// InitPennyDropRequestMultiError is an error wrapping multiple validation
// errors returned by InitPennyDropRequest.ValidateAll() if the designated
// constraints aren't met.
type InitPennyDropRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitPennyDropRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitPennyDropRequestMultiError) AllErrors() []error { return m }

// InitPennyDropRequestValidationError is the validation error returned by
// InitPennyDropRequest.Validate if the designated constraints aren't met.
type InitPennyDropRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitPennyDropRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitPennyDropRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitPennyDropRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitPennyDropRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitPennyDropRequestValidationError) ErrorName() string {
	return "InitPennyDropRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitPennyDropRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitPennyDropRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitPennyDropRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitPennyDropRequestValidationError{}

// Validate checks the field values on InitPennDropResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitPennDropResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitPennDropResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitPennDropResponseMultiError, or nil if none found.
func (m *InitPennDropResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitPennDropResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitPennDropResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitPennDropResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitPennDropResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrchId

	if len(errors) > 0 {
		return InitPennDropResponseMultiError(errors)
	}

	return nil
}

// InitPennDropResponseMultiError is an error wrapping multiple validation
// errors returned by InitPennDropResponse.ValidateAll() if the designated
// constraints aren't met.
type InitPennDropResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitPennDropResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitPennDropResponseMultiError) AllErrors() []error { return m }

// InitPennDropResponseValidationError is the validation error returned by
// InitPennDropResponse.Validate if the designated constraints aren't met.
type InitPennDropResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitPennDropResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitPennDropResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitPennDropResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitPennDropResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitPennDropResponseValidationError) ErrorName() string {
	return "InitPennDropResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitPennDropResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitPennDropResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitPennDropResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitPennDropResponseValidationError{}

// Validate checks the field values on GetLoanApplicationStageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanApplicationStageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanApplicationStageRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLoanApplicationStageRequestMultiError, or nil if none found.
func (m *GetLoanApplicationStageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanApplicationStageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	// no validation rules for StageName

	if len(errors) > 0 {
		return GetLoanApplicationStageRequestMultiError(errors)
	}

	return nil
}

// GetLoanApplicationStageRequestMultiError is an error wrapping multiple
// validation errors returned by GetLoanApplicationStageRequest.ValidateAll()
// if the designated constraints aren't met.
type GetLoanApplicationStageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanApplicationStageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanApplicationStageRequestMultiError) AllErrors() []error { return m }

// GetLoanApplicationStageRequestValidationError is the validation error
// returned by GetLoanApplicationStageRequest.Validate if the designated
// constraints aren't met.
type GetLoanApplicationStageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanApplicationStageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanApplicationStageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanApplicationStageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanApplicationStageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanApplicationStageRequestValidationError) ErrorName() string {
	return "GetLoanApplicationStageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanApplicationStageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanApplicationStageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanApplicationStageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanApplicationStageRequestValidationError{}

// Validate checks the field values on GetLoanApplicationStageResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanApplicationStageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanApplicationStageResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLoanApplicationStageResponseMultiError, or nil if none found.
func (m *GetLoanApplicationStageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanApplicationStageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanApplicationStageResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanApplicationStageResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanApplicationStageResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanApplicationStageResponseValidationError{
					field:  "Stage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanApplicationStageResponseValidationError{
					field:  "Stage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanApplicationStageResponseValidationError{
				field:  "Stage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanApplicationStageResponseMultiError(errors)
	}

	return nil
}

// GetLoanApplicationStageResponseMultiError is an error wrapping multiple
// validation errors returned by GetLoanApplicationStageResponse.ValidateAll()
// if the designated constraints aren't met.
type GetLoanApplicationStageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanApplicationStageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanApplicationStageResponseMultiError) AllErrors() []error { return m }

// GetLoanApplicationStageResponseValidationError is the validation error
// returned by GetLoanApplicationStageResponse.Validate if the designated
// constraints aren't met.
type GetLoanApplicationStageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanApplicationStageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanApplicationStageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanApplicationStageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanApplicationStageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanApplicationStageResponseValidationError) ErrorName() string {
	return "GetLoanApplicationStageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanApplicationStageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanApplicationStageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanApplicationStageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanApplicationStageResponseValidationError{}

// Validate checks the field values on GetLoanOfferRequest_BreParams with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanOfferRequest_BreParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanOfferRequest_BreParams with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLoanOfferRequest_BreParamsMultiError, or nil if none found.
func (m *GetLoanOfferRequest_BreParams) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanOfferRequest_BreParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPolicyParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanOfferRequest_BreParamsValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanOfferRequest_BreParamsValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPolicyParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanOfferRequest_BreParamsValidationError{
				field:  "PolicyParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDesiredLoanAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanOfferRequest_BreParamsValidationError{
					field:  "DesiredLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanOfferRequest_BreParamsValidationError{
					field:  "DesiredLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDesiredLoanAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanOfferRequest_BreParamsValidationError{
				field:  "DesiredLoanAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanOfferRequest_BreParamsMultiError(errors)
	}

	return nil
}

// GetLoanOfferRequest_BreParamsMultiError is an error wrapping multiple
// validation errors returned by GetLoanOfferRequest_BreParams.ValidateAll()
// if the designated constraints aren't met.
type GetLoanOfferRequest_BreParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanOfferRequest_BreParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanOfferRequest_BreParamsMultiError) AllErrors() []error { return m }

// GetLoanOfferRequest_BreParamsValidationError is the validation error
// returned by GetLoanOfferRequest_BreParams.Validate if the designated
// constraints aren't met.
type GetLoanOfferRequest_BreParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanOfferRequest_BreParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanOfferRequest_BreParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanOfferRequest_BreParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanOfferRequest_BreParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanOfferRequest_BreParamsValidationError) ErrorName() string {
	return "GetLoanOfferRequest_BreParamsValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanOfferRequest_BreParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanOfferRequest_BreParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanOfferRequest_BreParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanOfferRequest_BreParamsValidationError{}
