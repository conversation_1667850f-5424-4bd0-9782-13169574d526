// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/stockguardian/sgvendorgateway/lms/finflux/service.proto

package finflux

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gringott/api/stockguardian/lms/enums"

	types "github.com/epifi/gringott/api/stockguardian/sgvendorgateway/lms/finflux/types"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.PaymentProtocol(0)

	_ = types.ChargeType(0)
)

// Validate checks the field values on AddChargesRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddChargesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddChargesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddChargesRequestMultiError, or nil if none found.
func (m *AddChargesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddChargesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := AddChargesRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAmount() == nil {
		err := AddChargesRequestValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddChargesRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddChargesRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddChargesRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _AddChargesRequest_ChargeType_NotInLookup[m.GetChargeType()]; ok {
		err := AddChargesRequestValidationError{
			field:  "ChargeType",
			reason: "value must not be in list [CHARGE_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDueDate() == nil {
		err := AddChargesRequestValidationError{
			field:  "DueDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddChargesRequestValidationError{
					field:  "DueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddChargesRequestValidationError{
					field:  "DueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddChargesRequestValidationError{
				field:  "DueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetExternalId()) < 1 {
		err := AddChargesRequestValidationError{
			field:  "ExternalId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AddChargesRequestMultiError(errors)
	}

	return nil
}

// AddChargesRequestMultiError is an error wrapping multiple validation errors
// returned by AddChargesRequest.ValidateAll() if the designated constraints
// aren't met.
type AddChargesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddChargesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddChargesRequestMultiError) AllErrors() []error { return m }

// AddChargesRequestValidationError is the validation error returned by
// AddChargesRequest.Validate if the designated constraints aren't met.
type AddChargesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddChargesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddChargesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddChargesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddChargesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddChargesRequestValidationError) ErrorName() string {
	return "AddChargesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddChargesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddChargesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddChargesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddChargesRequestValidationError{}

var _AddChargesRequest_ChargeType_NotInLookup = map[types.ChargeType]struct{}{
	0: {},
}

// Validate checks the field values on AddChargesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddChargesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddChargesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddChargesResponseMultiError, or nil if none found.
func (m *AddChargesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddChargesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddChargesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddChargesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddChargesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddChargesResponseMultiError(errors)
	}

	return nil
}

// AddChargesResponseMultiError is an error wrapping multiple validation errors
// returned by AddChargesResponse.ValidateAll() if the designated constraints
// aren't met.
type AddChargesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddChargesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddChargesResponseMultiError) AllErrors() []error { return m }

// AddChargesResponseValidationError is the validation error returned by
// AddChargesResponse.Validate if the designated constraints aren't met.
type AddChargesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddChargesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddChargesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddChargesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddChargesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddChargesResponseValidationError) ErrorName() string {
	return "AddChargesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddChargesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddChargesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddChargesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddChargesResponseValidationError{}

// Validate checks the field values on PostLoanRepaymentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PostLoanRepaymentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PostLoanRepaymentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PostLoanRepaymentRequestMultiError, or nil if none found.
func (m *PostLoanRepaymentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PostLoanRepaymentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetTransactionAmount() == nil {
		err := PostLoanRepaymentRequestValidationError{
			field:  "TransactionAmount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTransactionAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostLoanRepaymentRequestValidationError{
					field:  "TransactionAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostLoanRepaymentRequestValidationError{
					field:  "TransactionAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostLoanRepaymentRequestValidationError{
				field:  "TransactionAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetExternalId()) < 1 {
		err := PostLoanRepaymentRequestValidationError{
			field:  "ExternalId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTransactionDate() == nil {
		err := PostLoanRepaymentRequestValidationError{
			field:  "TransactionDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTransactionDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostLoanRepaymentRequestValidationError{
					field:  "TransactionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostLoanRepaymentRequestValidationError{
					field:  "TransactionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostLoanRepaymentRequestValidationError{
				field:  "TransactionDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := PostLoanRepaymentRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PaymentProtocol

	// no validation rules for Utr

	// no validation rules for Note

	if len(errors) > 0 {
		return PostLoanRepaymentRequestMultiError(errors)
	}

	return nil
}

// PostLoanRepaymentRequestMultiError is an error wrapping multiple validation
// errors returned by PostLoanRepaymentRequest.ValidateAll() if the designated
// constraints aren't met.
type PostLoanRepaymentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostLoanRepaymentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostLoanRepaymentRequestMultiError) AllErrors() []error { return m }

// PostLoanRepaymentRequestValidationError is the validation error returned by
// PostLoanRepaymentRequest.Validate if the designated constraints aren't met.
type PostLoanRepaymentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostLoanRepaymentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostLoanRepaymentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostLoanRepaymentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostLoanRepaymentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostLoanRepaymentRequestValidationError) ErrorName() string {
	return "PostLoanRepaymentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PostLoanRepaymentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostLoanRepaymentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostLoanRepaymentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostLoanRepaymentRequestValidationError{}

// Validate checks the field values on PostLoanRepaymentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PostLoanRepaymentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PostLoanRepaymentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PostLoanRepaymentResponseMultiError, or nil if none found.
func (m *PostLoanRepaymentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PostLoanRepaymentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostLoanRepaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostLoanRepaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostLoanRepaymentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PostLoanRepaymentResponseMultiError(errors)
	}

	return nil
}

// PostLoanRepaymentResponseMultiError is an error wrapping multiple validation
// errors returned by PostLoanRepaymentResponse.ValidateAll() if the
// designated constraints aren't met.
type PostLoanRepaymentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostLoanRepaymentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostLoanRepaymentResponseMultiError) AllErrors() []error { return m }

// PostLoanRepaymentResponseValidationError is the validation error returned by
// PostLoanRepaymentResponse.Validate if the designated constraints aren't met.
type PostLoanRepaymentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostLoanRepaymentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostLoanRepaymentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostLoanRepaymentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostLoanRepaymentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostLoanRepaymentResponseValidationError) ErrorName() string {
	return "PostLoanRepaymentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PostLoanRepaymentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostLoanRepaymentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostLoanRepaymentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostLoanRepaymentResponseValidationError{}

// Validate checks the field values on ForecloseLoanRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForecloseLoanRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForecloseLoanRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ForecloseLoanRequestMultiError, or nil if none found.
func (m *ForecloseLoanRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ForecloseLoanRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetTransactionAmount() == nil {
		err := ForecloseLoanRequestValidationError{
			field:  "TransactionAmount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTransactionAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForecloseLoanRequestValidationError{
					field:  "TransactionAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForecloseLoanRequestValidationError{
					field:  "TransactionAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForecloseLoanRequestValidationError{
				field:  "TransactionAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetExternalId()) < 1 {
		err := ForecloseLoanRequestValidationError{
			field:  "ExternalId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTransactionDate() == nil {
		err := ForecloseLoanRequestValidationError{
			field:  "TransactionDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTransactionDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForecloseLoanRequestValidationError{
					field:  "TransactionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForecloseLoanRequestValidationError{
					field:  "TransactionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForecloseLoanRequestValidationError{
				field:  "TransactionDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := ForecloseLoanRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PaymentProtocol

	// no validation rules for Utr

	if len(errors) > 0 {
		return ForecloseLoanRequestMultiError(errors)
	}

	return nil
}

// ForecloseLoanRequestMultiError is an error wrapping multiple validation
// errors returned by ForecloseLoanRequest.ValidateAll() if the designated
// constraints aren't met.
type ForecloseLoanRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForecloseLoanRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForecloseLoanRequestMultiError) AllErrors() []error { return m }

// ForecloseLoanRequestValidationError is the validation error returned by
// ForecloseLoanRequest.Validate if the designated constraints aren't met.
type ForecloseLoanRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForecloseLoanRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForecloseLoanRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForecloseLoanRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForecloseLoanRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForecloseLoanRequestValidationError) ErrorName() string {
	return "ForecloseLoanRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ForecloseLoanRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForecloseLoanRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForecloseLoanRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForecloseLoanRequestValidationError{}

// Validate checks the field values on ForecloseLoanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForecloseLoanResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForecloseLoanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ForecloseLoanResponseMultiError, or nil if none found.
func (m *ForecloseLoanResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ForecloseLoanResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForecloseLoanResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForecloseLoanResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForecloseLoanResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ForecloseLoanResponseMultiError(errors)
	}

	return nil
}

// ForecloseLoanResponseMultiError is an error wrapping multiple validation
// errors returned by ForecloseLoanResponse.ValidateAll() if the designated
// constraints aren't met.
type ForecloseLoanResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForecloseLoanResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForecloseLoanResponseMultiError) AllErrors() []error { return m }

// ForecloseLoanResponseValidationError is the validation error returned by
// ForecloseLoanResponse.Validate if the designated constraints aren't met.
type ForecloseLoanResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForecloseLoanResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForecloseLoanResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForecloseLoanResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForecloseLoanResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForecloseLoanResponseValidationError) ErrorName() string {
	return "ForecloseLoanResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ForecloseLoanResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForecloseLoanResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForecloseLoanResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForecloseLoanResponseValidationError{}

// Validate checks the field values on GetForeclosureDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetForeclosureDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetForeclosureDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetForeclosureDetailsRequestMultiError, or nil if none found.
func (m *GetForeclosureDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetForeclosureDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := GetForeclosureDetailsRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetForeclosureDate() == nil {
		err := GetForeclosureDetailsRequestValidationError{
			field:  "ForeclosureDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetForeclosureDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureDetailsRequestValidationError{
					field:  "ForeclosureDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureDetailsRequestValidationError{
					field:  "ForeclosureDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetForeclosureDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureDetailsRequestValidationError{
				field:  "ForeclosureDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetForeclosureDetailsRequestMultiError(errors)
	}

	return nil
}

// GetForeclosureDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by GetForeclosureDetailsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetForeclosureDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetForeclosureDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetForeclosureDetailsRequestMultiError) AllErrors() []error { return m }

// GetForeclosureDetailsRequestValidationError is the validation error returned
// by GetForeclosureDetailsRequest.Validate if the designated constraints
// aren't met.
type GetForeclosureDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetForeclosureDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetForeclosureDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetForeclosureDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetForeclosureDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetForeclosureDetailsRequestValidationError) ErrorName() string {
	return "GetForeclosureDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetForeclosureDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetForeclosureDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetForeclosureDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetForeclosureDetailsRequestValidationError{}

// Validate checks the field values on GetForeclosureDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetForeclosureDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetForeclosureDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetForeclosureDetailsResponseMultiError, or nil if none found.
func (m *GetForeclosureDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetForeclosureDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureDetailsResponseValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNetForeclosureAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "NetForeclosureAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "NetForeclosureAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetForeclosureAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureDetailsResponseValidationError{
				field:  "NetForeclosureAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrincipalPortion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "PrincipalPortion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "PrincipalPortion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalPortion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureDetailsResponseValidationError{
				field:  "PrincipalPortion",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestPortion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "InterestPortion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "InterestPortion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestPortion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureDetailsResponseValidationError{
				field:  "InterestPortion",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeeChargesPortion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "FeeChargesPortion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "FeeChargesPortion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeeChargesPortion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureDetailsResponseValidationError{
				field:  "FeeChargesPortion",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPenaltyChargesPortion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "PenaltyChargesPortion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "PenaltyChargesPortion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPenaltyChargesPortion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureDetailsResponseValidationError{
				field:  "PenaltyChargesPortion",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOutstandingLoanBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "OutstandingLoanBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "OutstandingLoanBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOutstandingLoanBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureDetailsResponseValidationError{
				field:  "OutstandingLoanBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetForeclosureCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "ForeclosureCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "ForeclosureCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetForeclosureCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureDetailsResponseValidationError{
				field:  "ForeclosureCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetForeclosureDetailsResponseMultiError(errors)
	}

	return nil
}

// GetForeclosureDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by GetForeclosureDetailsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetForeclosureDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetForeclosureDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetForeclosureDetailsResponseMultiError) AllErrors() []error { return m }

// GetForeclosureDetailsResponseValidationError is the validation error
// returned by GetForeclosureDetailsResponse.Validate if the designated
// constraints aren't met.
type GetForeclosureDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetForeclosureDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetForeclosureDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetForeclosureDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetForeclosureDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetForeclosureDetailsResponseValidationError) ErrorName() string {
	return "GetForeclosureDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetForeclosureDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetForeclosureDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetForeclosureDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetForeclosureDetailsResponseValidationError{}

// Validate checks the field values on GetLoanCancellationDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetLoanCancellationDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanCancellationDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetLoanCancellationDetailsRequestMultiError, or nil if none found.
func (m *GetLoanCancellationDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanCancellationDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := GetLoanCancellationDetailsRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetCancellationDate() == nil {
		err := GetLoanCancellationDetailsRequestValidationError{
			field:  "CancellationDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCancellationDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanCancellationDetailsRequestValidationError{
					field:  "CancellationDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanCancellationDetailsRequestValidationError{
					field:  "CancellationDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCancellationDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanCancellationDetailsRequestValidationError{
				field:  "CancellationDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanCancellationDetailsRequestMultiError(errors)
	}

	return nil
}

// GetLoanCancellationDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetLoanCancellationDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLoanCancellationDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanCancellationDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanCancellationDetailsRequestMultiError) AllErrors() []error { return m }

// GetLoanCancellationDetailsRequestValidationError is the validation error
// returned by GetLoanCancellationDetailsRequest.Validate if the designated
// constraints aren't met.
type GetLoanCancellationDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanCancellationDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanCancellationDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanCancellationDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanCancellationDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanCancellationDetailsRequestValidationError) ErrorName() string {
	return "GetLoanCancellationDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanCancellationDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanCancellationDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanCancellationDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanCancellationDetailsRequestValidationError{}

// Validate checks the field values on GetLoanCancellationDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetLoanCancellationDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanCancellationDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetLoanCancellationDetailsResponseMultiError, or nil if none found.
func (m *GetLoanCancellationDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanCancellationDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanCancellationDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanCancellationDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanCancellationDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanCancellationDetailsResponseValidationError{
					field:  "LoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanCancellationDetailsResponseValidationError{
					field:  "LoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanCancellationDetailsResponseValidationError{
				field:  "LoanAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCancellationAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanCancellationDetailsResponseValidationError{
					field:  "CancellationAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanCancellationDetailsResponseValidationError{
					field:  "CancellationAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCancellationAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanCancellationDetailsResponseValidationError{
				field:  "CancellationAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanCancellationDetailsResponseMultiError(errors)
	}

	return nil
}

// GetLoanCancellationDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetLoanCancellationDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLoanCancellationDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanCancellationDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanCancellationDetailsResponseMultiError) AllErrors() []error { return m }

// GetLoanCancellationDetailsResponseValidationError is the validation error
// returned by GetLoanCancellationDetailsResponse.Validate if the designated
// constraints aren't met.
type GetLoanCancellationDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanCancellationDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanCancellationDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanCancellationDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanCancellationDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanCancellationDetailsResponseValidationError) ErrorName() string {
	return "GetLoanCancellationDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanCancellationDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanCancellationDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanCancellationDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanCancellationDetailsResponseValidationError{}

// Validate checks the field values on CancelLoanRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CancelLoanRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelLoanRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelLoanRequestMultiError, or nil if none found.
func (m *CancelLoanRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelLoanRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetTransactionAmount() == nil {
		err := CancelLoanRequestValidationError{
			field:  "TransactionAmount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTransactionAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CancelLoanRequestValidationError{
					field:  "TransactionAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CancelLoanRequestValidationError{
					field:  "TransactionAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CancelLoanRequestValidationError{
				field:  "TransactionAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetExternalId()) < 1 {
		err := CancelLoanRequestValidationError{
			field:  "ExternalId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTransactionDate() == nil {
		err := CancelLoanRequestValidationError{
			field:  "TransactionDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTransactionDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CancelLoanRequestValidationError{
					field:  "TransactionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CancelLoanRequestValidationError{
					field:  "TransactionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CancelLoanRequestValidationError{
				field:  "TransactionDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := CancelLoanRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PaymentProtocol

	if all {
		switch v := interface{}(m.GetInterestWaiverAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CancelLoanRequestValidationError{
					field:  "InterestWaiverAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CancelLoanRequestValidationError{
					field:  "InterestWaiverAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestWaiverAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CancelLoanRequestValidationError{
				field:  "InterestWaiverAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CancelLoanRequestMultiError(errors)
	}

	return nil
}

// CancelLoanRequestMultiError is an error wrapping multiple validation errors
// returned by CancelLoanRequest.ValidateAll() if the designated constraints
// aren't met.
type CancelLoanRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelLoanRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelLoanRequestMultiError) AllErrors() []error { return m }

// CancelLoanRequestValidationError is the validation error returned by
// CancelLoanRequest.Validate if the designated constraints aren't met.
type CancelLoanRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelLoanRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelLoanRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelLoanRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelLoanRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelLoanRequestValidationError) ErrorName() string {
	return "CancelLoanRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CancelLoanRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelLoanRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelLoanRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelLoanRequestValidationError{}

// Validate checks the field values on CancelLoanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelLoanResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelLoanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelLoanResponseMultiError, or nil if none found.
func (m *CancelLoanResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelLoanResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CancelLoanResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CancelLoanResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CancelLoanResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CancelLoanResponseMultiError(errors)
	}

	return nil
}

// CancelLoanResponseMultiError is an error wrapping multiple validation errors
// returned by CancelLoanResponse.ValidateAll() if the designated constraints
// aren't met.
type CancelLoanResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelLoanResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelLoanResponseMultiError) AllErrors() []error { return m }

// CancelLoanResponseValidationError is the validation error returned by
// CancelLoanResponse.Validate if the designated constraints aren't met.
type CancelLoanResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelLoanResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelLoanResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelLoanResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelLoanResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelLoanResponseValidationError) ErrorName() string {
	return "CancelLoanResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CancelLoanResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelLoanResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelLoanResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelLoanResponseValidationError{}

// Validate checks the field values on RejectLoanRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RejectLoanRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RejectLoanRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RejectLoanRequestMultiError, or nil if none found.
func (m *RejectLoanRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RejectLoanRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := RejectLoanRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRejectedOn() == nil {
		err := RejectLoanRequestValidationError{
			field:  "RejectedOn",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRejectedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RejectLoanRequestValidationError{
					field:  "RejectedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RejectLoanRequestValidationError{
					field:  "RejectedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRejectedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RejectLoanRequestValidationError{
				field:  "RejectedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RejectLoanRequestMultiError(errors)
	}

	return nil
}

// RejectLoanRequestMultiError is an error wrapping multiple validation errors
// returned by RejectLoanRequest.ValidateAll() if the designated constraints
// aren't met.
type RejectLoanRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RejectLoanRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RejectLoanRequestMultiError) AllErrors() []error { return m }

// RejectLoanRequestValidationError is the validation error returned by
// RejectLoanRequest.Validate if the designated constraints aren't met.
type RejectLoanRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RejectLoanRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RejectLoanRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RejectLoanRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RejectLoanRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RejectLoanRequestValidationError) ErrorName() string {
	return "RejectLoanRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RejectLoanRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRejectLoanRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RejectLoanRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RejectLoanRequestValidationError{}

// Validate checks the field values on RejectLoanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RejectLoanResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RejectLoanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RejectLoanResponseMultiError, or nil if none found.
func (m *RejectLoanResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RejectLoanResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RejectLoanResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RejectLoanResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RejectLoanResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanStatus

	// no validation rules for LoanId

	// no validation rules for ClientId

	if all {
		switch v := interface{}(m.GetRejectedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RejectLoanResponseValidationError{
					field:  "RejectedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RejectLoanResponseValidationError{
					field:  "RejectedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRejectedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RejectLoanResponseValidationError{
				field:  "RejectedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClosedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RejectLoanResponseValidationError{
					field:  "ClosedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RejectLoanResponseValidationError{
					field:  "ClosedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClosedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RejectLoanResponseValidationError{
				field:  "ClosedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RejectLoanResponseMultiError(errors)
	}

	return nil
}

// RejectLoanResponseMultiError is an error wrapping multiple validation errors
// returned by RejectLoanResponse.ValidateAll() if the designated constraints
// aren't met.
type RejectLoanResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RejectLoanResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RejectLoanResponseMultiError) AllErrors() []error { return m }

// RejectLoanResponseValidationError is the validation error returned by
// RejectLoanResponse.Validate if the designated constraints aren't met.
type RejectLoanResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RejectLoanResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RejectLoanResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RejectLoanResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RejectLoanResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RejectLoanResponseValidationError) ErrorName() string {
	return "RejectLoanResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RejectLoanResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRejectLoanResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RejectLoanResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RejectLoanResponseValidationError{}

// Validate checks the field values on FetchLoanScheduleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchLoanScheduleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchLoanScheduleRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchLoanScheduleRequestMultiError, or nil if none found.
func (m *FetchLoanScheduleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchLoanScheduleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	oneofIdentifierPresent := false
	switch v := m.Identifier.(type) {
	case *FetchLoanScheduleRequest_LoanId:
		if v == nil {
			err := FetchLoanScheduleRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofIdentifierPresent = true
		// no validation rules for LoanId
	case *FetchLoanScheduleRequest_ExternalId:
		if v == nil {
			err := FetchLoanScheduleRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofIdentifierPresent = true
		// no validation rules for ExternalId
	default:
		_ = v // ensures v is used
	}
	if !oneofIdentifierPresent {
		err := FetchLoanScheduleRequestValidationError{
			field:  "Identifier",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FetchLoanScheduleRequestMultiError(errors)
	}

	return nil
}

// FetchLoanScheduleRequestMultiError is an error wrapping multiple validation
// errors returned by FetchLoanScheduleRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchLoanScheduleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchLoanScheduleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchLoanScheduleRequestMultiError) AllErrors() []error { return m }

// FetchLoanScheduleRequestValidationError is the validation error returned by
// FetchLoanScheduleRequest.Validate if the designated constraints aren't met.
type FetchLoanScheduleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchLoanScheduleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchLoanScheduleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchLoanScheduleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchLoanScheduleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchLoanScheduleRequestValidationError) ErrorName() string {
	return "FetchLoanScheduleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchLoanScheduleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchLoanScheduleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchLoanScheduleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchLoanScheduleRequestValidationError{}

// Validate checks the field values on FetchLoanScheduleResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchLoanScheduleResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchLoanScheduleResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchLoanScheduleResponseMultiError, or nil if none found.
func (m *FetchLoanScheduleResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchLoanScheduleResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchLoanScheduleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchLoanScheduleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchLoanScheduleResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoan()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchLoanScheduleResponseValidationError{
					field:  "Loan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchLoanScheduleResponseValidationError{
					field:  "Loan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoan()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchLoanScheduleResponseValidationError{
				field:  "Loan",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPeriods() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FetchLoanScheduleResponseValidationError{
						field:  fmt.Sprintf("Periods[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FetchLoanScheduleResponseValidationError{
						field:  fmt.Sprintf("Periods[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FetchLoanScheduleResponseValidationError{
					field:  fmt.Sprintf("Periods[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetTotalOverpaidAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchLoanScheduleResponseValidationError{
					field:  "TotalOverpaidAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchLoanScheduleResponseValidationError{
					field:  "TotalOverpaidAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalOverpaidAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchLoanScheduleResponseValidationError{
				field:  "TotalOverpaidAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchLoanScheduleResponseMultiError(errors)
	}

	return nil
}

// FetchLoanScheduleResponseMultiError is an error wrapping multiple validation
// errors returned by FetchLoanScheduleResponse.ValidateAll() if the
// designated constraints aren't met.
type FetchLoanScheduleResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchLoanScheduleResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchLoanScheduleResponseMultiError) AllErrors() []error { return m }

// FetchLoanScheduleResponseValidationError is the validation error returned by
// FetchLoanScheduleResponse.Validate if the designated constraints aren't met.
type FetchLoanScheduleResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchLoanScheduleResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchLoanScheduleResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchLoanScheduleResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchLoanScheduleResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchLoanScheduleResponseValidationError) ErrorName() string {
	return "FetchLoanScheduleResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchLoanScheduleResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchLoanScheduleResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchLoanScheduleResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchLoanScheduleResponseValidationError{}

// Validate checks the field values on CalculateLoanScheduleRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CalculateLoanScheduleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CalculateLoanScheduleRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CalculateLoanScheduleRequestMultiError, or nil if none found.
func (m *CalculateLoanScheduleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CalculateLoanScheduleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetProductId()) < 1 {
		err := CalculateLoanScheduleRequestValidationError{
			field:  "ProductId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPrincipalAmount() == nil {
		err := CalculateLoanScheduleRequestValidationError{
			field:  "PrincipalAmount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPrincipalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleRequestValidationError{
					field:  "PrincipalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleRequestValidationError{
					field:  "PrincipalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleRequestValidationError{
				field:  "PrincipalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetNumberOfInstallments() < 1 {
		err := CalculateLoanScheduleRequestValidationError{
			field:  "NumberOfInstallments",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for InterestRate

	if m.GetExpectedDisbursementDate() == nil {
		err := CalculateLoanScheduleRequestValidationError{
			field:  "ExpectedDisbursementDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetExpectedDisbursementDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleRequestValidationError{
					field:  "ExpectedDisbursementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleRequestValidationError{
					field:  "ExpectedDisbursementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectedDisbursementDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleRequestValidationError{
				field:  "ExpectedDisbursementDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetSubmittedOn() == nil {
		err := CalculateLoanScheduleRequestValidationError{
			field:  "SubmittedOn",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSubmittedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleRequestValidationError{
					field:  "SubmittedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleRequestValidationError{
					field:  "SubmittedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubmittedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleRequestValidationError{
				field:  "SubmittedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProcessingFeePercentageIncludingGst

	if len(errors) > 0 {
		return CalculateLoanScheduleRequestMultiError(errors)
	}

	return nil
}

// CalculateLoanScheduleRequestMultiError is an error wrapping multiple
// validation errors returned by CalculateLoanScheduleRequest.ValidateAll() if
// the designated constraints aren't met.
type CalculateLoanScheduleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CalculateLoanScheduleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CalculateLoanScheduleRequestMultiError) AllErrors() []error { return m }

// CalculateLoanScheduleRequestValidationError is the validation error returned
// by CalculateLoanScheduleRequest.Validate if the designated constraints
// aren't met.
type CalculateLoanScheduleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CalculateLoanScheduleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CalculateLoanScheduleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CalculateLoanScheduleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CalculateLoanScheduleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CalculateLoanScheduleRequestValidationError) ErrorName() string {
	return "CalculateLoanScheduleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CalculateLoanScheduleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCalculateLoanScheduleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CalculateLoanScheduleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CalculateLoanScheduleRequestValidationError{}

// Validate checks the field values on CalculateLoanScheduleResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CalculateLoanScheduleResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CalculateLoanScheduleResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CalculateLoanScheduleResponseMultiError, or nil if none found.
func (m *CalculateLoanScheduleResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CalculateLoanScheduleResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalPrincipalDisbursed()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "TotalPrincipalDisbursed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "TotalPrincipalDisbursed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalPrincipalDisbursed()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleResponseValidationError{
				field:  "TotalPrincipalDisbursed",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalPrincipalExpected()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "TotalPrincipalExpected",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "TotalPrincipalExpected",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalPrincipalExpected()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleResponseValidationError{
				field:  "TotalPrincipalExpected",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalPrincipalPaid()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "TotalPrincipalPaid",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "TotalPrincipalPaid",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalPrincipalPaid()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleResponseValidationError{
				field:  "TotalPrincipalPaid",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalInterestCharged()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "TotalInterestCharged",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "TotalInterestCharged",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalInterestCharged()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleResponseValidationError{
				field:  "TotalInterestCharged",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalFeeChargesCharged()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "TotalFeeChargesCharged",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "TotalFeeChargesCharged",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalFeeChargesCharged()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleResponseValidationError{
				field:  "TotalFeeChargesCharged",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalPenaltyChargesCharged()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "TotalPenaltyChargesCharged",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "TotalPenaltyChargesCharged",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalPenaltyChargesCharged()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleResponseValidationError{
				field:  "TotalPenaltyChargesCharged",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalRepaymentExpected()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "TotalRepaymentExpected",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "TotalRepaymentExpected",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalRepaymentExpected()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleResponseValidationError{
				field:  "TotalRepaymentExpected",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "TotalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "TotalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleResponseValidationError{
				field:  "TotalOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCalculatedEmiAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "CalculatedEmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "CalculatedEmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCalculatedEmiAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleResponseValidationError{
				field:  "CalculatedEmiAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPeriods() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CalculateLoanScheduleResponseValidationError{
						field:  fmt.Sprintf("Periods[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CalculateLoanScheduleResponseValidationError{
						field:  fmt.Sprintf("Periods[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CalculateLoanScheduleResponseValidationError{
					field:  fmt.Sprintf("Periods[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetNetDisbursalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "NetDisbursalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "NetDisbursalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetDisbursalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleResponseValidationError{
				field:  "NetDisbursalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpectedMaturityDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "ExpectedMaturityDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "ExpectedMaturityDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectedMaturityDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleResponseValidationError{
				field:  "ExpectedMaturityDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AnnualPercentageRate

	// no validation rules for NumberOfRepayments

	// no validation rules for RepaymentFrequency

	if all {
		switch v := interface{}(m.GetChargesDueAtDisbursement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "ChargesDueAtDisbursement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "ChargesDueAtDisbursement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChargesDueAtDisbursement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleResponseValidationError{
				field:  "ChargesDueAtDisbursement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrokenPeriodInterest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "BrokenPeriodInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CalculateLoanScheduleResponseValidationError{
					field:  "BrokenPeriodInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrokenPeriodInterest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CalculateLoanScheduleResponseValidationError{
				field:  "BrokenPeriodInterest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CalculateLoanScheduleResponseMultiError(errors)
	}

	return nil
}

// CalculateLoanScheduleResponseMultiError is an error wrapping multiple
// validation errors returned by CalculateLoanScheduleResponse.ValidateAll()
// if the designated constraints aren't met.
type CalculateLoanScheduleResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CalculateLoanScheduleResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CalculateLoanScheduleResponseMultiError) AllErrors() []error { return m }

// CalculateLoanScheduleResponseValidationError is the validation error
// returned by CalculateLoanScheduleResponse.Validate if the designated
// constraints aren't met.
type CalculateLoanScheduleResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CalculateLoanScheduleResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CalculateLoanScheduleResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CalculateLoanScheduleResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CalculateLoanScheduleResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CalculateLoanScheduleResponseValidationError) ErrorName() string {
	return "CalculateLoanScheduleResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CalculateLoanScheduleResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCalculateLoanScheduleResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CalculateLoanScheduleResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CalculateLoanScheduleResponseValidationError{}

// Validate checks the field values on FetchLoanDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchLoanDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchLoanDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchLoanDetailsRequestMultiError, or nil if none found.
func (m *FetchLoanDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchLoanDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	oneofIdentifierPresent := false
	switch v := m.Identifier.(type) {
	case *FetchLoanDetailsRequest_LoanId:
		if v == nil {
			err := FetchLoanDetailsRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofIdentifierPresent = true
		// no validation rules for LoanId
	case *FetchLoanDetailsRequest_ExternalId:
		if v == nil {
			err := FetchLoanDetailsRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofIdentifierPresent = true
		// no validation rules for ExternalId
	default:
		_ = v // ensures v is used
	}
	if !oneofIdentifierPresent {
		err := FetchLoanDetailsRequestValidationError{
			field:  "Identifier",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FetchLoanDetailsRequestMultiError(errors)
	}

	return nil
}

// FetchLoanDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by FetchLoanDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchLoanDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchLoanDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchLoanDetailsRequestMultiError) AllErrors() []error { return m }

// FetchLoanDetailsRequestValidationError is the validation error returned by
// FetchLoanDetailsRequest.Validate if the designated constraints aren't met.
type FetchLoanDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchLoanDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchLoanDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchLoanDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchLoanDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchLoanDetailsRequestValidationError) ErrorName() string {
	return "FetchLoanDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchLoanDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchLoanDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchLoanDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchLoanDetailsRequestValidationError{}

// Validate checks the field values on FetchLoanDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchLoanDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchLoanDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchLoanDetailsResponseMultiError, or nil if none found.
func (m *FetchLoanDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchLoanDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchLoanDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchLoanDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchLoanDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoan()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchLoanDetailsResponseValidationError{
					field:  "Loan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchLoanDetailsResponseValidationError{
					field:  "Loan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoan()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchLoanDetailsResponseValidationError{
				field:  "Loan",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchLoanDetailsResponseMultiError(errors)
	}

	return nil
}

// FetchLoanDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by FetchLoanDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchLoanDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchLoanDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchLoanDetailsResponseMultiError) AllErrors() []error { return m }

// FetchLoanDetailsResponseValidationError is the validation error returned by
// FetchLoanDetailsResponse.Validate if the designated constraints aren't met.
type FetchLoanDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchLoanDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchLoanDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchLoanDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchLoanDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchLoanDetailsResponseValidationError) ErrorName() string {
	return "FetchLoanDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchLoanDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchLoanDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchLoanDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchLoanDetailsResponseValidationError{}

// Validate checks the field values on CreateLoanRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateLoanRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLoanRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLoanRequestMultiError, or nil if none found.
func (m *CreateLoanRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLoanRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetProductId()) < 1 {
		err := CreateLoanRequestValidationError{
			field:  "ProductId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetClientId()) < 1 {
		err := CreateLoanRequestValidationError{
			field:  "ClientId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetExternalId()) < 1 {
		err := CreateLoanRequestValidationError{
			field:  "ExternalId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPrincipalAmount() == nil {
		err := CreateLoanRequestValidationError{
			field:  "PrincipalAmount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPrincipalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanRequestValidationError{
					field:  "PrincipalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanRequestValidationError{
					field:  "PrincipalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanRequestValidationError{
				field:  "PrincipalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetSubmittedOn() == nil {
		err := CreateLoanRequestValidationError{
			field:  "SubmittedOn",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSubmittedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanRequestValidationError{
					field:  "SubmittedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanRequestValidationError{
					field:  "SubmittedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubmittedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanRequestValidationError{
				field:  "SubmittedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetNumberOfRepayments() < 1 {
		err := CreateLoanRequestValidationError{
			field:  "NumberOfRepayments",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ProcessingFeePercentageIncludingGst

	// no validation rules for InterestRate

	if m.GetExpectedDisbursementDate() == nil {
		err := CreateLoanRequestValidationError{
			field:  "ExpectedDisbursementDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetExpectedDisbursementDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanRequestValidationError{
					field:  "ExpectedDisbursementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanRequestValidationError{
					field:  "ExpectedDisbursementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectedDisbursementDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanRequestValidationError{
				field:  "ExpectedDisbursementDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetFundId()) < 1 {
		err := CreateLoanRequestValidationError{
			field:  "FundId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmountForUpfrontCollection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanRequestValidationError{
					field:  "AmountForUpfrontCollection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanRequestValidationError{
					field:  "AmountForUpfrontCollection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountForUpfrontCollection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanRequestValidationError{
				field:  "AmountForUpfrontCollection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateLoanRequestMultiError(errors)
	}

	return nil
}

// CreateLoanRequestMultiError is an error wrapping multiple validation errors
// returned by CreateLoanRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateLoanRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLoanRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLoanRequestMultiError) AllErrors() []error { return m }

// CreateLoanRequestValidationError is the validation error returned by
// CreateLoanRequest.Validate if the designated constraints aren't met.
type CreateLoanRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLoanRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLoanRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLoanRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLoanRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLoanRequestValidationError) ErrorName() string {
	return "CreateLoanRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLoanRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLoanRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLoanRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLoanRequestValidationError{}

// Validate checks the field values on CreateLoanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLoanResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLoanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLoanResponseMultiError, or nil if none found.
func (m *CreateLoanResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLoanResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanId

	if len(errors) > 0 {
		return CreateLoanResponseMultiError(errors)
	}

	return nil
}

// CreateLoanResponseMultiError is an error wrapping multiple validation errors
// returned by CreateLoanResponse.ValidateAll() if the designated constraints
// aren't met.
type CreateLoanResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLoanResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLoanResponseMultiError) AllErrors() []error { return m }

// CreateLoanResponseValidationError is the validation error returned by
// CreateLoanResponse.Validate if the designated constraints aren't met.
type CreateLoanResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLoanResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLoanResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLoanResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLoanResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLoanResponseValidationError) ErrorName() string {
	return "CreateLoanResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLoanResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLoanResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLoanResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLoanResponseValidationError{}

// Validate checks the field values on DisburseLoanRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DisburseLoanRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DisburseLoanRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DisburseLoanRequestMultiError, or nil if none found.
func (m *DisburseLoanRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DisburseLoanRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := DisburseLoanRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTransactionAmount() == nil {
		err := DisburseLoanRequestValidationError{
			field:  "TransactionAmount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTransactionAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DisburseLoanRequestValidationError{
					field:  "TransactionAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DisburseLoanRequestValidationError{
					field:  "TransactionAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DisburseLoanRequestValidationError{
				field:  "TransactionAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetActualDisbursementDate() == nil {
		err := DisburseLoanRequestValidationError{
			field:  "ActualDisbursementDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetActualDisbursementDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DisburseLoanRequestValidationError{
					field:  "ActualDisbursementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DisburseLoanRequestValidationError{
					field:  "ActualDisbursementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActualDisbursementDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DisburseLoanRequestValidationError{
				field:  "ActualDisbursementDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DisbursementBankAccountId

	// no validation rules for DisbursementTxnPaymentProtocol

	if utf8.RuneCountInString(m.GetDisbursementUtr()) < 1 {
		err := DisburseLoanRequestValidationError{
			field:  "DisbursementUtr",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DisburseLoanRequestMultiError(errors)
	}

	return nil
}

// DisburseLoanRequestMultiError is an error wrapping multiple validation
// errors returned by DisburseLoanRequest.ValidateAll() if the designated
// constraints aren't met.
type DisburseLoanRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DisburseLoanRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DisburseLoanRequestMultiError) AllErrors() []error { return m }

// DisburseLoanRequestValidationError is the validation error returned by
// DisburseLoanRequest.Validate if the designated constraints aren't met.
type DisburseLoanRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DisburseLoanRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DisburseLoanRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DisburseLoanRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DisburseLoanRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DisburseLoanRequestValidationError) ErrorName() string {
	return "DisburseLoanRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DisburseLoanRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDisburseLoanRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DisburseLoanRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DisburseLoanRequestValidationError{}

// Validate checks the field values on DisburseLoanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DisburseLoanResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DisburseLoanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DisburseLoanResponseMultiError, or nil if none found.
func (m *DisburseLoanResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DisburseLoanResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DisburseLoanResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DisburseLoanResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DisburseLoanResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DisburseLoanResponseMultiError(errors)
	}

	return nil
}

// DisburseLoanResponseMultiError is an error wrapping multiple validation
// errors returned by DisburseLoanResponse.ValidateAll() if the designated
// constraints aren't met.
type DisburseLoanResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DisburseLoanResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DisburseLoanResponseMultiError) AllErrors() []error { return m }

// DisburseLoanResponseValidationError is the validation error returned by
// DisburseLoanResponse.Validate if the designated constraints aren't met.
type DisburseLoanResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DisburseLoanResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DisburseLoanResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DisburseLoanResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DisburseLoanResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DisburseLoanResponseValidationError) ErrorName() string {
	return "DisburseLoanResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DisburseLoanResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDisburseLoanResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DisburseLoanResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DisburseLoanResponseValidationError{}

// Validate checks the field values on ApproveLoanRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApproveLoanRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApproveLoanRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApproveLoanRequestMultiError, or nil if none found.
func (m *ApproveLoanRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ApproveLoanRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := ApproveLoanRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetApprovedOn() == nil {
		err := ApproveLoanRequestValidationError{
			field:  "ApprovedOn",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetApprovedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApproveLoanRequestValidationError{
					field:  "ApprovedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApproveLoanRequestValidationError{
					field:  "ApprovedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApprovedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApproveLoanRequestValidationError{
				field:  "ApprovedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetApprovedLoanAmount() == nil {
		err := ApproveLoanRequestValidationError{
			field:  "ApprovedLoanAmount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetApprovedLoanAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApproveLoanRequestValidationError{
					field:  "ApprovedLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApproveLoanRequestValidationError{
					field:  "ApprovedLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApprovedLoanAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApproveLoanRequestValidationError{
				field:  "ApprovedLoanAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetExpectedDisbursementDate() == nil {
		err := ApproveLoanRequestValidationError{
			field:  "ExpectedDisbursementDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetExpectedDisbursementDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApproveLoanRequestValidationError{
					field:  "ExpectedDisbursementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApproveLoanRequestValidationError{
					field:  "ExpectedDisbursementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectedDisbursementDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApproveLoanRequestValidationError{
				field:  "ExpectedDisbursementDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ApproveLoanRequestMultiError(errors)
	}

	return nil
}

// ApproveLoanRequestMultiError is an error wrapping multiple validation errors
// returned by ApproveLoanRequest.ValidateAll() if the designated constraints
// aren't met.
type ApproveLoanRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApproveLoanRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApproveLoanRequestMultiError) AllErrors() []error { return m }

// ApproveLoanRequestValidationError is the validation error returned by
// ApproveLoanRequest.Validate if the designated constraints aren't met.
type ApproveLoanRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApproveLoanRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApproveLoanRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApproveLoanRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApproveLoanRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApproveLoanRequestValidationError) ErrorName() string {
	return "ApproveLoanRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ApproveLoanRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApproveLoanRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApproveLoanRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApproveLoanRequestValidationError{}

// Validate checks the field values on ApproveLoanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApproveLoanResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApproveLoanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApproveLoanResponseMultiError, or nil if none found.
func (m *ApproveLoanResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ApproveLoanResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApproveLoanResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApproveLoanResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApproveLoanResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ApproveLoanResponseMultiError(errors)
	}

	return nil
}

// ApproveLoanResponseMultiError is an error wrapping multiple validation
// errors returned by ApproveLoanResponse.ValidateAll() if the designated
// constraints aren't met.
type ApproveLoanResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApproveLoanResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApproveLoanResponseMultiError) AllErrors() []error { return m }

// ApproveLoanResponseValidationError is the validation error returned by
// ApproveLoanResponse.Validate if the designated constraints aren't met.
type ApproveLoanResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApproveLoanResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApproveLoanResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApproveLoanResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApproveLoanResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApproveLoanResponseValidationError) ErrorName() string {
	return "ApproveLoanResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ApproveLoanResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApproveLoanResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApproveLoanResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApproveLoanResponseValidationError{}

// Validate checks the field values on CreateClientRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateClientRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateClientRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateClientRequestMultiError, or nil if none found.
func (m *CreateClientRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateClientRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetClient() == nil {
		err := CreateClientRequestValidationError{
			field:  "Client",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetClient()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateClientRequestValidationError{
					field:  "Client",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateClientRequestValidationError{
					field:  "Client",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClient()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateClientRequestValidationError{
				field:  "Client",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetPermanentAddress() == nil {
		err := CreateClientRequestValidationError{
			field:  "PermanentAddress",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPermanentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateClientRequestValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateClientRequestValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPermanentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateClientRequestValidationError{
				field:  "PermanentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetResidentialAddress() == nil {
		err := CreateClientRequestValidationError{
			field:  "ResidentialAddress",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetResidentialAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateClientRequestValidationError{
					field:  "ResidentialAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateClientRequestValidationError{
					field:  "ResidentialAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResidentialAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateClientRequestValidationError{
				field:  "ResidentialAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetKycAddress() == nil {
		err := CreateClientRequestValidationError{
			field:  "KycAddress",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetKycAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateClientRequestValidationError{
					field:  "KycAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateClientRequestValidationError{
					field:  "KycAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKycAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateClientRequestValidationError{
				field:  "KycAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateClientRequestMultiError(errors)
	}

	return nil
}

// CreateClientRequestMultiError is an error wrapping multiple validation
// errors returned by CreateClientRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateClientRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateClientRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateClientRequestMultiError) AllErrors() []error { return m }

// CreateClientRequestValidationError is the validation error returned by
// CreateClientRequest.Validate if the designated constraints aren't met.
type CreateClientRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateClientRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateClientRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateClientRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateClientRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateClientRequestValidationError) ErrorName() string {
	return "CreateClientRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateClientRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateClientRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateClientRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateClientRequestValidationError{}

// Validate checks the field values on CreateClientResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateClientResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateClientResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateClientResponseMultiError, or nil if none found.
func (m *CreateClientResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateClientResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateClientResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateClientResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateClientResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientId

	if len(errors) > 0 {
		return CreateClientResponseMultiError(errors)
	}

	return nil
}

// CreateClientResponseMultiError is an error wrapping multiple validation
// errors returned by CreateClientResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateClientResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateClientResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateClientResponseMultiError) AllErrors() []error { return m }

// CreateClientResponseValidationError is the validation error returned by
// CreateClientResponse.Validate if the designated constraints aren't met.
type CreateClientResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateClientResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateClientResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateClientResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateClientResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateClientResponseValidationError) ErrorName() string {
	return "CreateClientResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateClientResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateClientResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateClientResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateClientResponseValidationError{}

// Validate checks the field values on UpdateClientRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateClientRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateClientRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateClientRequestMultiError, or nil if none found.
func (m *UpdateClientRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateClientRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetClientId()) < 1 {
		err := UpdateClientRequestValidationError{
			field:  "ClientId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetBankAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateClientRequestValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateClientRequestValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateClientRequestValidationError{
				field:  "BankAccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateClientRequestMultiError(errors)
	}

	return nil
}

// UpdateClientRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateClientRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateClientRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateClientRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateClientRequestMultiError) AllErrors() []error { return m }

// UpdateClientRequestValidationError is the validation error returned by
// UpdateClientRequest.Validate if the designated constraints aren't met.
type UpdateClientRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateClientRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateClientRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateClientRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateClientRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateClientRequestValidationError) ErrorName() string {
	return "UpdateClientRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateClientRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateClientRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateClientRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateClientRequestValidationError{}

// Validate checks the field values on UpdateClientResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateClientResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateClientResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateClientResponseMultiError, or nil if none found.
func (m *UpdateClientResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateClientResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateClientResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateClientResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateClientResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateClientResponseMultiError(errors)
	}

	return nil
}

// UpdateClientResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateClientResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateClientResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateClientResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateClientResponseMultiError) AllErrors() []error { return m }

// UpdateClientResponseValidationError is the validation error returned by
// UpdateClientResponse.Validate if the designated constraints aren't met.
type UpdateClientResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateClientResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateClientResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateClientResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateClientResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateClientResponseValidationError) ErrorName() string {
	return "UpdateClientResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateClientResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateClientResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateClientResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateClientResponseValidationError{}

// Validate checks the field values on FetchClientRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchClientRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchClientRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchClientRequestMultiError, or nil if none found.
func (m *FetchClientRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchClientRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	oneofIdentifierPresent := false
	switch v := m.Identifier.(type) {
	case *FetchClientRequest_ClientId:
		if v == nil {
			err := FetchClientRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofIdentifierPresent = true
		// no validation rules for ClientId
	case *FetchClientRequest_ExternalId:
		if v == nil {
			err := FetchClientRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofIdentifierPresent = true
		// no validation rules for ExternalId
	default:
		_ = v // ensures v is used
	}
	if !oneofIdentifierPresent {
		err := FetchClientRequestValidationError{
			field:  "Identifier",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FetchClientRequestMultiError(errors)
	}

	return nil
}

// FetchClientRequestMultiError is an error wrapping multiple validation errors
// returned by FetchClientRequest.ValidateAll() if the designated constraints
// aren't met.
type FetchClientRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchClientRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchClientRequestMultiError) AllErrors() []error { return m }

// FetchClientRequestValidationError is the validation error returned by
// FetchClientRequest.Validate if the designated constraints aren't met.
type FetchClientRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchClientRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchClientRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchClientRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchClientRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchClientRequestValidationError) ErrorName() string {
	return "FetchClientRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchClientRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchClientRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchClientRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchClientRequestValidationError{}

// Validate checks the field values on FetchClientResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchClientResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchClientResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchClientResponseMultiError, or nil if none found.
func (m *FetchClientResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchClientResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchClientResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchClientResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchClientResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClient()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchClientResponseValidationError{
					field:  "Client",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchClientResponseValidationError{
					field:  "Client",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClient()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchClientResponseValidationError{
				field:  "Client",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchClientResponseMultiError(errors)
	}

	return nil
}

// FetchClientResponseMultiError is an error wrapping multiple validation
// errors returned by FetchClientResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchClientResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchClientResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchClientResponseMultiError) AllErrors() []error { return m }

// FetchClientResponseValidationError is the validation error returned by
// FetchClientResponse.Validate if the designated constraints aren't met.
type FetchClientResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchClientResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchClientResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchClientResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchClientResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchClientResponseValidationError) ErrorName() string {
	return "FetchClientResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchClientResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchClientResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchClientResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchClientResponseValidationError{}

// Validate checks the field values on FetchBankAccountsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchBankAccountsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchBankAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchBankAccountsRequestMultiError, or nil if none found.
func (m *FetchBankAccountsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchBankAccountsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetClientId()) < 1 {
		err := FetchBankAccountsRequestValidationError{
			field:  "ClientId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FetchBankAccountsRequestMultiError(errors)
	}

	return nil
}

// FetchBankAccountsRequestMultiError is an error wrapping multiple validation
// errors returned by FetchBankAccountsRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchBankAccountsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchBankAccountsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchBankAccountsRequestMultiError) AllErrors() []error { return m }

// FetchBankAccountsRequestValidationError is the validation error returned by
// FetchBankAccountsRequest.Validate if the designated constraints aren't met.
type FetchBankAccountsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchBankAccountsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchBankAccountsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchBankAccountsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchBankAccountsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchBankAccountsRequestValidationError) ErrorName() string {
	return "FetchBankAccountsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchBankAccountsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchBankAccountsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchBankAccountsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchBankAccountsRequestValidationError{}

// Validate checks the field values on FetchBankAccountsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchBankAccountsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchBankAccountsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchBankAccountsResponseMultiError, or nil if none found.
func (m *FetchBankAccountsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchBankAccountsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchBankAccountsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchBankAccountsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchBankAccountsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBankAccountDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FetchBankAccountsResponseValidationError{
						field:  fmt.Sprintf("BankAccountDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FetchBankAccountsResponseValidationError{
						field:  fmt.Sprintf("BankAccountDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FetchBankAccountsResponseValidationError{
					field:  fmt.Sprintf("BankAccountDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FetchBankAccountsResponseMultiError(errors)
	}

	return nil
}

// FetchBankAccountsResponseMultiError is an error wrapping multiple validation
// errors returned by FetchBankAccountsResponse.ValidateAll() if the
// designated constraints aren't met.
type FetchBankAccountsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchBankAccountsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchBankAccountsResponseMultiError) AllErrors() []error { return m }

// FetchBankAccountsResponseValidationError is the validation error returned by
// FetchBankAccountsResponse.Validate if the designated constraints aren't met.
type FetchBankAccountsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchBankAccountsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchBankAccountsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchBankAccountsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchBankAccountsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchBankAccountsResponseValidationError) ErrorName() string {
	return "FetchBankAccountsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchBankAccountsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchBankAccountsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchBankAccountsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchBankAccountsResponseValidationError{}

// Validate checks the field values on GetDocumentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDocumentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDocumentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDocumentRequestMultiError, or nil if none found.
func (m *GetDocumentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDocumentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.RequestType.(type) {
	case *GetDocumentRequest_LoanAccountStatement:
		if v == nil {
			err := GetDocumentRequestValidationError{
				field:  "RequestType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLoanAccountStatement()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetDocumentRequestValidationError{
						field:  "LoanAccountStatement",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetDocumentRequestValidationError{
						field:  "LoanAccountStatement",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLoanAccountStatement()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetDocumentRequestValidationError{
					field:  "LoanAccountStatement",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetDocumentRequest_Noc:
		if v == nil {
			err := GetDocumentRequestValidationError{
				field:  "RequestType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNoc()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetDocumentRequestValidationError{
						field:  "Noc",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetDocumentRequestValidationError{
						field:  "Noc",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNoc()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetDocumentRequestValidationError{
					field:  "Noc",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetDocumentRequestMultiError(errors)
	}

	return nil
}

// GetDocumentRequestMultiError is an error wrapping multiple validation errors
// returned by GetDocumentRequest.ValidateAll() if the designated constraints
// aren't met.
type GetDocumentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDocumentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDocumentRequestMultiError) AllErrors() []error { return m }

// GetDocumentRequestValidationError is the validation error returned by
// GetDocumentRequest.Validate if the designated constraints aren't met.
type GetDocumentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDocumentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDocumentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDocumentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDocumentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDocumentRequestValidationError) ErrorName() string {
	return "GetDocumentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDocumentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDocumentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDocumentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDocumentRequestValidationError{}

// Validate checks the field values on LoanAccountStatement with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanAccountStatement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanAccountStatement with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanAccountStatementMultiError, or nil if none found.
func (m *LoanAccountStatement) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanAccountStatement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanId

	if all {
		switch v := interface{}(m.GetEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanAccountStatementValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanAccountStatementValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanAccountStatementValidationError{
				field:  "EndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanAccountStatementMultiError(errors)
	}

	return nil
}

// LoanAccountStatementMultiError is an error wrapping multiple validation
// errors returned by LoanAccountStatement.ValidateAll() if the designated
// constraints aren't met.
type LoanAccountStatementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanAccountStatementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanAccountStatementMultiError) AllErrors() []error { return m }

// LoanAccountStatementValidationError is the validation error returned by
// LoanAccountStatement.Validate if the designated constraints aren't met.
type LoanAccountStatementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanAccountStatementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanAccountStatementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanAccountStatementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanAccountStatementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanAccountStatementValidationError) ErrorName() string {
	return "LoanAccountStatementValidationError"
}

// Error satisfies the builtin error interface
func (e LoanAccountStatementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanAccountStatement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanAccountStatementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanAccountStatementValidationError{}

// Validate checks the field values on NoObjectionCertificate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoObjectionCertificate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoObjectionCertificate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoObjectionCertificateMultiError, or nil if none found.
func (m *NoObjectionCertificate) ValidateAll() error {
	return m.validate(true)
}

func (m *NoObjectionCertificate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanId

	if len(errors) > 0 {
		return NoObjectionCertificateMultiError(errors)
	}

	return nil
}

// NoObjectionCertificateMultiError is an error wrapping multiple validation
// errors returned by NoObjectionCertificate.ValidateAll() if the designated
// constraints aren't met.
type NoObjectionCertificateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoObjectionCertificateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoObjectionCertificateMultiError) AllErrors() []error { return m }

// NoObjectionCertificateValidationError is the validation error returned by
// NoObjectionCertificate.Validate if the designated constraints aren't met.
type NoObjectionCertificateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoObjectionCertificateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoObjectionCertificateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoObjectionCertificateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoObjectionCertificateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoObjectionCertificateValidationError) ErrorName() string {
	return "NoObjectionCertificateValidationError"
}

// Error satisfies the builtin error interface
func (e NoObjectionCertificateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoObjectionCertificate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoObjectionCertificateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoObjectionCertificateValidationError{}

// Validate checks the field values on GetDocumentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDocumentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDocumentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDocumentResponseMultiError, or nil if none found.
func (m *GetDocumentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDocumentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDocumentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDocumentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDocumentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Base64Document

	if len(errors) > 0 {
		return GetDocumentResponseMultiError(errors)
	}

	return nil
}

// GetDocumentResponseMultiError is an error wrapping multiple validation
// errors returned by GetDocumentResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDocumentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDocumentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDocumentResponseMultiError) AllErrors() []error { return m }

// GetDocumentResponseValidationError is the validation error returned by
// GetDocumentResponse.Validate if the designated constraints aren't met.
type GetDocumentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDocumentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDocumentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDocumentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDocumentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDocumentResponseValidationError) ErrorName() string {
	return "GetDocumentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDocumentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDocumentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDocumentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDocumentResponseValidationError{}

// Validate checks the field values on FetchProductResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchProductResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchProductResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchProductResponseMultiError, or nil if none found.
func (m *FetchProductResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchProductResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchProductResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchProductResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchProductResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProductDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchProductResponseValidationError{
					field:  "ProductDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchProductResponseValidationError{
					field:  "ProductDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProductDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchProductResponseValidationError{
				field:  "ProductDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchProductResponseMultiError(errors)
	}

	return nil
}

// FetchProductResponseMultiError is an error wrapping multiple validation
// errors returned by FetchProductResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchProductResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchProductResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchProductResponseMultiError) AllErrors() []error { return m }

// FetchProductResponseValidationError is the validation error returned by
// FetchProductResponse.Validate if the designated constraints aren't met.
type FetchProductResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchProductResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchProductResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchProductResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchProductResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchProductResponseValidationError) ErrorName() string {
	return "FetchProductResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchProductResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchProductResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchProductResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchProductResponseValidationError{}

// Validate checks the field values on UpdateClientRequest_BankAccountDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateClientRequest_BankAccountDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateClientRequest_BankAccountDetails with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// UpdateClientRequest_BankAccountDetailsMultiError, or nil if none found.
func (m *UpdateClientRequest_BankAccountDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateClientRequest_BankAccountDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountNumber

	// no validation rules for IfscCode

	// no validation rules for AccountHolderName

	if len(errors) > 0 {
		return UpdateClientRequest_BankAccountDetailsMultiError(errors)
	}

	return nil
}

// UpdateClientRequest_BankAccountDetailsMultiError is an error wrapping
// multiple validation errors returned by
// UpdateClientRequest_BankAccountDetails.ValidateAll() if the designated
// constraints aren't met.
type UpdateClientRequest_BankAccountDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateClientRequest_BankAccountDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateClientRequest_BankAccountDetailsMultiError) AllErrors() []error { return m }

// UpdateClientRequest_BankAccountDetailsValidationError is the validation
// error returned by UpdateClientRequest_BankAccountDetails.Validate if the
// designated constraints aren't met.
type UpdateClientRequest_BankAccountDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateClientRequest_BankAccountDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateClientRequest_BankAccountDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateClientRequest_BankAccountDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateClientRequest_BankAccountDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateClientRequest_BankAccountDetailsValidationError) ErrorName() string {
	return "UpdateClientRequest_BankAccountDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateClientRequest_BankAccountDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateClientRequest_BankAccountDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateClientRequest_BankAccountDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateClientRequest_BankAccountDetailsValidationError{}

// Validate checks the field values on
// FetchBankAccountsResponse_BankAccountDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FetchBankAccountsResponse_BankAccountDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FetchBankAccountsResponse_BankAccountDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FetchBankAccountsResponse_BankAccountDetailsMultiError, or nil if none found.
func (m *FetchBankAccountsResponse_BankAccountDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchBankAccountsResponse_BankAccountDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for AccountNumber

	// no validation rules for IfscCode

	if len(errors) > 0 {
		return FetchBankAccountsResponse_BankAccountDetailsMultiError(errors)
	}

	return nil
}

// FetchBankAccountsResponse_BankAccountDetailsMultiError is an error wrapping
// multiple validation errors returned by
// FetchBankAccountsResponse_BankAccountDetails.ValidateAll() if the
// designated constraints aren't met.
type FetchBankAccountsResponse_BankAccountDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchBankAccountsResponse_BankAccountDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchBankAccountsResponse_BankAccountDetailsMultiError) AllErrors() []error { return m }

// FetchBankAccountsResponse_BankAccountDetailsValidationError is the
// validation error returned by
// FetchBankAccountsResponse_BankAccountDetails.Validate if the designated
// constraints aren't met.
type FetchBankAccountsResponse_BankAccountDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchBankAccountsResponse_BankAccountDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchBankAccountsResponse_BankAccountDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchBankAccountsResponse_BankAccountDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchBankAccountsResponse_BankAccountDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchBankAccountsResponse_BankAccountDetailsValidationError) ErrorName() string {
	return "FetchBankAccountsResponse_BankAccountDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FetchBankAccountsResponse_BankAccountDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchBankAccountsResponse_BankAccountDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchBankAccountsResponse_BankAccountDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchBankAccountsResponse_BankAccountDetailsValidationError{}
