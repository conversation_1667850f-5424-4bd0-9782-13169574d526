// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/stockguardian/sgvendorgateway/pan/service.proto

package pan

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ProfilePANRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ProfilePANRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProfilePANRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProfilePANRequestMultiError, or nil if none found.
func (m *ProfilePANRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProfilePANRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfilePANRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfilePANRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfilePANRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Pan

	if len(errors) > 0 {
		return ProfilePANRequestMultiError(errors)
	}

	return nil
}

// ProfilePANRequestMultiError is an error wrapping multiple validation errors
// returned by ProfilePANRequest.ValidateAll() if the designated constraints
// aren't met.
type ProfilePANRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProfilePANRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProfilePANRequestMultiError) AllErrors() []error { return m }

// ProfilePANRequestValidationError is the validation error returned by
// ProfilePANRequest.Validate if the designated constraints aren't met.
type ProfilePANRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProfilePANRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProfilePANRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProfilePANRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProfilePANRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProfilePANRequestValidationError) ErrorName() string {
	return "ProfilePANRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProfilePANRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProfilePANRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProfilePANRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProfilePANRequestValidationError{}

// Validate checks the field values on ProfilePANResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProfilePANResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProfilePANResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProfilePANResponseMultiError, or nil if none found.
func (m *ProfilePANResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProfilePANResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfilePANResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfilePANResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfilePANResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPanName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfilePANResponseValidationError{
					field:  "PanName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfilePANResponseValidationError{
					field:  "PanName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPanName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfilePANResponseValidationError{
				field:  "PanName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfilePANResponseValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfilePANResponseValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfilePANResponseValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PanStatus

	if len(errors) > 0 {
		return ProfilePANResponseMultiError(errors)
	}

	return nil
}

// ProfilePANResponseMultiError is an error wrapping multiple validation errors
// returned by ProfilePANResponse.ValidateAll() if the designated constraints
// aren't met.
type ProfilePANResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProfilePANResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProfilePANResponseMultiError) AllErrors() []error { return m }

// ProfilePANResponseValidationError is the validation error returned by
// ProfilePANResponse.Validate if the designated constraints aren't met.
type ProfilePANResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProfilePANResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProfilePANResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProfilePANResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProfilePANResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProfilePANResponseValidationError) ErrorName() string {
	return "ProfilePANResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProfilePANResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProfilePANResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProfilePANResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProfilePANResponseValidationError{}

// Validate checks the field values on PerformPANInquiryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerformPANInquiryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerformPANInquiryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerformPANInquiryRequestMultiError, or nil if none found.
func (m *PerformPANInquiryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PerformPANInquiryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerformPANInquiryRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerformPANInquiryRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerformPANInquiryRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetInquiriesData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerformPANInquiryRequestValidationError{
						field:  fmt.Sprintf("InquiriesData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerformPANInquiryRequestValidationError{
						field:  fmt.Sprintf("InquiriesData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerformPANInquiryRequestValidationError{
					field:  fmt.Sprintf("InquiriesData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PerformPANInquiryRequestMultiError(errors)
	}

	return nil
}

// PerformPANInquiryRequestMultiError is an error wrapping multiple validation
// errors returned by PerformPANInquiryRequest.ValidateAll() if the designated
// constraints aren't met.
type PerformPANInquiryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerformPANInquiryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerformPANInquiryRequestMultiError) AllErrors() []error { return m }

// PerformPANInquiryRequestValidationError is the validation error returned by
// PerformPANInquiryRequest.Validate if the designated constraints aren't met.
type PerformPANInquiryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerformPANInquiryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerformPANInquiryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerformPANInquiryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerformPANInquiryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerformPANInquiryRequestValidationError) ErrorName() string {
	return "PerformPANInquiryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PerformPANInquiryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerformPANInquiryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerformPANInquiryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerformPANInquiryRequestValidationError{}

// Validate checks the field values on PanInquiryData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PanInquiryData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PanInquiryData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PanInquiryDataMultiError,
// or nil if none found.
func (m *PanInquiryData) ValidateAll() error {
	return m.validate(true)
}

func (m *PanInquiryData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Pan

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PanInquiryDataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PanInquiryDataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PanInquiryDataValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFatherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PanInquiryDataValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PanInquiryDataValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFatherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PanInquiryDataValidationError{
				field:  "FatherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PanInquiryDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PanInquiryDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PanInquiryDataValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PanInquiryDataMultiError(errors)
	}

	return nil
}

// PanInquiryDataMultiError is an error wrapping multiple validation errors
// returned by PanInquiryData.ValidateAll() if the designated constraints
// aren't met.
type PanInquiryDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PanInquiryDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PanInquiryDataMultiError) AllErrors() []error { return m }

// PanInquiryDataValidationError is the validation error returned by
// PanInquiryData.Validate if the designated constraints aren't met.
type PanInquiryDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PanInquiryDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PanInquiryDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PanInquiryDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PanInquiryDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PanInquiryDataValidationError) ErrorName() string { return "PanInquiryDataValidationError" }

// Error satisfies the builtin error interface
func (e PanInquiryDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPanInquiryData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PanInquiryDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PanInquiryDataValidationError{}

// Validate checks the field values on PerformPANInquiryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerformPANInquiryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerformPANInquiryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerformPANInquiryResponseMultiError, or nil if none found.
func (m *PerformPANInquiryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PerformPANInquiryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerformPANInquiryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerformPANInquiryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerformPANInquiryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetInquiryResults() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerformPANInquiryResponseValidationError{
						field:  fmt.Sprintf("InquiryResults[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerformPANInquiryResponseValidationError{
						field:  fmt.Sprintf("InquiryResults[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerformPANInquiryResponseValidationError{
					field:  fmt.Sprintf("InquiryResults[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PerformPANInquiryResponseMultiError(errors)
	}

	return nil
}

// PerformPANInquiryResponseMultiError is an error wrapping multiple validation
// errors returned by PerformPANInquiryResponse.ValidateAll() if the
// designated constraints aren't met.
type PerformPANInquiryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerformPANInquiryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerformPANInquiryResponseMultiError) AllErrors() []error { return m }

// PerformPANInquiryResponseValidationError is the validation error returned by
// PerformPANInquiryResponse.Validate if the designated constraints aren't met.
type PerformPANInquiryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerformPANInquiryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerformPANInquiryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerformPANInquiryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerformPANInquiryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerformPANInquiryResponseValidationError) ErrorName() string {
	return "PerformPANInquiryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PerformPANInquiryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerformPANInquiryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerformPANInquiryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerformPANInquiryResponseValidationError{}

// Validate checks the field values on PanInquiryResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PanInquiryResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PanInquiryResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PanInquiryResultMultiError, or nil if none found.
func (m *PanInquiryResult) ValidateAll() error {
	return m.validate(true)
}

func (m *PanInquiryResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Pan

	// no validation rules for PanStatus

	// no validation rules for AadhaarStatus

	// no validation rules for NameMatched

	// no validation rules for FatherNameMatched

	// no validation rules for DobMatched

	if len(errors) > 0 {
		return PanInquiryResultMultiError(errors)
	}

	return nil
}

// PanInquiryResultMultiError is an error wrapping multiple validation errors
// returned by PanInquiryResult.ValidateAll() if the designated constraints
// aren't met.
type PanInquiryResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PanInquiryResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PanInquiryResultMultiError) AllErrors() []error { return m }

// PanInquiryResultValidationError is the validation error returned by
// PanInquiryResult.Validate if the designated constraints aren't met.
type PanInquiryResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PanInquiryResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PanInquiryResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PanInquiryResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PanInquiryResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PanInquiryResultValidationError) ErrorName() string { return "PanInquiryResultValidationError" }

// Error satisfies the builtin error interface
func (e PanInquiryResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPanInquiryResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PanInquiryResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PanInquiryResultValidationError{}
