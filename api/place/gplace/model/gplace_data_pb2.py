# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/place/gplace/model/gplace_data.proto

from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

from api.vendorgateway.gplace import gplace_pb2 as api_dot_vendorgateway_dot_gplace_dot_gplace__pb2

DESCRIPTOR = _descriptor.FileDescriptor(
    name="api/place/gplace/model/gplace_data.proto",
    package="place.gplace.model",
    syntax="proto3",
    serialized_options=b"\n-com.github.epifi.gamma.api.place.gplace,modelZ-github.com/epifi/gamma/api/place/gplace/model",
    create_key=_descriptor._internal_create_key,
    serialized_pb=b'\n(api/place/gplace/model/gplace_data.proto\x12\x12place.gplace.model\x1a\x1fgoogle/protobuf/timestamp.proto\x1a%api/vendorgateway/gplace/gplace.proto"\xb0\x02\n\nGPlaceData\x12\n\n\x02id\x18\x01 \x01(\t\x12\x12\n\nplace_name\x18\x02 \x01(\t\x12\x39\n\rlocation_bias\x18\x03 \x01(\x0b\x32".vendorgateway.gplace.LocationBias\x12\x10\n\x08place_id\x18\x04 \x01(\t\x12%\n\x1dplace_name_location_bias_hash\x18\x05 \x01(\t\x12.\n\ncreated_at\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ndeleted_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.TimestampB^\n-com.github.epifi.gamma.api.place.gplace,modelZ-github.com/epifi/gamma/api/place/gplace/modelb\x06proto3',
    dependencies=[
        google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,
        api_dot_vendorgateway_dot_gplace_dot_gplace__pb2.DESCRIPTOR,
    ],
)


_GPLACEDATA = _descriptor.Descriptor(
    name="GPlaceData",
    full_name="place.gplace.model.GPlaceData",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="id",
            full_name="place.gplace.model.GPlaceData.id",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="place_name",
            full_name="place.gplace.model.GPlaceData.place_name",
            index=1,
            number=2,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="location_bias",
            full_name="place.gplace.model.GPlaceData.location_bias",
            index=2,
            number=3,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="place_id",
            full_name="place.gplace.model.GPlaceData.place_id",
            index=3,
            number=4,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="place_name_location_bias_hash",
            full_name="place.gplace.model.GPlaceData.place_name_location_bias_hash",
            index=4,
            number=5,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="created_at",
            full_name="place.gplace.model.GPlaceData.created_at",
            index=5,
            number=6,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="updated_at",
            full_name="place.gplace.model.GPlaceData.updated_at",
            index=6,
            number=7,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="deleted_at",
            full_name="place.gplace.model.GPlaceData.deleted_at",
            index=7,
            number=8,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=137,
    serialized_end=441,
)

_GPLACEDATA.fields_by_name[
    "location_bias"
].message_type = api_dot_vendorgateway_dot_gplace_dot_gplace__pb2._LOCATIONBIAS
_GPLACEDATA.fields_by_name[
    "created_at"
].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GPLACEDATA.fields_by_name[
    "updated_at"
].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GPLACEDATA.fields_by_name[
    "deleted_at"
].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name["GPlaceData"] = _GPLACEDATA
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GPlaceData = _reflection.GeneratedProtocolMessageType(
    "GPlaceData",
    (_message.Message,),
    {
        "DESCRIPTOR": _GPLACEDATA,
        "__module__": "api.place.gplace.model.gplace_data_pb2"
        # @@protoc_insertion_point(class_scope:place.gplace.model.GPlaceData)
    },
)
_sym_db.RegisterMessage(GPlaceData)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)
