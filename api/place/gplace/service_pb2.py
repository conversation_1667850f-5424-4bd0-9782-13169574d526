# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/place/gplace/service.proto

from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from api.rpc import status_pb2 as api_dot_rpc_dot_status__pb2
from api.vendorgateway.gplace import enums_pb2 as api_dot_vendorgateway_dot_gplace_dot_enums__pb2
from api.vendorgateway.gplace import gplace_pb2 as api_dot_vendorgateway_dot_gplace_dot_gplace__pb2
from validate import validate_pb2 as validate_dot_validate__pb2

DESCRIPTOR = _descriptor.FileDescriptor(
    name="api/place/gplace/service.proto",
    package="place.gplace",
    syntax="proto3",
    serialized_options=b"\n'com.github.epifi.gamma.api.place.gplaceZ'github.com/epifi/gamma/api/place/gplace",
    create_key=_descriptor._internal_create_key,
    serialized_pb=b'\n\x1e\x61pi/place/gplace/service.proto\x12\x0cplace.gplace\x1a\x14\x61pi/rpc/status.proto\x1a$api/vendorgateway/gplace/enums.proto\x1a%api/vendorgateway/gplace/gplace.proto\x1a\x17validate/validate.proto"\xb5\x01\n\x1cGetGPlaceBatchDetailsRequest\x12N\n\x17place_name_and_location\x18\x01 \x03(\x0b\x32".place.gplace.PlaceNameAndLocationB\t\xfa\x42\x06\x92\x01\x03\x10\xe8\x07\x12\x45\n\x10requested_fields\x18\x02 \x03(\x0e\x32!.vendorgateway.gplace.BasicFieldsB\x08\xfa\x42\x05\x92\x01\x02\x08\x01"|\n\x1dGetGPlaceBatchDetailsResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12>\n\x0e\x62\x61tch_response\x18\x02 \x03(\x0b\x32&.place.gplace.GetGPlaceDetailsResponse"\x8a\x04\n\x18GetGPlaceDetailsResponse\x12=\n\x06status\x18\x01 \x01(\x0e\x32-.place.gplace.GetGPlaceDetailsResponse.Status\x12=\n\x0f\x62usiness_status\x18\x02 \x01(\x0e\x32$.vendorgateway.gplace.BusinessStatus\x12\x30\n\x08geometry\x18\x03 \x01(\x0b\x32\x1e.vendorgateway.gplace.Geometry\x12\x19\n\x11\x66ormatted_address\x18\x04 \x01(\t\x12\x19\n\x11\x63\x61tegory_icon_url\x18\x05 \x01(\t\x12\x18\n\x10\x63lean_place_name\x18\x06 \x01(\t\x12\x10\n\x08place_id\x18\x07 \x01(\t\x12/\n\x05types\x18\x08 \x03(\x0e\x32 .vendorgateway.gplace.GPlaceType\x12\x1a\n\x12icon_mask_base_uri\x18\t \x01(\t\x12\x1d\n\x15icon_background_color\x18\n \x01(\t"p\n\x06Status\x12\x06\n\x02OK\x10\x00\x12\x14\n\x10INVALID_ARGUMENT\x10\x03\x12\r\n\tNOT_FOUND\x10\x05\x12\x16\n\x12RESOURCE_EXHAUSTED\x10\x08\x12\x0c\n\x08INTERNAL\x10\r\x12\x13\n\x0fUNAUTHENTICATED\x10\x10"e\n\x14PlaceNameAndLocation\x12\x12\n\nplace_name\x18\x01 \x01(\t\x12\x39\n\rlocation_bias\x18\x02 \x01(\x0b\x32".vendorgateway.gplace.LocationBias2|\n\x06GPlace\x12r\n\x15GetGPlaceBatchDetails\x12*.place.gplace.GetGPlaceBatchDetailsRequest\x1a+.place.gplace.GetGPlaceBatchDetailsResponse"\x00\x42R\n\'com.github.epifi.gamma.api.place.gplaceZ\'github.com/epifi/gamma/api/place/gplaceb\x06proto3',
    dependencies=[
        api_dot_rpc_dot_status__pb2.DESCRIPTOR,
        api_dot_vendorgateway_dot_gplace_dot_enums__pb2.DESCRIPTOR,
        api_dot_vendorgateway_dot_gplace_dot_gplace__pb2.DESCRIPTOR,
        validate_dot_validate__pb2.DESCRIPTOR,
    ],
)


_GETGPLACEDETAILSRESPONSE_STATUS = _descriptor.EnumDescriptor(
    name="Status",
    full_name="place.gplace.GetGPlaceDetailsResponse.Status",
    filename=None,
    file=DESCRIPTOR,
    create_key=_descriptor._internal_create_key,
    values=[
        _descriptor.EnumValueDescriptor(
            name="OK",
            index=0,
            number=0,
            serialized_options=None,
            type=None,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.EnumValueDescriptor(
            name="INVALID_ARGUMENT",
            index=1,
            number=3,
            serialized_options=None,
            type=None,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.EnumValueDescriptor(
            name="NOT_FOUND",
            index=2,
            number=5,
            serialized_options=None,
            type=None,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.EnumValueDescriptor(
            name="RESOURCE_EXHAUSTED",
            index=3,
            number=8,
            serialized_options=None,
            type=None,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.EnumValueDescriptor(
            name="INTERNAL",
            index=4,
            number=13,
            serialized_options=None,
            type=None,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.EnumValueDescriptor(
            name="UNAUTHENTICATED",
            index=5,
            number=16,
            serialized_options=None,
            type=None,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    containing_type=None,
    serialized_options=None,
    serialized_start=893,
    serialized_end=1005,
)
_sym_db.RegisterEnumDescriptor(_GETGPLACEDETAILSRESPONSE_STATUS)


_GETGPLACEBATCHDETAILSREQUEST = _descriptor.Descriptor(
    name="GetGPlaceBatchDetailsRequest",
    full_name="place.gplace.GetGPlaceBatchDetailsRequest",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="place_name_and_location",
            full_name="place.gplace.GetGPlaceBatchDetailsRequest.place_name_and_location",
            index=0,
            number=1,
            type=11,
            cpp_type=10,
            label=3,
            has_default_value=False,
            default_value=[],
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=b"\372B\006\222\001\003\020\350\007",
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="requested_fields",
            full_name="place.gplace.GetGPlaceBatchDetailsRequest.requested_fields",
            index=1,
            number=2,
            type=14,
            cpp_type=8,
            label=3,
            has_default_value=False,
            default_value=[],
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=b"\372B\005\222\001\002\010\001",
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=173,
    serialized_end=354,
)


_GETGPLACEBATCHDETAILSRESPONSE = _descriptor.Descriptor(
    name="GetGPlaceBatchDetailsResponse",
    full_name="place.gplace.GetGPlaceBatchDetailsResponse",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="status",
            full_name="place.gplace.GetGPlaceBatchDetailsResponse.status",
            index=0,
            number=1,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="batch_response",
            full_name="place.gplace.GetGPlaceBatchDetailsResponse.batch_response",
            index=1,
            number=2,
            type=11,
            cpp_type=10,
            label=3,
            has_default_value=False,
            default_value=[],
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=356,
    serialized_end=480,
)


_GETGPLACEDETAILSRESPONSE = _descriptor.Descriptor(
    name="GetGPlaceDetailsResponse",
    full_name="place.gplace.GetGPlaceDetailsResponse",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="status",
            full_name="place.gplace.GetGPlaceDetailsResponse.status",
            index=0,
            number=1,
            type=14,
            cpp_type=8,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="business_status",
            full_name="place.gplace.GetGPlaceDetailsResponse.business_status",
            index=1,
            number=2,
            type=14,
            cpp_type=8,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="geometry",
            full_name="place.gplace.GetGPlaceDetailsResponse.geometry",
            index=2,
            number=3,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="formatted_address",
            full_name="place.gplace.GetGPlaceDetailsResponse.formatted_address",
            index=3,
            number=4,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="category_icon_url",
            full_name="place.gplace.GetGPlaceDetailsResponse.category_icon_url",
            index=4,
            number=5,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="clean_place_name",
            full_name="place.gplace.GetGPlaceDetailsResponse.clean_place_name",
            index=5,
            number=6,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="place_id",
            full_name="place.gplace.GetGPlaceDetailsResponse.place_id",
            index=6,
            number=7,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="types",
            full_name="place.gplace.GetGPlaceDetailsResponse.types",
            index=7,
            number=8,
            type=14,
            cpp_type=8,
            label=3,
            has_default_value=False,
            default_value=[],
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="icon_mask_base_uri",
            full_name="place.gplace.GetGPlaceDetailsResponse.icon_mask_base_uri",
            index=8,
            number=9,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="icon_background_color",
            full_name="place.gplace.GetGPlaceDetailsResponse.icon_background_color",
            index=9,
            number=10,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[
        _GETGPLACEDETAILSRESPONSE_STATUS,
    ],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=483,
    serialized_end=1005,
)


_PLACENAMEANDLOCATION = _descriptor.Descriptor(
    name="PlaceNameAndLocation",
    full_name="place.gplace.PlaceNameAndLocation",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="place_name",
            full_name="place.gplace.PlaceNameAndLocation.place_name",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="location_bias",
            full_name="place.gplace.PlaceNameAndLocation.location_bias",
            index=1,
            number=2,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=1007,
    serialized_end=1108,
)

_GETGPLACEBATCHDETAILSREQUEST.fields_by_name[
    "place_name_and_location"
].message_type = _PLACENAMEANDLOCATION
_GETGPLACEBATCHDETAILSREQUEST.fields_by_name[
    "requested_fields"
].enum_type = api_dot_vendorgateway_dot_gplace_dot_enums__pb2._BASICFIELDS
_GETGPLACEBATCHDETAILSRESPONSE.fields_by_name[
    "status"
].message_type = api_dot_rpc_dot_status__pb2._STATUS
_GETGPLACEBATCHDETAILSRESPONSE.fields_by_name[
    "batch_response"
].message_type = _GETGPLACEDETAILSRESPONSE
_GETGPLACEDETAILSRESPONSE.fields_by_name["status"].enum_type = _GETGPLACEDETAILSRESPONSE_STATUS
_GETGPLACEDETAILSRESPONSE.fields_by_name[
    "business_status"
].enum_type = api_dot_vendorgateway_dot_gplace_dot_enums__pb2._BUSINESSSTATUS
_GETGPLACEDETAILSRESPONSE.fields_by_name[
    "geometry"
].message_type = api_dot_vendorgateway_dot_gplace_dot_gplace__pb2._GEOMETRY
_GETGPLACEDETAILSRESPONSE.fields_by_name[
    "types"
].enum_type = api_dot_vendorgateway_dot_gplace_dot_enums__pb2._GPLACETYPE
_GETGPLACEDETAILSRESPONSE_STATUS.containing_type = _GETGPLACEDETAILSRESPONSE
_PLACENAMEANDLOCATION.fields_by_name[
    "location_bias"
].message_type = api_dot_vendorgateway_dot_gplace_dot_gplace__pb2._LOCATIONBIAS
DESCRIPTOR.message_types_by_name["GetGPlaceBatchDetailsRequest"] = _GETGPLACEBATCHDETAILSREQUEST
DESCRIPTOR.message_types_by_name["GetGPlaceBatchDetailsResponse"] = _GETGPLACEBATCHDETAILSRESPONSE
DESCRIPTOR.message_types_by_name["GetGPlaceDetailsResponse"] = _GETGPLACEDETAILSRESPONSE
DESCRIPTOR.message_types_by_name["PlaceNameAndLocation"] = _PLACENAMEANDLOCATION
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetGPlaceBatchDetailsRequest = _reflection.GeneratedProtocolMessageType(
    "GetGPlaceBatchDetailsRequest",
    (_message.Message,),
    {
        "DESCRIPTOR": _GETGPLACEBATCHDETAILSREQUEST,
        "__module__": "api.place.gplace.service_pb2"
        # @@protoc_insertion_point(class_scope:place.gplace.GetGPlaceBatchDetailsRequest)
    },
)
_sym_db.RegisterMessage(GetGPlaceBatchDetailsRequest)

GetGPlaceBatchDetailsResponse = _reflection.GeneratedProtocolMessageType(
    "GetGPlaceBatchDetailsResponse",
    (_message.Message,),
    {
        "DESCRIPTOR": _GETGPLACEBATCHDETAILSRESPONSE,
        "__module__": "api.place.gplace.service_pb2"
        # @@protoc_insertion_point(class_scope:place.gplace.GetGPlaceBatchDetailsResponse)
    },
)
_sym_db.RegisterMessage(GetGPlaceBatchDetailsResponse)

GetGPlaceDetailsResponse = _reflection.GeneratedProtocolMessageType(
    "GetGPlaceDetailsResponse",
    (_message.Message,),
    {
        "DESCRIPTOR": _GETGPLACEDETAILSRESPONSE,
        "__module__": "api.place.gplace.service_pb2"
        # @@protoc_insertion_point(class_scope:place.gplace.GetGPlaceDetailsResponse)
    },
)
_sym_db.RegisterMessage(GetGPlaceDetailsResponse)

PlaceNameAndLocation = _reflection.GeneratedProtocolMessageType(
    "PlaceNameAndLocation",
    (_message.Message,),
    {
        "DESCRIPTOR": _PLACENAMEANDLOCATION,
        "__module__": "api.place.gplace.service_pb2"
        # @@protoc_insertion_point(class_scope:place.gplace.PlaceNameAndLocation)
    },
)
_sym_db.RegisterMessage(PlaceNameAndLocation)


DESCRIPTOR._options = None
_GETGPLACEBATCHDETAILSREQUEST.fields_by_name["place_name_and_location"]._options = None
_GETGPLACEBATCHDETAILSREQUEST.fields_by_name["requested_fields"]._options = None

_GPLACE = _descriptor.ServiceDescriptor(
    name="GPlace",
    full_name="place.gplace.GPlace",
    file=DESCRIPTOR,
    index=0,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
    serialized_start=1110,
    serialized_end=1234,
    methods=[
        _descriptor.MethodDescriptor(
            name="GetGPlaceBatchDetails",
            full_name="place.gplace.GPlace.GetGPlaceBatchDetails",
            index=0,
            containing_service=None,
            input_type=_GETGPLACEBATCHDETAILSREQUEST,
            output_type=_GETGPLACEBATCHDETAILSRESPONSE,
            serialized_options=None,
            create_key=_descriptor._internal_create_key,
        ),
    ],
)
_sym_db.RegisterServiceDescriptor(_GPLACE)

DESCRIPTOR.services_by_name["GPlace"] = _GPLACE

# @@protoc_insertion_point(module_scope)
