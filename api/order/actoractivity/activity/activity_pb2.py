# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/order/actoractivity/activity/activity.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.type import money_pb2 as google_dot_type_dot_money__pb2

from api.accounts import account_type_pb2 as api_dot_accounts_dot_account__type__pb2
from api.order.actoractivity.enums import (
    activity_source_pb2 as api_dot_order_dot_actoractivity_dot_enums_dot_activity__source__pb2,
)
from api.order.actoractivity.enums import (
    activity_type_pb2 as api_dot_order_dot_actoractivity_dot_enums_dot_activity__type__pb2,
)

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n/api/order/actoractivity/activity/activity.proto\x12\x1corder.actoractivity.activity\x1a\x1f\x61pi/accounts/account_type.proto\x1a\x33\x61pi/order/actoractivity/enums/activity_source.proto\x1a\x31\x61pi/order/actoractivity/enums/activity_type.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17google/type/money.proto"\xf0\x05\n\x08\x41\x63tivity\x12\x10\n\x08icon_url\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12\x12\n\nshort_desc\x18\x03 \x01(\t\x12\x1b\n\x13short_desc_icon_url\x18\x04 \x01(\t\x12"\n\x06\x61mount\x18\x05 \x01(\x0b\x32\x12.google.type.Money\x12H\n\x0c\x61mount_badge\x18\x06 \x01(\x0e\x32\x32.order.actoractivity.activity.Activity.AmountBadge\x12\x36\n\x12\x61\x63tivity_timestamp\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x61\x63tivity_id\x18\x08 \x01(\t\x12\x17\n\x0fsecond_actor_id\x18\t \x01(\t\x12>\n\ractivity_type\x18\n \x01(\x0e\x32\'.order.actoractivity.enums.ActivityType\x12\x42\n\x0f\x61\x63tivity_source\x18\x0b \x01(\x0e\x32).order.actoractivity.enums.ActivitySource\x12\x15\n\x0btimeline_id\x18\x0c \x01(\tH\x00\x12\x65\n\x1a\x64\x65posit_account_identifier\x18\r \x01(\x0b\x32?.order.actoractivity.activity.Activity.DepositAccountIdentifierH\x00\x1aT\n\x18\x44\x65positAccountIdentifier\x12\x12\n\naccount_id\x18\x01 \x01(\t\x12$\n\x0c\x61\x63\x63ount_type\x18\x02 \x01(\x0e\x32\x0e.accounts.Type"O\n\x0b\x41mountBadge\x12\x1c\n\x18\x41MOUNT_BADGE_UNSPECIFIED\x10\x00\x12\t\n\x05\x44\x45\x42IT\x10\x01\x12\n\n\x06\x43REDIT\x10\x02\x12\x0b\n\x07SAVINGS\x10\x03\x42\x15\n\x13\x64\x65\x65plink_identifierBr\n7com.github.epifi.gamma.api.order.actoractivity.activityZ7github.com/epifi/gamma/api/order/actoractivity/activityb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "api.order.actoractivity.activity.activity_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n7com.github.epifi.gamma.api.order.actoractivity.activityZ7github.com/epifi/gamma/api/order/actoractivity/activity"
    _globals["_ACTIVITY"]._serialized_start = 277
    _globals["_ACTIVITY"]._serialized_end = 1029
    _globals["_ACTIVITY_DEPOSITACCOUNTIDENTIFIER"]._serialized_start = 841
    _globals["_ACTIVITY_DEPOSITACCOUNTIDENTIFIER"]._serialized_end = 925
    _globals["_ACTIVITY_AMOUNTBADGE"]._serialized_start = 927
    _globals["_ACTIVITY_AMOUNTBADGE"]._serialized_end = 1006
# @@protoc_insertion_point(module_scope)
