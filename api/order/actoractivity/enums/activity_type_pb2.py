# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/order/actoractivity/enums/activity_type.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n1api/order/actoractivity/enums/activity_type.proto\x12\x19order.actoractivity.enums*\xe1\x08\n\x0c\x41\x63tivityType\x12\x1d\n\x19\x41\x43TIVITY_TYPE_UNSPECIFIED\x10\x00\x12\x1c\n\x18IMPS_TRANSACTION_SUCCESS\x10\x01\x12\x1b\n\x17IMPS_TRANSACTION_FAILED\x10\x02\x12\x1c\n\x18NEFT_TRANSACTION_SUCCESS\x10\x03\x12\x1b\n\x17NEFT_TRANSACTION_FAILED\x10\x04\x12\x1c\n\x18RTGS_TRANSACTION_SUCCESS\x10\x05\x12\x1b\n\x17RTGS_TRANSACTION_FAILED\x10\x06\x12\x1b\n\x17UPI_TRANSACTION_SUCCESS\x10\x07\x12\x1a\n\x16UPI_TRANSACTION_FAILED\x10\x08\x12!\n\x1dINTRABANK_TRANSACTION_SUCCESS\x10\t\x12 \n\x1cINTRABANK_TRANSACTION_FAILED\x10\n\x12\x1b\n\x17\x41TM_TRANSACTION_SUCCESS\x10\x0b\x12\x1a\n\x16\x41TM_TRANSACTION_FAILED\x10\x0c\x12\x19\n\x15SMART_DEPOSIT_CREATED\x10\r\x12\x19\n\x15SMART_DEPOSIT_MATURED\x10\x0e\x12\x1b\n\x17SMART_DEPOSIT_PRECLOSED\x10\x0f\x12\x1b\n\x17SMART_DEPOSIT_AMT_ADDED\x10\x10\x12\x19\n\x15\x46IXED_DEPOSIT_CREATED\x10\x11\x12\x19\n\x15\x46IXED_DEPOSIT_MATURED\x10\x12\x12\x1b\n\x17\x46IXED_DEPOSIT_PRECLOSED\x10\x13\x12\x1d\n\x19IMPS_TRANSACTION_REVERSED\x10\x14\x12\x1d\n\x19NEFT_TRANSACTION_REVERSED\x10\x15\x12\x1d\n\x19RTGS_TRANSACTION_REVERSED\x10\x16\x12\x1c\n\x18UPI_TRANSACTION_REVERSED\x10\x17\x12"\n\x1eINTRABANK_TRANSACTION_REVERSED\x10\x18\x12\x1c\n\x18\x41TM_TRANSACTION_REVERSED\x10\x19\x12"\n\x1e\x44\x45\x42IT_CARD_TRANSACTION_SUCCESS\x10\x1a\x12!\n\x1d\x44\x45\x42IT_CARD_TRANSACTION_FAILED\x10\x1b\x12#\n\x1f\x44\x45\x42IT_CARD_TRANSACTION_REVERSED\x10\x1c\x12!\n\x1dSMART_DEPOSIT_INTEREST_CREDIT\x10\x1d\x12!\n\x1d\x46IXED_DEPOSIT_INTEREST_CREDIT\x10\x1e\x12\x1c\n\x18NEFT_TRANSACTION_PENDING\x10\x1f\x12\x1c\n\x18RTGS_TRANSACTION_PENDING\x10 \x12\x1b\n\x17UPI_TRANSACTION_PENDING\x10!\x12!\n\x1dINTRABANK_TRANSACTION_PENDING\x10"\x12\x1c\n\x18IMPS_TRANSACTION_PENDING\x10#Bl\n4com.github.epifi.gamma.api.order.actoractivity.enumsZ4github.com/epifi/gamma/api/order/actoractivity/enumsb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "api.order.actoractivity.enums.activity_type_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n4com.github.epifi.gamma.api.order.actoractivity.enumsZ4github.com/epifi/gamma/api/order/actoractivity/enums"
    _globals["_ACTIVITYTYPE"]._serialized_start = 81
    _globals["_ACTIVITYTYPE"]._serialized_end = 1202
# @@protoc_insertion_point(module_scope)
