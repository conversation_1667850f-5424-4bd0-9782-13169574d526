# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/order/actoractivity/enums/activity_source.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n3api/order/actoractivity/enums/activity_source.proto\x12\x19order.actoractivity.enums*\x89\x01\n\x0e\x41\x63tivitySource\x12\x1f\n\x1b\x41\x43TIVITY_SOURCE_UNSPECIFIED\x10\x00\x12!\n\x1d\x43ONNECTED_ACCOUNT_TRANSACTION\x10\x01\x12\x12\n\x0e\x46I_TRANSACTION\x10\x02\x12\x1f\n\x1b\x41\x43TIVITY_SOURCE_CREDIT_CARD\x10\x03\x42l\n4com.github.epifi.gamma.api.order.actoractivity.enumsZ4github.com/epifi/gamma/api/order/actoractivity/enumsb\x06proto3"
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "api.order.actoractivity.enums.activity_source_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n4com.github.epifi.gamma.api.order.actoractivity.enumsZ4github.com/epifi/gamma/api/order/actoractivity/enums"
    _globals["_ACTIVITYSOURCE"]._serialized_start = 83
    _globals["_ACTIVITYSOURCE"]._serialized_end = 220
# @@protoc_insertion_point(module_scope)
