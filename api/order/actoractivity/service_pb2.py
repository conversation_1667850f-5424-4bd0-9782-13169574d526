# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/order/actoractivity/service.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.type import money_pb2 as google_dot_type_dot_money__pb2

from api.accounts import account_type_pb2 as api_dot_accounts_dot_account__type__pb2
from api.order.actoractivity.activity import (
    activity_pb2 as api_dot_order_dot_actoractivity_dot_activity_dot_activity__pb2,
)
from api.order.actoractivity.enums import (
    activity_source_pb2 as api_dot_order_dot_actoractivity_dot_enums_dot_activity__source__pb2,
)
from api.order.payment import (
    accounting_entry_type_pb2 as api_dot_order_dot_payment_dot_accounting__entry__type__pb2,
)
from api.order.payment import (
    payment_protocol_pb2 as api_dot_order_dot_payment_dot_payment__protocol__pb2,
)
from api.rpc import status_pb2 as api_dot_rpc_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n%api/order/actoractivity/service.proto\x12\ractoractivity\x1a\x1f\x61pi/accounts/account_type.proto\x1a/api/order/actoractivity/activity/activity.proto\x1a\x33\x61pi/order/actoractivity/enums/activity_source.proto\x1a-api/order/payment/accounting_entry_type.proto\x1a(api/order/payment/payment_protocol.proto\x1a\x14\x61pi/rpc/status.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17google/type/money.proto\x1a\x17validate/validate.proto"\x8d\x08\n\x14GetActivitiesRequest\x12\x18\n\x10\x63urrent_actor_id\x18\x01 \x01(\t\x12I\n\x0e\x61\x63\x63ount_filter\x18\x02 \x03(\x0b\x32\x31.actoractivity.GetActivitiesRequest.AccountFilter\x12>\n\x1a\x61\x63tivities_start_timestamp\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1c\n\tpage_size\x18\x04 \x01(\x05\x42\t\xfa\x42\x06\x1a\x04\x18((\n\x12\x1b\n\x0f\x61\x63tivity_offset\x18\x05 \x01(\x05\x42\x02\x18\x01\x12\x12\n\ndescending\x18\x06 \x01(\x08\x12\x15\n\tpi_filter\x18\x07 \x03(\tB\x02\x18\x01\x12\x14\n\x0corder_offset\x18\x08 \x01(\x05\x12\x15\n\raa_txn_offset\x18\t \x01(\x05\x12<\n\x18\x61\x63tivities_end_timestamp\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12I\n\x0epayment_filter\x18\x0b \x01(\x0b\x32\x31.actoractivity.GetActivitiesRequest.PaymentFilter\x12:\n\x16page_landing_timestamp\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12L\n\x10\x65ntry_point_type\x18\r \x01(\x0e\x32\x32.actoractivity.GetActivitiesRequest.EntryPointType\x1aI\n\rAccountFilter\x12\x12\n\naccount_id\x18\x01 \x01(\t\x12$\n\x0c\x61\x63\x63ount_type\x18\x02 \x01(\x0e\x32\x0e.accounts.Type\x1a\xea\x01\n\rPaymentFilter\x12\x38\n\x10payment_protocol\x18\x01 \x03(\x0e\x32\x1e.order.payment.PaymentProtocol\x12\'\n\x0b\x66rom_amount\x18\x02 \x01(\x0b\x32\x12.google.type.Money\x12%\n\tto_amount\x18\x03 \x01(\x0b\x32\x12.google.type.Money\x12<\n\x10transaction_type\x18\x05 \x01(\x0e\x32".order.payment.AccountingEntryType\x12\x11\n\tpi_filter\x18\x06 \x03(\t"r\n\x0e\x45ntryPointType\x12 \n\x1c\x45NTRY_POINT_TYPE_UNSPECIFIED\x10\x00\x12\x17\n\x13\x45NTRY_POINT_TYPE_UI\x10\x01\x12%\n!ENTRY_POINT_TYPE_INTERNAL_SERVICE\x10\x02"\xbc\x10\n\x15GetActivitiesResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x41\n\nactivities\x18\x02 \x03(\x0b\x32-.actoractivity.GetActivitiesResponse.Activity\x1a\x8c\x0f\n\x08\x41\x63tivity\x12\x10\n\x08icon_url\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12\x12\n\nshort_desc\x18\x03 \x01(\t\x12\x1b\n\x13short_desc_icon_url\x18\x04 \x01(\t\x12"\n\x06\x61mount\x18\x05 \x01(\x0b\x32\x12.google.type.Money\x12O\n\x0c\x61mount_badge\x18\x06 \x01(\x0e\x32\x39.actoractivity.GetActivitiesResponse.Activity.AmountBadge\x12\x36\n\x12\x61\x63tivity_timestamp\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x61\x63tivity_id\x18\x08 \x01(\t\x12\x17\n\x0fsecond_actor_id\x18\t \x01(\t\x12\x15\n\x0btimeline_id\x18\n \x01(\tH\x00\x12l\n\x1a\x64\x65posit_account_identifier\x18\x0b \x01(\x0b\x32\x46.actoractivity.GetActivitiesResponse.Activity.DepositAccountIdentifierH\x00\x12I\n\ractivity_type\x18\x0c \x01(\x0e\x32\x32.actoractivity.GetActivitiesResponse.Activity.Type\x12?\n\x14\x61\x63tivity_entry_point\x18\r \x01(\x0e\x32!.actoractivity.ActivityEntryPoint\x1aT\n\x18\x44\x65positAccountIdentifier\x12\x12\n\naccount_id\x18\x01 \x01(\t\x12$\n\x0c\x61\x63\x63ount_type\x18\x02 \x01(\x0e\x32\x0e.accounts.Type"y\n\x0b\x41mountBadge\x12\x1c\n\x18\x41MOUNT_BADGE_UNSPECIFIED\x10\x00\x12\t\n\x05\x44\x45\x42IT\x10\x01\x12\n\n\x06\x43REDIT\x10\x02\x12\x0b\n\x07SAVINGS\x10\x03\x12\n\n\x06\x46\x41ILED\x10\x04\x12\x0c\n\x08REVERSED\x10\x05\x12\x0e\n\nIN_PAYMENT\x10\x06"\xd9\x08\n\x04Type\x12\x1d\n\x19\x41\x43TIVITY_TYPE_UNSPECIFIED\x10\x00\x12\x1c\n\x18IMPS_TRANSACTION_SUCCESS\x10\x01\x12\x1b\n\x17IMPS_TRANSACTION_FAILED\x10\x02\x12\x1c\n\x18NEFT_TRANSACTION_SUCCESS\x10\x03\x12\x1b\n\x17NEFT_TRANSACTION_FAILED\x10\x04\x12\x1c\n\x18RTGS_TRANSACTION_SUCCESS\x10\x05\x12\x1b\n\x17RTGS_TRANSACTION_FAILED\x10\x06\x12\x1b\n\x17UPI_TRANSACTION_SUCCESS\x10\x07\x12\x1a\n\x16UPI_TRANSACTION_FAILED\x10\x08\x12!\n\x1dINTRABANK_TRANSACTION_SUCCESS\x10\t\x12 \n\x1cINTRABANK_TRANSACTION_FAILED\x10\n\x12\x1b\n\x17\x41TM_TRANSACTION_SUCCESS\x10\x0b\x12\x1a\n\x16\x41TM_TRANSACTION_FAILED\x10\x0c\x12\x19\n\x15SMART_DEPOSIT_CREATED\x10\r\x12\x19\n\x15SMART_DEPOSIT_MATURED\x10\x0e\x12\x1b\n\x17SMART_DEPOSIT_PRECLOSED\x10\x0f\x12\x1b\n\x17SMART_DEPOSIT_AMT_ADDED\x10\x10\x12\x19\n\x15\x46IXED_DEPOSIT_CREATED\x10\x11\x12\x19\n\x15\x46IXED_DEPOSIT_MATURED\x10\x12\x12\x1b\n\x17\x46IXED_DEPOSIT_PRECLOSED\x10\x13\x12\x1d\n\x19IMPS_TRANSACTION_REVERSED\x10\x14\x12\x1d\n\x19NEFT_TRANSACTION_REVERSED\x10\x15\x12\x1d\n\x19RTGS_TRANSACTION_REVERSED\x10\x16\x12\x1c\n\x18UPI_TRANSACTION_REVERSED\x10\x17\x12"\n\x1eINTRABANK_TRANSACTION_REVERSED\x10\x18\x12\x1c\n\x18\x41TM_TRANSACTION_REVERSED\x10\x19\x12"\n\x1e\x44\x45\x42IT_CARD_TRANSACTION_SUCCESS\x10\x1a\x12!\n\x1d\x44\x45\x42IT_CARD_TRANSACTION_FAILED\x10\x1b\x12#\n\x1f\x44\x45\x42IT_CARD_TRANSACTION_REVERSED\x10\x1c\x12!\n\x1dSMART_DEPOSIT_INTEREST_CREDIT\x10\x1d\x12!\n\x1d\x46IXED_DEPOSIT_INTEREST_CREDIT\x10\x1e\x12\x1c\n\x18NEFT_TRANSACTION_PENDING\x10\x1f\x12\x1c\n\x18RTGS_TRANSACTION_PENDING\x10 \x12\x1b\n\x17UPI_TRANSACTION_PENDING\x10!\x12!\n\x1dINTRABANK_TRANSACTION_PENDING\x10"\x12\x1c\n\x18IMPS_TRANSACTION_PENDING\x10#B\x15\n\x13\x64\x65\x65plink_identifier"4\n\x06Status\x12\x06\n\x02OK\x10\x00\x12\x14\n\x10RECORD_NOT_FOUND\x10\x05\x12\x0c\n\x08INTERNAL\x10\r"\xd4\x07\n\x1dGetFinancialActivitiesRequest\x12\x18\n\x10\x63urrent_actor_id\x18\x01 \x01(\t\x12>\n\x1a\x61\x63tivities_start_timestamp\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12<\n\x18\x61\x63tivities_end_timestamp\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1c\n\tpage_size\x18\x04 \x01(\x05\x42\t\xfa\x42\x06\x1a\x04\x18((\n\x12\x12\n\ndescending\x18\x05 \x01(\x08\x12\x42\n\x0f\x61\x63tivity_source\x18\x06 \x01(\x0e\x32).order.actoractivity.enums.ActivitySource\x12T\n\x0f\x61\x63tivity_offset\x18\x07 \x01(\x0b\x32;.actoractivity.GetFinancialActivitiesRequest.ActivityOffSet\x12R\n\x0epayment_filter\x18\x08 \x01(\x0b\x32:.actoractivity.GetFinancialActivitiesRequest.PaymentFilter\x1aM\n\x0e\x41\x63tivityOffSet\x12\x15\n\rfi_txn_offset\x18\x01 \x01(\x05\x12$\n\x1c\x63onnected_account_txn_offset\x18\x02 \x01(\x05\x1a\xab\x03\n\rPaymentFilter\x12\x38\n\x10payment_protocol\x18\x01 \x03(\x0e\x32\x1e.order.payment.PaymentProtocol\x12\'\n\x0b\x66rom_amount\x18\x02 \x01(\x0b\x32\x12.google.type.Money\x12%\n\tto_amount\x18\x03 \x01(\x0b\x32\x12.google.type.Money\x12<\n\x10transaction_type\x18\x04 \x01(\x0e\x32".order.payment.AccountingEntryType\x12V\n\tpi_filter\x18\x05 \x01(\x0b\x32\x43.actoractivity.GetFinancialActivitiesRequest.PaymentFilter.PiFilter\x1az\n\x08PiFilter\x12\x1a\n\x12internal_pi_filter\x18\x01 \x03(\t\x12\x14\n\x0c\x61\x61_pi_filter\x18\x02 \x03(\t\x12 \n\x18other_internal_pi_filter\x18\x03 \x03(\t\x12\x1a\n\x12other_aa_pi_filter\x18\x04 \x03(\t"\xaf\x01\n\x1eGetFinancialActivitiesResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12:\n\nactivities\x18\x02 \x03(\x0b\x32&.order.actoractivity.activity.Activity"4\n\x06Status\x12\x06\n\x02OK\x10\x00\x12\x14\n\x10RECORD_NOT_FOUND\x10\x05\x12\x0c\n\x08INTERNAL\x10\r*^\n\x12\x41\x63tivityEntryPoint\x12$\n ACTIVITY_ENTRY_POINT_UNSPECIFIED\x10\x00\x12\t\n\x05ORDER\x10\x01\x12\x06\n\x02\x41\x41\x10\x02\x12\x0f\n\x0b\x43REDIT_CARD\x10\x03\x32\xe6\x01\n\rActorActivity\x12\\\n\rGetActivities\x12#.actoractivity.GetActivitiesRequest\x1a$.actoractivity.GetActivitiesResponse"\x00\x12w\n\x16GetFinancialActivities\x12,.actoractivity.GetFinancialActivitiesRequest\x1a-.actoractivity.GetFinancialActivitiesResponse"\x00\x42`\n.com.github.epifi.gamma.api.order.actoractivityZ.github.com/epifi/gamma/api/order/actoractivityb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "api.order.actoractivity.service_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n.com.github.epifi.gamma.api.order.actoractivityZ.github.com/epifi/gamma/api/order/actoractivity"
    _GETACTIVITIESREQUEST.fields_by_name["page_size"]._options = None
    _GETACTIVITIESREQUEST.fields_by_name[
        "page_size"
    ]._serialized_options = b"\372B\006\032\004\030((\n"
    _GETACTIVITIESREQUEST.fields_by_name["activity_offset"]._options = None
    _GETACTIVITIESREQUEST.fields_by_name["activity_offset"]._serialized_options = b"\030\001"
    _GETACTIVITIESREQUEST.fields_by_name["pi_filter"]._options = None
    _GETACTIVITIESREQUEST.fields_by_name["pi_filter"]._serialized_options = b"\030\001"
    _GETFINANCIALACTIVITIESREQUEST.fields_by_name["page_size"]._options = None
    _GETFINANCIALACTIVITIESREQUEST.fields_by_name[
        "page_size"
    ]._serialized_options = b"\372B\006\032\004\030((\n"
    _globals["_ACTIVITYENTRYPOINT"]._serialized_start = 4697
    _globals["_ACTIVITYENTRYPOINT"]._serialized_end = 4791
    _globals["_GETACTIVITIESREQUEST"]._serialized_start = 386
    _globals["_GETACTIVITIESREQUEST"]._serialized_end = 1423
    _globals["_GETACTIVITIESREQUEST_ACCOUNTFILTER"]._serialized_start = 997
    _globals["_GETACTIVITIESREQUEST_ACCOUNTFILTER"]._serialized_end = 1070
    _globals["_GETACTIVITIESREQUEST_PAYMENTFILTER"]._serialized_start = 1073
    _globals["_GETACTIVITIESREQUEST_PAYMENTFILTER"]._serialized_end = 1307
    _globals["_GETACTIVITIESREQUEST_ENTRYPOINTTYPE"]._serialized_start = 1309
    _globals["_GETACTIVITIESREQUEST_ENTRYPOINTTYPE"]._serialized_end = 1423
    _globals["_GETACTIVITIESRESPONSE"]._serialized_start = 1426
    _globals["_GETACTIVITIESRESPONSE"]._serialized_end = 3534
    _globals["_GETACTIVITIESRESPONSE_ACTIVITY"]._serialized_start = 1548
    _globals["_GETACTIVITIESRESPONSE_ACTIVITY"]._serialized_end = 3480
    _globals["_GETACTIVITIESRESPONSE_ACTIVITY_DEPOSITACCOUNTIDENTIFIER"]._serialized_start = 2134
    _globals["_GETACTIVITIESRESPONSE_ACTIVITY_DEPOSITACCOUNTIDENTIFIER"]._serialized_end = 2218
    _globals["_GETACTIVITIESRESPONSE_ACTIVITY_AMOUNTBADGE"]._serialized_start = 2220
    _globals["_GETACTIVITIESRESPONSE_ACTIVITY_AMOUNTBADGE"]._serialized_end = 2341
    _globals["_GETACTIVITIESRESPONSE_ACTIVITY_TYPE"]._serialized_start = 2344
    _globals["_GETACTIVITIESRESPONSE_ACTIVITY_TYPE"]._serialized_end = 3457
    _globals["_GETACTIVITIESRESPONSE_STATUS"]._serialized_start = 3482
    _globals["_GETACTIVITIESRESPONSE_STATUS"]._serialized_end = 3534
    _globals["_GETFINANCIALACTIVITIESREQUEST"]._serialized_start = 3537
    _globals["_GETFINANCIALACTIVITIESREQUEST"]._serialized_end = 4517
    _globals["_GETFINANCIALACTIVITIESREQUEST_ACTIVITYOFFSET"]._serialized_start = 4010
    _globals["_GETFINANCIALACTIVITIESREQUEST_ACTIVITYOFFSET"]._serialized_end = 4087
    _globals["_GETFINANCIALACTIVITIESREQUEST_PAYMENTFILTER"]._serialized_start = 4090
    _globals["_GETFINANCIALACTIVITIESREQUEST_PAYMENTFILTER"]._serialized_end = 4517
    _globals["_GETFINANCIALACTIVITIESREQUEST_PAYMENTFILTER_PIFILTER"]._serialized_start = 4395
    _globals["_GETFINANCIALACTIVITIESREQUEST_PAYMENTFILTER_PIFILTER"]._serialized_end = 4517
    _globals["_GETFINANCIALACTIVITIESRESPONSE"]._serialized_start = 4520
    _globals["_GETFINANCIALACTIVITIESRESPONSE"]._serialized_end = 4695
    _globals["_GETFINANCIALACTIVITIESRESPONSE_STATUS"]._serialized_start = 3482
    _globals["_GETFINANCIALACTIVITIESRESPONSE_STATUS"]._serialized_end = 3534
    _globals["_ACTORACTIVITY"]._serialized_start = 4794
    _globals["_ACTORACTIVITY"]._serialized_end = 5024
# @@protoc_insertion_point(module_scope)
