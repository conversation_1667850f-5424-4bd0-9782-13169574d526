# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/order/developer/service.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from api.cx.developer.db_state import (
    db_state_pb2 as api_dot_cx_dot_developer_dot_db__state_dot_db__state__pb2,
)

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n!api/order/developer/service.proto\x12\x0forder.developer\x1a(api/cx/developer/db_state/db_state.proto2\xc6\x02\n\x03\x44\x65v\x12l\n\rGetEntityList\x12+.cx.developer.db_state.GetEntityListRequest\x1a,.cx.developer.db_state.GetEntityListResponse"\x00\x12u\n\x10GetParameterList\x12..cx.developer.db_state.GetParameterListRequest\x1a/.cx.developer.db_state.GetParameterListResponse"\x00\x12Z\n\x07GetData\x12%.cx.developer.db_state.GetDataRequest\x1a&.cx.developer.db_state.GetDataResponse"\x00\x42X\n*com.github.epifi.gamma.api.order.developerZ*github.com/epifi/gamma/api/order/developerb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.order.developer.service_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n*com.github.epifi.gamma.api.order.developerZ*github.com/epifi/gamma/api/order/developer"
    _globals["_DEV"]._serialized_start = 97
    _globals["_DEV"]._serialized_end = 423
# @@protoc_insertion_point(module_scope)
