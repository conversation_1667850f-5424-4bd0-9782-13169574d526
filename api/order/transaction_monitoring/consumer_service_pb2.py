# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/order/transaction_monitoring/consumer_service.proto

from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from api.order import order_pb2 as api_dot_order_dot_order__pb2
from api.queue import consumer_headers_pb2 as api_dot_queue_dot_consumer__headers__pb2

DESCRIPTOR = _descriptor.FileDescriptor(
    name="api/order/transaction_monitoring/consumer_service.proto",
    package="order.transaction_monitoring",
    syntax="proto3",
    serialized_options=b"\n7com.github.epifi.gamma.api.order.transaction_monitoringZ7github.com/epifi/gamma/api/order/transaction_monitoring",
    create_key=_descriptor._internal_create_key,
    serialized_pb=b'\n7api/order/transaction_monitoring/consumer_service.proto\x12\x1corder.transaction_monitoring\x1a\x15\x61pi/order/order.proto\x1a api/queue/consumer_headers.proto"[\n!ProcessOrderTxnMonitoringResponse\x12\x36\n\x0fresponse_header\x18\x01 \x01(\x0b\x32\x1d.queue.ConsumerResponseHeader2\x8b\x01\n\x15TxnMonitoringConsumer\x12r\n\x19ProcessOrderTxnMonitoring\x12\x12.order.OrderUpdate\x1a?.order.transaction_monitoring.ProcessOrderTxnMonitoringResponse"\x00\x42r\n7com.github.epifi.gamma.api.order.transaction_monitoringZ7github.com/epifi/gamma/api/order/transaction_monitoringb\x06proto3',
    dependencies=[
        api_dot_order_dot_order__pb2.DESCRIPTOR,
        api_dot_queue_dot_consumer__headers__pb2.DESCRIPTOR,
    ],
)


_PROCESSORDERTXNMONITORINGRESPONSE = _descriptor.Descriptor(
    name="ProcessOrderTxnMonitoringResponse",
    full_name="order.transaction_monitoring.ProcessOrderTxnMonitoringResponse",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="response_header",
            full_name="order.transaction_monitoring.ProcessOrderTxnMonitoringResponse.response_header",
            index=0,
            number=1,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=146,
    serialized_end=237,
)

_PROCESSORDERTXNMONITORINGRESPONSE.fields_by_name[
    "response_header"
].message_type = api_dot_queue_dot_consumer__headers__pb2._CONSUMERRESPONSEHEADER
DESCRIPTOR.message_types_by_name[
    "ProcessOrderTxnMonitoringResponse"
] = _PROCESSORDERTXNMONITORINGRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ProcessOrderTxnMonitoringResponse = _reflection.GeneratedProtocolMessageType(
    "ProcessOrderTxnMonitoringResponse",
    (_message.Message,),
    {
        "DESCRIPTOR": _PROCESSORDERTXNMONITORINGRESPONSE,
        "__module__": "api.order.transaction_monitoring.consumer_service_pb2"
        # @@protoc_insertion_point(class_scope:order.transaction_monitoring.ProcessOrderTxnMonitoringResponse)
    },
)
_sym_db.RegisterMessage(ProcessOrderTxnMonitoringResponse)


DESCRIPTOR._options = None

_TXNMONITORINGCONSUMER = _descriptor.ServiceDescriptor(
    name="TxnMonitoringConsumer",
    full_name="order.transaction_monitoring.TxnMonitoringConsumer",
    file=DESCRIPTOR,
    index=0,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
    serialized_start=240,
    serialized_end=379,
    methods=[
        _descriptor.MethodDescriptor(
            name="ProcessOrderTxnMonitoring",
            full_name="order.transaction_monitoring.TxnMonitoringConsumer.ProcessOrderTxnMonitoring",
            index=0,
            containing_service=None,
            input_type=api_dot_order_dot_order__pb2._ORDERUPDATE,
            output_type=_PROCESSORDERTXNMONITORINGRESPONSE,
            serialized_options=None,
            create_key=_descriptor._internal_create_key,
        ),
    ],
)
_sym_db.RegisterServiceDescriptor(_TXNMONITORINGCONSUMER)

DESCRIPTOR.services_by_name["TxnMonitoringConsumer"] = _TXNMONITORINGCONSUMER

# @@protoc_insertion_point(module_scope)
