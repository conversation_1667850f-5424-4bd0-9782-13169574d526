# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/order/workflow/filter/status_filter.proto

from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2

from api.order import order_pb2 as api_dot_order_dot_order__pb2

DESCRIPTOR = _descriptor.FileDescriptor(
    name="api/order/workflow/filter/status_filter.proto",
    package="order.workflow.filter",
    syntax="proto3",
    serialized_options=b"\n0com.github.epifi.gamma.api.order.workflow.filterZ0github.com/epifi/gamma/api/order/workflow/filter",
    create_key=_descriptor._internal_create_key,
    serialized_pb=b'\n-api/order/workflow/filter/status_filter.proto\x12\x15order.workflow.filter\x1a\x15\x61pi/order/order.proto\x1a\x1egoogle/protobuf/duration.proto"7\n\x0cStatusOption\x12\'\n\x0bstatus_list\x18\x01 \x03(\x0e\x32\x12.order.OrderStatus"\x80\x01\n\x13StatusWithSlaOption\x12\x41\n\x14status_filter_option\x18\x01 \x01(\x0b\x32#.order.workflow.filter.StatusOption\x12&\n\x03sla\x18\x02 \x01(\x0b\x32\x19.google.protobuf.DurationBd\n0com.github.epifi.gamma.api.order.workflow.filterZ0github.com/epifi/gamma/api/order/workflow/filterb\x06proto3',
    dependencies=[
        api_dot_order_dot_order__pb2.DESCRIPTOR,
        google_dot_protobuf_dot_duration__pb2.DESCRIPTOR,
    ],
)


_STATUSOPTION = _descriptor.Descriptor(
    name="StatusOption",
    full_name="order.workflow.filter.StatusOption",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="status_list",
            full_name="order.workflow.filter.StatusOption.status_list",
            index=0,
            number=1,
            type=14,
            cpp_type=8,
            label=3,
            has_default_value=False,
            default_value=[],
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=127,
    serialized_end=182,
)


_STATUSWITHSLAOPTION = _descriptor.Descriptor(
    name="StatusWithSlaOption",
    full_name="order.workflow.filter.StatusWithSlaOption",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="status_filter_option",
            full_name="order.workflow.filter.StatusWithSlaOption.status_filter_option",
            index=0,
            number=1,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="sla",
            full_name="order.workflow.filter.StatusWithSlaOption.sla",
            index=1,
            number=2,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=185,
    serialized_end=313,
)

_STATUSOPTION.fields_by_name["status_list"].enum_type = api_dot_order_dot_order__pb2._ORDERSTATUS
_STATUSWITHSLAOPTION.fields_by_name["status_filter_option"].message_type = _STATUSOPTION
_STATUSWITHSLAOPTION.fields_by_name[
    "sla"
].message_type = google_dot_protobuf_dot_duration__pb2._DURATION
DESCRIPTOR.message_types_by_name["StatusOption"] = _STATUSOPTION
DESCRIPTOR.message_types_by_name["StatusWithSlaOption"] = _STATUSWITHSLAOPTION
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

StatusOption = _reflection.GeneratedProtocolMessageType(
    "StatusOption",
    (_message.Message,),
    {
        "DESCRIPTOR": _STATUSOPTION,
        "__module__": "api.order.workflow.filter.status_filter_pb2"
        # @@protoc_insertion_point(class_scope:order.workflow.filter.StatusOption)
    },
)
_sym_db.RegisterMessage(StatusOption)

StatusWithSlaOption = _reflection.GeneratedProtocolMessageType(
    "StatusWithSlaOption",
    (_message.Message,),
    {
        "DESCRIPTOR": _STATUSWITHSLAOPTION,
        "__module__": "api.order.workflow.filter.status_filter_pb2"
        # @@protoc_insertion_point(class_scope:order.workflow.filter.StatusWithSlaOption)
    },
)
_sym_db.RegisterMessage(StatusWithSlaOption)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)
