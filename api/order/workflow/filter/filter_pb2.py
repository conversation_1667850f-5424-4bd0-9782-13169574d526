# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/order/workflow/filter/filter.proto

from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from api.order.workflow.filter import (
    stage_filter_pb2 as api_dot_order_dot_workflow_dot_filter_dot_stage__filter__pb2,
)
from api.order.workflow.filter import (
    status_filter_pb2 as api_dot_order_dot_workflow_dot_filter_dot_status__filter__pb2,
)

DESCRIPTOR = _descriptor.FileDescriptor(
    name="api/order/workflow/filter/filter.proto",
    package="order.workflow.filter",
    syntax="proto3",
    serialized_options=b"\n0com.github.epifi.gamma.api.order.workflow.filterZ0github.com/epifi/gamma/api/order/workflow/filter",
    create_key=_descriptor._internal_create_key,
    serialized_pb=b'\n&api/order/workflow/filter/filter.proto\x12\x15order.workflow.filter\x1a,api/order/workflow/filter/stage_filter.proto\x1a-api/order/workflow/filter/status_filter.proto"\xc2\x02\n\x06\x46ilter\x12\x43\n\x14status_filter_option\x18\x01 \x01(\x0b\x32#.order.workflow.filter.StatusOptionH\x00\x12\x41\n\x13stage_filter_option\x18\x02 \x01(\x0b\x32".order.workflow.filter.StageOptionH\x00\x12S\n\x1dstatus_with_sla_filter_option\x18\x03 \x01(\x0b\x32*.order.workflow.filter.StatusWithSlaOptionH\x00\x12Q\n\x1cstage_with_sla_filter_option\x18\x04 \x01(\x0b\x32).order.workflow.filter.StageWithSlaOptionH\x00\x42\x08\n\x06Option"O\n\x0f\x46ilterWithCount\x12-\n\x06\x66ilter\x18\x01 \x01(\x0b\x32\x1d.order.workflow.filter.Filter\x12\r\n\x05\x63ount\x18\x02 \x01(\x03\x42\x64\n0com.github.epifi.gamma.api.order.workflow.filterZ0github.com/epifi/gamma/api/order/workflow/filterb\x06proto3',
    dependencies=[
        api_dot_order_dot_workflow_dot_filter_dot_stage__filter__pb2.DESCRIPTOR,
        api_dot_order_dot_workflow_dot_filter_dot_status__filter__pb2.DESCRIPTOR,
    ],
)


_FILTER = _descriptor.Descriptor(
    name="Filter",
    full_name="order.workflow.filter.Filter",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="status_filter_option",
            full_name="order.workflow.filter.Filter.status_filter_option",
            index=0,
            number=1,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="stage_filter_option",
            full_name="order.workflow.filter.Filter.stage_filter_option",
            index=1,
            number=2,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="status_with_sla_filter_option",
            full_name="order.workflow.filter.Filter.status_with_sla_filter_option",
            index=2,
            number=3,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="stage_with_sla_filter_option",
            full_name="order.workflow.filter.Filter.stage_with_sla_filter_option",
            index=3,
            number=4,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[
        _descriptor.OneofDescriptor(
            name="Option",
            full_name="order.workflow.filter.Filter.Option",
            index=0,
            containing_type=None,
            create_key=_descriptor._internal_create_key,
            fields=[],
        ),
    ],
    serialized_start=159,
    serialized_end=481,
)


_FILTERWITHCOUNT = _descriptor.Descriptor(
    name="FilterWithCount",
    full_name="order.workflow.filter.FilterWithCount",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="filter",
            full_name="order.workflow.filter.FilterWithCount.filter",
            index=0,
            number=1,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="count",
            full_name="order.workflow.filter.FilterWithCount.count",
            index=1,
            number=2,
            type=3,
            cpp_type=2,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=483,
    serialized_end=562,
)

_FILTER.fields_by_name[
    "status_filter_option"
].message_type = api_dot_order_dot_workflow_dot_filter_dot_status__filter__pb2._STATUSOPTION
_FILTER.fields_by_name[
    "stage_filter_option"
].message_type = api_dot_order_dot_workflow_dot_filter_dot_stage__filter__pb2._STAGEOPTION
_FILTER.fields_by_name[
    "status_with_sla_filter_option"
].message_type = api_dot_order_dot_workflow_dot_filter_dot_status__filter__pb2._STATUSWITHSLAOPTION
_FILTER.fields_by_name[
    "stage_with_sla_filter_option"
].message_type = api_dot_order_dot_workflow_dot_filter_dot_stage__filter__pb2._STAGEWITHSLAOPTION
_FILTER.oneofs_by_name["Option"].fields.append(_FILTER.fields_by_name["status_filter_option"])
_FILTER.fields_by_name["status_filter_option"].containing_oneof = _FILTER.oneofs_by_name["Option"]
_FILTER.oneofs_by_name["Option"].fields.append(_FILTER.fields_by_name["stage_filter_option"])
_FILTER.fields_by_name["stage_filter_option"].containing_oneof = _FILTER.oneofs_by_name["Option"]
_FILTER.oneofs_by_name["Option"].fields.append(
    _FILTER.fields_by_name["status_with_sla_filter_option"]
)
_FILTER.fields_by_name["status_with_sla_filter_option"].containing_oneof = _FILTER.oneofs_by_name[
    "Option"
]
_FILTER.oneofs_by_name["Option"].fields.append(
    _FILTER.fields_by_name["stage_with_sla_filter_option"]
)
_FILTER.fields_by_name["stage_with_sla_filter_option"].containing_oneof = _FILTER.oneofs_by_name[
    "Option"
]
_FILTERWITHCOUNT.fields_by_name["filter"].message_type = _FILTER
DESCRIPTOR.message_types_by_name["Filter"] = _FILTER
DESCRIPTOR.message_types_by_name["FilterWithCount"] = _FILTERWITHCOUNT
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Filter = _reflection.GeneratedProtocolMessageType(
    "Filter",
    (_message.Message,),
    {
        "DESCRIPTOR": _FILTER,
        "__module__": "api.order.workflow.filter.filter_pb2"
        # @@protoc_insertion_point(class_scope:order.workflow.filter.Filter)
    },
)
_sym_db.RegisterMessage(Filter)

FilterWithCount = _reflection.GeneratedProtocolMessageType(
    "FilterWithCount",
    (_message.Message,),
    {
        "DESCRIPTOR": _FILTERWITHCOUNT,
        "__module__": "api.order.workflow.filter.filter_pb2"
        # @@protoc_insertion_point(class_scope:order.workflow.filter.FilterWithCount)
    },
)
_sym_db.RegisterMessage(FilterWithCount)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)
