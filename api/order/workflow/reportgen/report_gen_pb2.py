# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/order/workflow/reportgen/report_gen.proto

from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

from api.order import workflow_pb2 as api_dot_order_dot_workflow__pb2
from api.order.workflow.filter import (
    filter_pb2 as api_dot_order_dot_workflow_dot_filter_dot_filter__pb2,
)

DESCRIPTOR = _descriptor.FileDescriptor(
    name="api/order/workflow/reportgen/report_gen.proto",
    package="order.workflow.reportgen",
    syntax="proto3",
    serialized_options=b"\n3com.github.epifi.gamma.api.order.workflow.reportgenZ3github.com/epifi/gamma/api/order/workflow/reportgen",
    create_key=_descriptor._internal_create_key,
    serialized_pb=b'\n-api/order/workflow/reportgen/report_gen.proto\x12\x18order.workflow.reportgen\x1a&api/order/workflow/filter/filter.proto\x1a\x18\x61pi/order/workflow.proto\x1a\x1fgoogle/protobuf/timestamp.proto"\xbd\x01\n\x1f\x45xecutionReportGeneratorRequest\x12+\n\rworkflow_type\x18\x01 \x01(\x0e\x32\x14.order.OrderWorkflow\x12\x32\n\x0b\x66ilter_list\x18\x02 \x03(\x0b\x32\x1d.order.workflow.filter.Filter\x12\x39\n\x0breport_sink\x18\x03 \x01(\x0b\x32$.order.workflow.reportgen.ReportSink"X\n ExecutionReportGeneratorResponse\x12\x34\n\x10last_reported_at\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp".\n\nReportSink\x12\x12\n\x08\x65mail_id\x18\x01 \x01(\tH\x00\x42\x0c\n\nidentifierBj\n3com.github.epifi.gamma.api.order.workflow.reportgenZ3github.com/epifi/gamma/api/order/workflow/reportgenb\x06proto3',
    dependencies=[
        api_dot_order_dot_workflow_dot_filter_dot_filter__pb2.DESCRIPTOR,
        api_dot_order_dot_workflow__pb2.DESCRIPTOR,
        google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,
    ],
)


_EXECUTIONREPORTGENERATORREQUEST = _descriptor.Descriptor(
    name="ExecutionReportGeneratorRequest",
    full_name="order.workflow.reportgen.ExecutionReportGeneratorRequest",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="workflow_type",
            full_name="order.workflow.reportgen.ExecutionReportGeneratorRequest.workflow_type",
            index=0,
            number=1,
            type=14,
            cpp_type=8,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="filter_list",
            full_name="order.workflow.reportgen.ExecutionReportGeneratorRequest.filter_list",
            index=1,
            number=2,
            type=11,
            cpp_type=10,
            label=3,
            has_default_value=False,
            default_value=[],
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="report_sink",
            full_name="order.workflow.reportgen.ExecutionReportGeneratorRequest.report_sink",
            index=2,
            number=3,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=175,
    serialized_end=364,
)


_EXECUTIONREPORTGENERATORRESPONSE = _descriptor.Descriptor(
    name="ExecutionReportGeneratorResponse",
    full_name="order.workflow.reportgen.ExecutionReportGeneratorResponse",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="last_reported_at",
            full_name="order.workflow.reportgen.ExecutionReportGeneratorResponse.last_reported_at",
            index=0,
            number=1,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=366,
    serialized_end=454,
)


_REPORTSINK = _descriptor.Descriptor(
    name="ReportSink",
    full_name="order.workflow.reportgen.ReportSink",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="email_id",
            full_name="order.workflow.reportgen.ReportSink.email_id",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[
        _descriptor.OneofDescriptor(
            name="identifier",
            full_name="order.workflow.reportgen.ReportSink.identifier",
            index=0,
            containing_type=None,
            create_key=_descriptor._internal_create_key,
            fields=[],
        ),
    ],
    serialized_start=456,
    serialized_end=502,
)

_EXECUTIONREPORTGENERATORREQUEST.fields_by_name[
    "workflow_type"
].enum_type = api_dot_order_dot_workflow__pb2._ORDERWORKFLOW
_EXECUTIONREPORTGENERATORREQUEST.fields_by_name[
    "filter_list"
].message_type = api_dot_order_dot_workflow_dot_filter_dot_filter__pb2._FILTER
_EXECUTIONREPORTGENERATORREQUEST.fields_by_name["report_sink"].message_type = _REPORTSINK
_EXECUTIONREPORTGENERATORRESPONSE.fields_by_name[
    "last_reported_at"
].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_REPORTSINK.oneofs_by_name["identifier"].fields.append(_REPORTSINK.fields_by_name["email_id"])
_REPORTSINK.fields_by_name["email_id"].containing_oneof = _REPORTSINK.oneofs_by_name["identifier"]
DESCRIPTOR.message_types_by_name[
    "ExecutionReportGeneratorRequest"
] = _EXECUTIONREPORTGENERATORREQUEST
DESCRIPTOR.message_types_by_name[
    "ExecutionReportGeneratorResponse"
] = _EXECUTIONREPORTGENERATORRESPONSE
DESCRIPTOR.message_types_by_name["ReportSink"] = _REPORTSINK
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ExecutionReportGeneratorRequest = _reflection.GeneratedProtocolMessageType(
    "ExecutionReportGeneratorRequest",
    (_message.Message,),
    {
        "DESCRIPTOR": _EXECUTIONREPORTGENERATORREQUEST,
        "__module__": "api.order.workflow.reportgen.report_gen_pb2"
        # @@protoc_insertion_point(class_scope:order.workflow.reportgen.ExecutionReportGeneratorRequest)
    },
)
_sym_db.RegisterMessage(ExecutionReportGeneratorRequest)

ExecutionReportGeneratorResponse = _reflection.GeneratedProtocolMessageType(
    "ExecutionReportGeneratorResponse",
    (_message.Message,),
    {
        "DESCRIPTOR": _EXECUTIONREPORTGENERATORRESPONSE,
        "__module__": "api.order.workflow.reportgen.report_gen_pb2"
        # @@protoc_insertion_point(class_scope:order.workflow.reportgen.ExecutionReportGeneratorResponse)
    },
)
_sym_db.RegisterMessage(ExecutionReportGeneratorResponse)

ReportSink = _reflection.GeneratedProtocolMessageType(
    "ReportSink",
    (_message.Message,),
    {
        "DESCRIPTOR": _REPORTSINK,
        "__module__": "api.order.workflow.reportgen.report_gen_pb2"
        # @@protoc_insertion_point(class_scope:order.workflow.reportgen.ReportSink)
    },
)
_sym_db.RegisterMessage(ReportSink)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)
