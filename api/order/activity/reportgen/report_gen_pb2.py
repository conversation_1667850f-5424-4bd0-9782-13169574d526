# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/order/activity/reportgen/report_gen.proto

from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

from api.order import workflow_pb2 as api_dot_order_dot_workflow__pb2
from api.order.workflow.filter import (
    filter_pb2 as api_dot_order_dot_workflow_dot_filter_dot_filter__pb2,
)

DESCRIPTOR = _descriptor.FileDescriptor(
    name="api/order/activity/reportgen/report_gen.proto",
    package="order.activity.reportgen",
    syntax="proto3",
    serialized_options=b"\n3com.github.epifi.gamma.api.order.activity.reportgenZ3github.com/epifi/gamma/api/order/activity/reportgen",
    create_key=_descriptor._internal_create_key,
    serialized_pb=b'\n-api/order/activity/reportgen/report_gen.proto\x12\x18order.activity.reportgen\x1a&api/order/workflow/filter/filter.proto\x1a\x18\x61pi/order/workflow.proto\x1a\x1fgoogle/protobuf/timestamp.proto"\xd6\x01\n\x17GetWorkflowCountRequest\x12+\n\rworkflow_type\x18\x01 \x01(\x0e\x32\x14.order.OrderWorkflow\x12\x32\n\x0b\x66ilter_list\x18\x02 \x03(\x0b\x32\x1d.order.workflow.filter.Filter\x12-\n\tfrom_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07to_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp"b\n\x18GetWorkflowCountResponse\x12\x46\n\x16\x66ilter_with_count_list\x18\x01 \x03(\x0b\x32&.order.workflow.filter.FilterWithCountBj\n3com.github.epifi.gamma.api.order.activity.reportgenZ3github.com/epifi/gamma/api/order/activity/reportgenb\x06proto3',
    dependencies=[
        api_dot_order_dot_workflow_dot_filter_dot_filter__pb2.DESCRIPTOR,
        api_dot_order_dot_workflow__pb2.DESCRIPTOR,
        google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,
    ],
)


_GETWORKFLOWCOUNTREQUEST = _descriptor.Descriptor(
    name="GetWorkflowCountRequest",
    full_name="order.activity.reportgen.GetWorkflowCountRequest",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="workflow_type",
            full_name="order.activity.reportgen.GetWorkflowCountRequest.workflow_type",
            index=0,
            number=1,
            type=14,
            cpp_type=8,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="filter_list",
            full_name="order.activity.reportgen.GetWorkflowCountRequest.filter_list",
            index=1,
            number=2,
            type=11,
            cpp_type=10,
            label=3,
            has_default_value=False,
            default_value=[],
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="from_time",
            full_name="order.activity.reportgen.GetWorkflowCountRequest.from_time",
            index=2,
            number=3,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="to_time",
            full_name="order.activity.reportgen.GetWorkflowCountRequest.to_time",
            index=3,
            number=4,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=175,
    serialized_end=389,
)


_GETWORKFLOWCOUNTRESPONSE = _descriptor.Descriptor(
    name="GetWorkflowCountResponse",
    full_name="order.activity.reportgen.GetWorkflowCountResponse",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="filter_with_count_list",
            full_name="order.activity.reportgen.GetWorkflowCountResponse.filter_with_count_list",
            index=0,
            number=1,
            type=11,
            cpp_type=10,
            label=3,
            has_default_value=False,
            default_value=[],
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=391,
    serialized_end=489,
)

_GETWORKFLOWCOUNTREQUEST.fields_by_name[
    "workflow_type"
].enum_type = api_dot_order_dot_workflow__pb2._ORDERWORKFLOW
_GETWORKFLOWCOUNTREQUEST.fields_by_name[
    "filter_list"
].message_type = api_dot_order_dot_workflow_dot_filter_dot_filter__pb2._FILTER
_GETWORKFLOWCOUNTREQUEST.fields_by_name[
    "from_time"
].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETWORKFLOWCOUNTREQUEST.fields_by_name[
    "to_time"
].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETWORKFLOWCOUNTRESPONSE.fields_by_name[
    "filter_with_count_list"
].message_type = api_dot_order_dot_workflow_dot_filter_dot_filter__pb2._FILTERWITHCOUNT
DESCRIPTOR.message_types_by_name["GetWorkflowCountRequest"] = _GETWORKFLOWCOUNTREQUEST
DESCRIPTOR.message_types_by_name["GetWorkflowCountResponse"] = _GETWORKFLOWCOUNTRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetWorkflowCountRequest = _reflection.GeneratedProtocolMessageType(
    "GetWorkflowCountRequest",
    (_message.Message,),
    {
        "DESCRIPTOR": _GETWORKFLOWCOUNTREQUEST,
        "__module__": "api.order.activity.reportgen.report_gen_pb2"
        # @@protoc_insertion_point(class_scope:order.activity.reportgen.GetWorkflowCountRequest)
    },
)
_sym_db.RegisterMessage(GetWorkflowCountRequest)

GetWorkflowCountResponse = _reflection.GeneratedProtocolMessageType(
    "GetWorkflowCountResponse",
    (_message.Message,),
    {
        "DESCRIPTOR": _GETWORKFLOWCOUNTRESPONSE,
        "__module__": "api.order.activity.reportgen.report_gen_pb2"
        # @@protoc_insertion_point(class_scope:order.activity.reportgen.GetWorkflowCountResponse)
    },
)
_sym_db.RegisterMessage(GetWorkflowCountResponse)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)
