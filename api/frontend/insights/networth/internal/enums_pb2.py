# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/frontend/insights/networth/internal/enums.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n3api/frontend/insights/networth/internal/enums.proto\x12\x1a\x66rontend.insights.networth*\x95\x08\n\x10NetworthCategory\x12!\n\x1dNETWORTH_CATEGORY_UNSPECIFIED\x10\x00\x12,\n(NETWORTH_CATEGORY_ASSET_SAVINGS_ACCOUNTS\x10\x01\x12(\n$NETWORTH_CATEGORY_ASSET_MUTUAL_FUNDS\x10\x02\x12\x1f\n\x1bNETWORTH_CATEGORY_ASSET_EPF\x10\x03\x12$\n NETWORTH_CATEGORY_ASSET_DEPOSITS\x10\x04\x12-\n)NETWORTH_CATEGORY_ASSET_INDIAN_SECURITIES\x10\x05\x12)\n%NETWORTH_CATEGORY_ASSET_US_SECURITIES\x10\x06\x12 \n\x1cNETWORTH_CATEGORY_ASSET_JUMP\x10\x07\x12!\n\x1dNETWORTH_CATEGORY_ASSET_FI_SD\x10\x08\x12!\n\x1dNETWORTH_CATEGORY_ASSET_FI_FD\x10\t\x12\x37\n3NETWORTH_CATEGORY_LIABILITY_CREDIT_CARD_OUTSTANDING\x10\n\x12)\n%NETWORTH_CATEGORY_LIABILITY_HOME_LOAN\x10\x0b\x12-\n)NETWORTH_CATEGORY_LIABILITY_PERSONAL_LOAN\x10\x0c\x12,\n(NETWORTH_CATEGORY_LIABILITY_VEHICLE_LOAN\x10\r\x12.\n*NETWORTH_CATEGORY_LIABILITY_EDUCATION_LOAN\x10\x0e\x12+\n'NETWORTH_CATEGORY_LIABILITY_OTHER_LOANS\x10\x0f\x12\x1f\n\x1bNETWORTH_CATEGORY_ASSET_NPS\x10\x10\x12\x19\n\x15NETWORTH_CATEGORY_AIF\x10\x11\x12#\n\x1fNETWORTH_CATEGORY_ART_ARTEFACTS\x10\x12\x12\x1b\n\x17NETWORTH_CATEGORY_BONDS\x10\x13\x12\x1a\n\x16NETWORTH_CATEGORY_CASH\x10\x14\x12\"\n\x1eNETWORTH_CATEGORY_DIGITAL_GOLD\x10\x15\x12$\n NETWORTH_CATEGORY_DIGITAL_SILVER\x10\x16\x12$\n NETWORTH_CATEGORY_PRIVATE_EQUITY\x10\x17\x12!\n\x1dNETWORTH_CATEGORY_REAL_ESTATE\x10\x18\x12\x32\n.NETWORTH_CATEGORY_PORTFOLIO_MANAGEMENT_SERVICE\x10\x19*\x85\x01\n\x13NetworthSectionType\x12%\n!NETWORTH_SECTION_TYPE_UNSPECIFIED\x10\x00\x12 \n\x1cNETWORTH_SECTION_TYPE_ASSETS\x10\x01\x12%\n!NETWORTH_SECTION_TYPE_LIABILITIES\x10\x02*\xf6\x01\n\x16NetworthCategoryStatus\x12(\n$NETWORTH_CATEGORY_STATUS_UNSPECIFIED\x10\x00\x12*\n&NETWORTH_CATEGORY_STATUS_UNINITIALIZED\x10\x01\x12+\n'NETWORTH_CATEGORY_STATUS_NOT_APPLICABLE\x10\x02\x12(\n$NETWORTH_CATEGORY_STATUS_INITIALIZED\x10\x03\x12/\n+NETWORTH_CATEGORY_STATUS_VALUE_FETCH_FAILED\x10\x04*\x85\x01\n\x13NetworthWidgetState\x12%\n!NETWORTH_WIDGET_STATE_UNSPECIFIED\x10\x00\x12 \n\x1cNETWORTH_WIDGET_STATE_ACTIVE\x10\x01\x12%\n!NETWORTH_WIDGET_STATE_COMING_SOON\x10\x02*\xb6\x05\n\x1bNetworthManualFormFieldName\x12*\n&NETWORTH_MANUAL_FORM_FIELD_UNSPECIFIED\x10\x00\x12.\n*NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_NAME\x10\x01\x12-\n)NETWORTH_MANUAL_FORM_FIELD_INVESTED_VALUE\x10\x02\x12,\n(NETWORTH_MANUAL_FORM_FIELD_CURRENT_VALUE\x10\x03\x12.\n*NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_DATE\x10\x04\x12.\n*NETWORTH_MANUAL_FORM_FIELD_EVALUATION_DATE\x10\x05\x12'\n#NETWORTH_MANUAL_FORM_FIELD_AMC_NAME\x10\x06\x12'\n#NETWORTH_MANUAL_FORM_FIELD_CATEGORY\x10\x07\x12'\n#NETWORTH_MANUAL_FORM_FIELD_FOLIO_ID\x10\x08\x12*\n&NETWORTH_MANUAL_FORM_FIELD_BROKER_CODE\x10\t\x12&\n\"NETWORTH_MANUAL_FORM_FIELD_REMARKS\x10\n\x12,\n(NETWORTH_MANUAL_FORM_FIELD_MATURITY_DATE\x10\x0b\x12(\n$NETWORTH_MANUAL_FORM_FIELD_NUM_UNITS\x10\x0c\x12-\n)NETWORTH_MANUAL_FORM_FIELD_QUANTITY_GRAMS\x10\r\x12(\n$NETWORTH_MANUAL_FORM_FIELD_GOLD_TYPE\x10\x0e*\xb2\x01\n\x15NetworthAggregateType\x12'\n#NETWORTH_AGGREGATE_TYPE_UNSPECIFIED\x10\x00\x12#\n\x1fNETWORTH_AGGREGATE_TYPE_SAVINGS\x10\x01\x12\"\n\x1eNETWORTH_AGGREGATE_TYPE_ASSETS\x10\x02\x12'\n#NETWORTH_AGGREGATE_TYPE_LIABILITIES\x10\x03\x42n\n5com.github.epifi.gamma.api.frontend.insights.networthZ5github.com/epifi/gamma/api/frontend/insights/networthb\x06proto3"
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "api.frontend.insights.networth.internal.enums_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n5com.github.epifi.gamma.api.frontend.insights.networthZ5github.com/epifi/gamma/api/frontend/insights/networth"
    _globals["_NETWORTHCATEGORY"]._serialized_start = 84
    _globals["_NETWORTHCATEGORY"]._serialized_end = 1129
    _globals["_NETWORTHSECTIONTYPE"]._serialized_start = 1132
    _globals["_NETWORTHSECTIONTYPE"]._serialized_end = 1265
    _globals["_NETWORTHCATEGORYSTATUS"]._serialized_start = 1268
    _globals["_NETWORTHCATEGORYSTATUS"]._serialized_end = 1514
    _globals["_NETWORTHWIDGETSTATE"]._serialized_start = 1517
    _globals["_NETWORTHWIDGETSTATE"]._serialized_end = 1650
    _globals["_NETWORTHMANUALFORMFIELDNAME"]._serialized_start = 1653
    _globals["_NETWORTHMANUALFORMFIELDNAME"]._serialized_end = 2347
    _globals["_NETWORTHAGGREGATETYPE"]._serialized_start = 2350
    _globals["_NETWORTHAGGREGATETYPE"]._serialized_end = 2528
# @@protoc_insertion_point(module_scope)
