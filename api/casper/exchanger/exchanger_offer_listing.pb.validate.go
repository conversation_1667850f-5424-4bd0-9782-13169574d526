// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/casper/exchanger/exchanger_offer_listing.proto

package exchanger

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ExchangerOfferListing with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExchangerOfferListing) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExchangerOfferListing with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExchangerOfferListingMultiError, or nil if none found.
func (m *ExchangerOfferListing) ValidateAll() error {
	return m.validate(true)
}

func (m *ExchangerOfferListing) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ExchangerOfferId

	// no validation rules for ActiveSince

	// no validation rules for ActiveTill

	// no validation rules for DisplaySince

	// no validation rules for DisplayTill

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExchangerOfferListingValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExchangerOfferListingValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExchangerOfferListingValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExchangerOfferListingValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExchangerOfferListingValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExchangerOfferListingValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExchangerOfferListingValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExchangerOfferListingValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExchangerOfferListingValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExchangerOfferListingMultiError(errors)
	}

	return nil
}

// ExchangerOfferListingMultiError is an error wrapping multiple validation
// errors returned by ExchangerOfferListing.ValidateAll() if the designated
// constraints aren't met.
type ExchangerOfferListingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExchangerOfferListingMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExchangerOfferListingMultiError) AllErrors() []error { return m }

// ExchangerOfferListingValidationError is the validation error returned by
// ExchangerOfferListing.Validate if the designated constraints aren't met.
type ExchangerOfferListingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExchangerOfferListingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExchangerOfferListingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExchangerOfferListingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExchangerOfferListingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExchangerOfferListingValidationError) ErrorName() string {
	return "ExchangerOfferListingValidationError"
}

// Error satisfies the builtin error interface
func (e ExchangerOfferListingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExchangerOfferListing.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExchangerOfferListingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExchangerOfferListingValidationError{}

// Validate checks the field values on ExchangerOfferListings with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExchangerOfferListings) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExchangerOfferListings with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExchangerOfferListingsMultiError, or nil if none found.
func (m *ExchangerOfferListings) ValidateAll() error {
	return m.validate(true)
}

func (m *ExchangerOfferListings) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetExchangerOfferListings() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExchangerOfferListingsValidationError{
						field:  fmt.Sprintf("ExchangerOfferListings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExchangerOfferListingsValidationError{
						field:  fmt.Sprintf("ExchangerOfferListings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExchangerOfferListingsValidationError{
					field:  fmt.Sprintf("ExchangerOfferListings[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ExchangerOfferListingsMultiError(errors)
	}

	return nil
}

// ExchangerOfferListingsMultiError is an error wrapping multiple validation
// errors returned by ExchangerOfferListings.ValidateAll() if the designated
// constraints aren't met.
type ExchangerOfferListingsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExchangerOfferListingsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExchangerOfferListingsMultiError) AllErrors() []error { return m }

// ExchangerOfferListingsValidationError is the validation error returned by
// ExchangerOfferListings.Validate if the designated constraints aren't met.
type ExchangerOfferListingsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExchangerOfferListingsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExchangerOfferListingsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExchangerOfferListingsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExchangerOfferListingsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExchangerOfferListingsValidationError) ErrorName() string {
	return "ExchangerOfferListingsValidationError"
}

// Error satisfies the builtin error interface
func (e ExchangerOfferListingsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExchangerOfferListings.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExchangerOfferListingsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExchangerOfferListingsValidationError{}
