# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/card/provisioning/card_request.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

from api.card import card_block_pb2 as api_dot_card_dot_card__block__pb2
from api.card import card_pb2 as api_dot_card_dot_card__pb2
from api.card.enums import enums_pb2 as api_dot_card_dot_enums_dot_enums__pb2
from api.frontend.deeplink import deeplink_pb2 as api_dot_frontend_dot_deeplink_dot_deeplink__pb2
from api.types import address_pb2 as api_dot_types_dot_address__pb2
from api.vendorgateway import vendor_pb2 as api_dot_vendorgateway_dot_vendor__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n(api/card/provisioning/card_request.proto\x12\x11\x63\x61rd.provisioning\x1a\x13\x61pi/card/card.proto\x1a\x19\x61pi/card/card_block.proto\x1a\x1a\x61pi/card/enums/enums.proto\x1a$api/frontend/deeplink/deeplink.proto\x1a\x17\x61pi/types/address.proto\x1a\x1e\x61pi/vendorgateway/vendor.proto\x1a\x1fgoogle/protobuf/timestamp.proto"\xbf\x04\n\x0b\x43\x61rdRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0f\n\x07\x63\x61rd_id\x18\x02 \x01(\t\x12\x10\n\x08\x61\x63tor_id\x18\x03 \x01(\t\x12\x18\n\x10orchestration_id\x18\x04 \x01(\t\x12%\n\x06vendor\x18\x05 \x01(\x0e\x32\x15.vendorgateway.Vendor\x12>\n\x0frequest_details\x18\x06 \x01(\x0b\x32%.card.provisioning.CardRequestDetails\x12\x30\n\x0bnext_action\x18\x07 \x01(\x0b\x32\x1b.frontend.deeplink.Deeplink\x12\x36\n\rstage_details\x18\x08 \x01(\x0b\x32\x1f.card.provisioning.StageDetails\x12\x31\n\x08workflow\x18\t \x01(\x0e\x32\x1f.card.enums.CardRequestWorkflow\x12-\n\x06status\x18\n \x01(\x0e\x32\x1d.card.enums.CardRequestStatus\x12$\n\nprovenance\x18\x0b \x01(\x0e\x32\x10.card.Provenance\x12.\n\ncreated_at\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ndeleted_at\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp"n\n\x12\x43\x61rdRequestDetails\x12P\n\x1arenew_card_request_details\x18\x01 \x01(\x0b\x32*.card.provisioning.RenewCardRequestDetailsH\x00\x42\x06\n\x04\x44\x61ta"\xb2\x01\n\x17RenewCardRequestDetails\x12\x19\n\x11\x62lock_card_reason\x18\x01 \x01(\t\x12(\n\x0c\x61\x64\x64ress_type\x18\x02 \x01(\x0e\x32\x12.types.AddressType\x12!\n\tcard_form\x18\x03 \x01(\x0e\x32\x0e.card.CardForm\x12/\n\x15\x62lock_card_provenance\x18\x04 \x01(\x0e\x32\x10.card.Provenance"\xc2\x01\n\x0cStageDetails\x12S\n\x13\x63\x61rd_request_stages\x18\x01 \x03(\x0b\x32\x36.card.provisioning.StageDetails.CardRequestStagesEntry\x1a]\n\x16\x43\x61rdRequestStagesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x32\n\x05value\x18\x02 \x01(\x0b\x32#.card.provisioning.CardRequestStage:\x02\x38\x01"\xc6\x02\n\x10\x43\x61rdRequestStage\x12\x34\n\nstage_name\x18\x01 \x01(\x0e\x32 .card.enums.CardRequestStageName\x12\x32\n\x06status\x18\x02 \x01(\x0e\x32".card.enums.CardRequestStageStatus\x12\x39\n\nsub_status\x18\x03 \x01(\x0e\x32%.card.enums.CardRequestStageSubStatus\x12-\n\tstaled_at\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\r \x01(\x0b\x32\x1a.google.protobuf.TimestampB\\\n,com.github.epifi.gamma.api.card.provisioningZ,github.com/epifi/gamma/api/card/provisioningb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "api.card.provisioning.card_request_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n,com.github.epifi.gamma.api.card.provisioningZ,github.com/epifi/gamma/api/card/provisioning"
    _STAGEDETAILS_CARDREQUESTSTAGESENTRY._options = None
    _STAGEDETAILS_CARDREQUESTSTAGESENTRY._serialized_options = b"8\001"
    _globals["_CARDREQUEST"]._serialized_start = 268
    _globals["_CARDREQUEST"]._serialized_end = 843
    _globals["_CARDREQUESTDETAILS"]._serialized_start = 845
    _globals["_CARDREQUESTDETAILS"]._serialized_end = 955
    _globals["_RENEWCARDREQUESTDETAILS"]._serialized_start = 958
    _globals["_RENEWCARDREQUESTDETAILS"]._serialized_end = 1136
    _globals["_STAGEDETAILS"]._serialized_start = 1139
    _globals["_STAGEDETAILS"]._serialized_end = 1333
    _globals["_STAGEDETAILS_CARDREQUESTSTAGESENTRY"]._serialized_start = 1240
    _globals["_STAGEDETAILS_CARDREQUESTSTAGESENTRY"]._serialized_end = 1333
    _globals["_CARDREQUESTSTAGE"]._serialized_start = 1336
    _globals["_CARDREQUESTSTAGE"]._serialized_end = 1662
# @@protoc_insertion_point(module_scope)
