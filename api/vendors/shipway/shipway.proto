// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendors.shipway;

option go_package = "github.com/epifi/gamma/api/vendors/shipway";
option java_package = "com.github.epifi.gamma.api.vendors.shipway";

message BulkPushOrderDataRequest {
  // Login id / username
  string user_name = 1 [json_name = "username"];
  // Licence key
  string password = 2 [json_name = "password"];
  repeated ShipmentDetails shipments = 3 [json_name = "shipments"];
}

message BulkPushOrderDataResponse {
  repeated PushOrderRespData push_order_resp_data = 1;
}

message PushOrderRespData {
  // AWB number for the shipment
  string awb = 1 [json_name = "awb"];
  // Unique identifier for each order registered at shipway
  string order_id = 2 [json_name = "order_id"];
  string status = 3 [json_name = "status"];
  string msg = 4 [json_name = "msg"];
}

message ShipmentDetails {
  // Courier Id of shipment
  string carrier_id = 1 [json_name = "carrier_id"];
  // Air Waybill/Tracking number
  string awb = 2 [json_name = "awb"];
  // Unique identifier for each shipment
  string order_id = 3 [json_name = "order_id"];
  // Email of customer, can be set to N/A if not applicable
  string email = 4 [json_name = "email"];
  // Phone no of customer, can be set to N/A if not applicable
  string phone = 5 [json_name = "phone"];
  // First name of customer, can be set to N/A if not applicable
  string first_name = 6 [json_name = "first_name"];
  // Last name of customer, can be set to N/A if not applicable
  string last_name = 7 [json_name = "last_name"];
  // Description of products(Max 35 characters), can be set to N/A if not applicable
  string products = 8 [json_name = "products"];
  // Company name(Max 35 characters), can be set to N/A if not applicable
  string company = 9 [json_name = "company"];
  // Shipment Type(1 = forward ,2 = reverse)
  string shipment_type = 10 [json_name = "shipment_type"];
  // Printing vendor identifier
  string order_data = 11 [json_name = "order_data"];
}

message GetOrderShipmentDetailsRequest {
  // Login id / username
  string user_name = 1 [json_name = "username"];
  // Licence key
  string password = 2 [json_name = "password"];
  // Unique identifier for each shipment
  string order_id = 3 [json_name = "order_id"];
}

message GetOrderShipmentDetailsResponse {
  string status = 1 [json_name = "status"];

  ResponseData response_data = 2 [json_name = "response"];
}

message GetOrderShipmentDetailsErrorResponse {
  string status = 1 [json_name = "status"];

  string response_data = 2 [json_name = "response"];
}

message ResponseData {
  string pickup_date = 1 [json_name = "pickup_date"];
  // Description for the above status code
  string current_status = 2 [json_name = "current_status"];
  // Current status of the shipment. Ex : DEL,INT,OOD,UND,RTO,RTD (Delivered,In Transit,Out for Delivery,Undelivered,RTO,RTO Delivered etc)
  string current_status_code = 3 [json_name = "current_status_code"];
  string from = 4 [json_name = "from"];
  string to = 5 [json_name = "to"];
  // Detailed information for each scan for the order
  repeated Scan scans = 6 [json_name = "scan"];
  // Carrier partner for delivery
  string carrier = 7 [json_name = "carrier"];
  // AWB number for the shipment
  string awb_number = 8 [json_name = "awbno"];
  ExtraFields extra_fields = 9 [json_name = "extra_fields"];
  string tracking_url = 10 [json_name = "tracking_url"];
}

message Scan {
  string scan_time = 1 [json_name = "time"];
  string location = 2 [json_name = "location"];
  string status_detail = 3 [json_name = "status_detail"];
}

message ExtraFields {
  string expected_delivery_date = 1 [json_name = "expected_delivery_date"];
  string reference = 2 [json_name = "reference"];
}

message AddOrUpdateWebhookRequest {
  // Login id / username
  string user_name = 1 [json_name = "username"];
  // Licence key
  string password = 2 [json_name = "password"];

  string callback_url = 3 [json_name = "callback_url"];

  // Delivery events which we want to get notified for any order on the webhook
  // Ex : DEL,INT,OOD,UND,RTO,RTD (Delivered,In Transit,Out for Delivery,Undelivered,RTO,RTO Delivered etc)
  string events = 4 [json_name = "events"];
}

message AddOrUpdateWebhookResponse {
  string status = 1 [json_name = "status"];

  string message = 2 [json_name = "message"];

  string status_code = 3 [json_name = "status_code"];
}

message PushOrderDataRequest {
  // Login id / username
  string user_name = 1 [json_name = "username"];
  // Licence key
  string password = 2 [json_name = "password"];
  // Courier Id of shipment
  string carrier_id = 3 [json_name = "carrier_id"];
  // Air Waybill/Tracking number
  string awb = 4 [json_name = "awb"];
  // Unique identifier for each shipment
  string order_id = 5 [json_name = "order_id"];
  // Email of customer, can be set to N/A if not applicable
  string email = 6 [json_name = "email"];
  // Phone no of customer, can be set to N/A if not applicable
  string phone = 7 [json_name = "phone"];
  // First name of customer, can be set to N/A if not applicable
  string first_name = 8 [json_name = "first_name"];
  // Last name of customer, can be set to N/A if not applicable
  string last_name = 9 [json_name = "last_name"];
  // Description of products(Max 35 characters), can be set to N/A if not applicable
  string products = 10 [json_name = "products"];
  // Company name(Max 35 characters), can be set to N/A if not applicable
  string company = 11 [json_name = "company"];
  // Shipment Type(1 = forward ,2 = reverse)
  string shipment_type = 12 [json_name = "shipment_type"];
}

message PushOrderDataResponse {
  string status = 1 [json_name = "status"];

  string message = 2 [json_name = "msg"];
}

message CardTrackingCallbacksRequest {
  repeated CallbackTrackingStatus tracking_status_list = 1 [json_name = "status_feed"];
}

message CallbackTrackingStatus {
  // Unique identifier for each order registered at shipway
  string order_id = 1 [json_name = "order_id"];

  // AWB number for the shipment
  string awb_number = 2 [json_name = "awbno"];

  // Current status of the shipment. Ex : DEL,INT,OOD,UND,RTO,RTD (Delivered,In Transit,Out for Delivery,Undelivered,RTO,RTO Delivered etc)
  string current_status = 3 [json_name = "current_status"];

  // Description for the above status code
  string current_status_description = 4 [json_name = "current_status_desc"];

  // Time of last update of the shipment status
  string status_time = 5 [json_name = "status_time"];

  // Carrier partner for delivery
  string carrier = 6 [json_name = "carrier"];

  // URL at which the shipment can be tracked
  string tracking_url = 7 [json_name = "tracking_url"];

  // Detailed information for each scan for the order
  repeated CallbackScan scans = 8 [json_name = "scans"];

  // Date at which shipment was picked up
  string pickup_date = 9 [json_name = "pickupdate"];
}

message CallbackScan {
  // Time of the scan
  string time = 1 [json_name = "time"];
  // Status description after the scan
  string status = 2 [json_name = "status"];
  // Location of scan
  string location = 3 [json_name = "location"];
}
