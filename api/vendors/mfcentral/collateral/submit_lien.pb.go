// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/mfcentral/collateral/submit_lien.proto

package collateral

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SubmitLienRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReqId       string    `protobuf:"bytes,1,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	Pan         string    `protobuf:"bytes,2,opt,name=pan,proto3" json:"pan,omitempty"`
	Pekrn       string    `protobuf:"bytes,3,opt,name=pekrn,proto3" json:"pekrn,omitempty"`
	Mobile      string    `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Email       string    `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	ClientId    string    `protobuf:"bytes,6,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientRefNo string    `protobuf:"bytes,7,opt,name=client_ref_no,json=clientRefNo,proto3" json:"client_ref_no,omitempty"`
	LenderCode  string    `protobuf:"bytes,8,opt,name=lender_code,json=lenderCode,proto3" json:"lender_code,omitempty"`
	Data        *LienData `protobuf:"bytes,9,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *SubmitLienRequest) Reset() {
	*x = SubmitLienRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_mfcentral_collateral_submit_lien_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitLienRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitLienRequest) ProtoMessage() {}

func (x *SubmitLienRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_mfcentral_collateral_submit_lien_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitLienRequest.ProtoReflect.Descriptor instead.
func (*SubmitLienRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_mfcentral_collateral_submit_lien_proto_rawDescGZIP(), []int{0}
}

func (x *SubmitLienRequest) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *SubmitLienRequest) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *SubmitLienRequest) GetPekrn() string {
	if x != nil {
		return x.Pekrn
	}
	return ""
}

func (x *SubmitLienRequest) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *SubmitLienRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *SubmitLienRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *SubmitLienRequest) GetClientRefNo() string {
	if x != nil {
		return x.ClientRefNo
	}
	return ""
}

func (x *SubmitLienRequest) GetLenderCode() string {
	if x != nil {
		return x.LenderCode
	}
	return ""
}

func (x *SubmitLienRequest) GetData() *LienData {
	if x != nil {
		return x.Data
	}
	return nil
}

type SubmitLienResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReqId                int64    `protobuf:"varint,1,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	OtpRef               string   `protobuf:"bytes,2,opt,name=otp_ref,json=otpRef,proto3" json:"otp_ref,omitempty"`
	UserSubjectReference string   `protobuf:"bytes,3,opt,name=user_subject_reference,json=userSubjectReference,proto3" json:"user_subject_reference,omitempty"`
	ClientRefNo          string   `protobuf:"bytes,4,opt,name=client_ref_no,json=clientRefNo,proto3" json:"client_ref_no,omitempty"`
	Errors               []*Error `protobuf:"bytes,5,rep,name=errors,proto3" json:"errors,omitempty"`
}

func (x *SubmitLienResponse) Reset() {
	*x = SubmitLienResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_mfcentral_collateral_submit_lien_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitLienResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitLienResponse) ProtoMessage() {}

func (x *SubmitLienResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_mfcentral_collateral_submit_lien_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitLienResponse.ProtoReflect.Descriptor instead.
func (*SubmitLienResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_mfcentral_collateral_submit_lien_proto_rawDescGZIP(), []int{1}
}

func (x *SubmitLienResponse) GetReqId() int64 {
	if x != nil {
		return x.ReqId
	}
	return 0
}

func (x *SubmitLienResponse) GetOtpRef() string {
	if x != nil {
		return x.OtpRef
	}
	return ""
}

func (x *SubmitLienResponse) GetUserSubjectReference() string {
	if x != nil {
		return x.UserSubjectReference
	}
	return ""
}

func (x *SubmitLienResponse) GetClientRefNo() string {
	if x != nil {
		return x.ClientRefNo
	}
	return ""
}

func (x *SubmitLienResponse) GetErrors() []*Error {
	if x != nil {
		return x.Errors
	}
	return nil
}

type SubmitLienResponseErr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errors   []*Error `protobuf:"bytes,1,rep,name=errors,proto3" json:"errors,omitempty"`
	ReqId    string   `protobuf:"bytes,2,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	Response string   `protobuf:"bytes,3,opt,name=response,proto3" json:"response,omitempty"`
}

func (x *SubmitLienResponseErr) Reset() {
	*x = SubmitLienResponseErr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_mfcentral_collateral_submit_lien_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitLienResponseErr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitLienResponseErr) ProtoMessage() {}

func (x *SubmitLienResponseErr) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_mfcentral_collateral_submit_lien_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitLienResponseErr.ProtoReflect.Descriptor instead.
func (*SubmitLienResponseErr) Descriptor() ([]byte, []int) {
	return file_api_vendors_mfcentral_collateral_submit_lien_proto_rawDescGZIP(), []int{2}
}

func (x *SubmitLienResponseErr) GetErrors() []*Error {
	if x != nil {
		return x.Errors
	}
	return nil
}

func (x *SubmitLienResponseErr) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *SubmitLienResponseErr) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

var File_api_vendors_mfcentral_collateral_submit_lien_proto protoreflect.FileDescriptor

var file_api_vendors_mfcentral_collateral_submit_lien_proto_rawDesc = []byte{
	0x0a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x66,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72,
	0x61, 0x6c, 0x2f, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x65, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x66,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72,
	0x61, 0x6c, 0x1a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f,
	0x6d, 0x66, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x74,
	0x65, 0x72, 0x61, 0x6c, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x66,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72,
	0x61, 0x6c, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x65, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9e, 0x02, 0x0a, 0x11, 0x53, 0x75, 0x62, 0x6d, 0x69,
	0x74, 0x4c, 0x69, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06,
	0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65,
	0x71, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x65, 0x6b, 0x72, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x65, 0x6b, 0x72, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x6d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x66, 0x5f, 0x6e, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x4e, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3a, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x66, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x63, 0x6f,
	0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x4c, 0x69, 0x65, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xdb, 0x01, 0x0a, 0x12, 0x53, 0x75, 0x62, 0x6d,
	0x69, 0x74, 0x4c, 0x69, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x15,
	0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x72, 0x65, 0x71, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x74, 0x70, 0x5f, 0x72, 0x65, 0x66,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x74, 0x70, 0x52, 0x65, 0x66, 0x12, 0x34,
	0x0a, 0x16, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14,
	0x75, 0x73, 0x65, 0x72, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72,
	0x65, 0x66, 0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x4e, 0x6f, 0x12, 0x3b, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2e, 0x6d, 0x66, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x63, 0x6f, 0x6c,
	0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x06, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x22, 0x87, 0x01, 0x0a, 0x15, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x4c, 0x69, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x45, 0x72, 0x72, 0x12,
	0x3b, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x66, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x61, 0x6c, 0x2e, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x15, 0x0a, 0x06,
	0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65,
	0x71, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x39, 0x5a, 0x37, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x66, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2f,
	0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_vendors_mfcentral_collateral_submit_lien_proto_rawDescOnce sync.Once
	file_api_vendors_mfcentral_collateral_submit_lien_proto_rawDescData = file_api_vendors_mfcentral_collateral_submit_lien_proto_rawDesc
)

func file_api_vendors_mfcentral_collateral_submit_lien_proto_rawDescGZIP() []byte {
	file_api_vendors_mfcentral_collateral_submit_lien_proto_rawDescOnce.Do(func() {
		file_api_vendors_mfcentral_collateral_submit_lien_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_mfcentral_collateral_submit_lien_proto_rawDescData)
	})
	return file_api_vendors_mfcentral_collateral_submit_lien_proto_rawDescData
}

var file_api_vendors_mfcentral_collateral_submit_lien_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_vendors_mfcentral_collateral_submit_lien_proto_goTypes = []interface{}{
	(*SubmitLienRequest)(nil),     // 0: vendors.mfcentral.collateral.SubmitLienRequest
	(*SubmitLienResponse)(nil),    // 1: vendors.mfcentral.collateral.SubmitLienResponse
	(*SubmitLienResponseErr)(nil), // 2: vendors.mfcentral.collateral.SubmitLienResponseErr
	(*LienData)(nil),              // 3: vendors.mfcentral.collateral.LienData
	(*Error)(nil),                 // 4: vendors.mfcentral.collateral.Error
}
var file_api_vendors_mfcentral_collateral_submit_lien_proto_depIdxs = []int32{
	3, // 0: vendors.mfcentral.collateral.SubmitLienRequest.data:type_name -> vendors.mfcentral.collateral.LienData
	4, // 1: vendors.mfcentral.collateral.SubmitLienResponse.errors:type_name -> vendors.mfcentral.collateral.Error
	4, // 2: vendors.mfcentral.collateral.SubmitLienResponseErr.errors:type_name -> vendors.mfcentral.collateral.Error
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_vendors_mfcentral_collateral_submit_lien_proto_init() }
func file_api_vendors_mfcentral_collateral_submit_lien_proto_init() {
	if File_api_vendors_mfcentral_collateral_submit_lien_proto != nil {
		return
	}
	file_api_vendors_mfcentral_collateral_error_proto_init()
	file_api_vendors_mfcentral_collateral_validate_lien_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_mfcentral_collateral_submit_lien_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitLienRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_mfcentral_collateral_submit_lien_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitLienResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_mfcentral_collateral_submit_lien_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitLienResponseErr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_mfcentral_collateral_submit_lien_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_mfcentral_collateral_submit_lien_proto_goTypes,
		DependencyIndexes: file_api_vendors_mfcentral_collateral_submit_lien_proto_depIdxs,
		MessageInfos:      file_api_vendors_mfcentral_collateral_submit_lien_proto_msgTypes,
	}.Build()
	File_api_vendors_mfcentral_collateral_submit_lien_proto = out.File
	file_api_vendors_mfcentral_collateral_submit_lien_proto_rawDesc = nil
	file_api_vendors_mfcentral_collateral_submit_lien_proto_goTypes = nil
	file_api_vendors_mfcentral_collateral_submit_lien_proto_depIdxs = nil
}
