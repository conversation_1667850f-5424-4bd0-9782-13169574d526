# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/quest/developer/developer.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n#api/quest/developer/developer.proto\x12\x13\x61pi.quest.developer*\xbd\x01\n\x0bQuestEntity\x12\x1c\n\x18QUEST_ENTITY_UNSPECIFIED\x10\x00\x12\x0e\n\nEXPERIMENT\x10\x01\x12\x16\n\x12\x45XPERIMENT_VERSION\x10\x02\x12\x0c\n\x08VARIABLE\x10\x03\x12\x0b\n\x07VARIANT\x10\x04\x12\x18\n\x14VARIANT_VARIABLE_MAP\x10\x05\x12\x15\n\x11REDIS_EXPERIMENTS\x10\x06\x12\x1c\n\x18VARIANT_EVALUATION_VALUE\x10\x07\x42X\n*com.github.epifi.gamma.api.quest.developerZ*github.com/epifi/gamma/api/quest/developerb\x06proto3"
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.quest.developer.developer_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n*com.github.epifi.gamma.api.quest.developerZ*github.com/epifi/gamma/api/quest/developer"
    _globals["_QUESTENTITY"]._serialized_start = 61
    _globals["_QUESTENTITY"]._serialized_end = 250
# @@protoc_insertion_point(module_scope)
