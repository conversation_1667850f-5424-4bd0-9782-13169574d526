# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/quest/manager/service.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import field_mask_pb2 as google_dot_protobuf_dot_field__mask__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

from api.pkg.web import values_pb2 as api_dot_pkg_dot_web_dot_values__pb2
from api.quest.types import event_details_pb2 as api_dot_quest_dot_types_dot_event__details__pb2
from api.quest.types import experiment_pb2 as api_dot_quest_dot_types_dot_experiment__pb2
from api.quest.types import (
    experiment_version_pb2 as api_dot_quest_dot_types_dot_experiment__version__pb2,
)
from api.quest.types import layer_pb2 as api_dot_quest_dot_types_dot_layer__pb2
from api.quest.types import variable_pb2 as api_dot_quest_dot_types_dot_variable__pb2
from api.rpc import page_pb2 as api_dot_rpc_dot_page__pb2
from api.rpc import status_pb2 as api_dot_rpc_dot_status__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x1f\x61pi/quest/manager/service.proto\x12\x11\x61pi.quest.manager\x1a#api/quest/types/event_details.proto\x1a api/quest/types/experiment.proto\x1a(api/quest/types/experiment_version.proto\x1a\x1b\x61pi/quest/types/layer.proto\x1a\x1e\x61pi/quest/types/variable.proto\x1a\x12\x61pi/rpc/page.proto\x1a\x14\x61pi/rpc/status.proto\x1a google/protobuf/field_mask.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x18\x61pi/pkg/web/values.proto"K\n\x1b\x43reateQuestVariablesRequest\x12,\n\tvariables\x18\x01 \x03(\x0b\x32\x19.api.quest.types.Variable"i\n\x1c\x43reateQuestVariablesResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12,\n\tvariables\x18\x02 \x03(\x0b\x32\x19.api.quest.types.Variable"\x7f\n\x1dUpdateExperimentStatusRequest\x12\x0e\n\x06\x65xp_id\x18\x01 \x01(\t\x12\x35\n\nexp_status\x18\x02 \x01(\x0e\x32!.api.quest.types.ExperimentStatus\x12\x17\n\x0flast_updated_by\x18\x03 \x01(\t"g\n\x1eUpdateExperimentStatusResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12(\n\x03\x65xp\x18\x02 \x01(\x0b\x32\x1b.api.quest.types.Experiment"\x89\x01\n\x1dUpdateExperimentsCacheRequest\x12\x34\n\x10updated_at_start\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0eupdated_at_end\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp"=\n\x1eUpdateExperimentsCacheResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status"V\n\x1e\x43reateExperimentVersionRequest\x12\x34\n\x08\x65xp_vers\x18\x01 \x01(\x0b\x32".api.quest.types.ExperimentVersion"t\n\x1f\x43reateExperimentVersionResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x34\n\x08\x65xp_vers\x18\x02 \x01(\x0b\x32".api.quest.types.ExperimentVersion"\x8d\x01\n\x1eUpdateExperimentVersionRequest\x12\x34\n\x08\x65xp_vers\x18\x01 \x01(\x0b\x32".api.quest.types.ExperimentVersion\x12\x35\n\x11update_field_mask\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.FieldMask"t\n\x1fUpdateExperimentVersionResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x34\n\x08\x65xp_vers\x18\x02 \x01(\x0b\x32".api.quest.types.ExperimentVersion"\xfe\x02\n\x1cGetExperimentVersionsRequest\x12-\n\x0cpage_context\x18\x01 \x01(\x0b\x32\x17.rpc.PageContextRequest\x12.\n\nfield_mask\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.FieldMask\x12\x18\n\x10\x63reated_by_users\x18\x03 \x03(\t\x12\x1b\n\x13requested_reviewers\x18\x04 \x03(\t\x12\x34\n\x10\x63reated_at_start\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0e\x63reated_at_end\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x38\n\x06status\x18\x07 \x03(\x0e\x32(.api.quest.types.ExperimentVersionStatus\x12\x0f\n\x07\x65xp_ids\x18\x08 \x03(\t\x12\x13\n\x0b\x65xp_ver_ids\x18\t \x03(\t"\xa2\x01\n\x1dGetExperimentVersionsResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12.\n\x0cpage_context\x18\x02 \x01(\x0b\x32\x18.rpc.PageContextResponse\x12\x34\n\x08versions\x18\x03 \x03(\x0b\x32".api.quest.types.ExperimentVersion"\xa8\x04\n\x15GetExperimentsRequest\x12-\n\x0cpage_context\x18\x01 \x01(\x0b\x32\x17.rpc.PageContextRequest\x12.\n\nfield_mask\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.FieldMask\x12\x37\n\x13variants_field_mask\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.FieldMask\x12\x37\n\x13variable_field_mask\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.FieldMask\x12\x18\n\x10\x63reated_by_users\x18\x05 \x03(\t\x12\x19\n\x11reviewed_by_users\x18\x06 \x03(\t\x12\x34\n\x10\x63reated_at_start\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0e\x63reated_at_end\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\x06status\x18\t \x03(\x0e\x32!.api.quest.types.ExperimentStatus\x12\x0f\n\x07\x65xp_ids\x18\n \x03(\t\x12\x16\n\x0evariable_paths\x18\x0b \x03(\t\x12\x0c\n\x04name\x18\x0c \x01(\t\x12\r\n\x05\x61reas\x18\r \x03(\t\x12\x16\n\x0evariable_areas\x18\x0e \x03(\t\x12\x0e\n\x06layers\x18\x0f \x03(\t"\x97\x01\n\x16GetExperimentsResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12.\n\x0cpage_context\x18\x02 \x01(\x0b\x32\x18.rpc.PageContextResponse\x12\x30\n\x0b\x65xperiments\x18\x03 \x03(\x0b\x32\x1b.api.quest.types.Experiment"V\n\x16\x43reateVariablesRequest\x12,\n\tvariables\x18\x01 \x03(\x0b\x32\x19.api.quest.types.Variable\x12\x0e\n\x06server\x18\x02 \x01(\t"t\n\x17\x43reateVariablesResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12,\n\tvariables\x18\x02 \x03(\x0b\x32\x19.api.quest.types.Variable\x12\x0e\n\x06server\x18\x03 \x01(\t"\xd9\x02\n\x13GetVariablesRequest\x12-\n\x0cpage_context\x18\x01 \x01(\x0b\x32\x17.rpc.PageContextRequest\x12.\n\nfield_mask\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.FieldMask\x12\n\n\x02id\x18\x03 \x03(\t\x12\x13\n\x0bpath_prefix\x18\x04 \x01(\t\x12\x0f\n\x07servers\x18\x05 \x03(\t\x12\r\n\x05\x61reas\x18\n \x03(\t\x12\x0e\n\x06layers\x18\x06 \x03(\t\x12\x0c\n\x04tags\x18\x07 \x03(\t\x12\x1a\n\x12is_config_variable\x18\x0b \x03(\x08\x12\x34\n\x10\x63reated_at_start\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0e\x63reated_at_end\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp"\x91\x01\n\x14GetVariablesResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12.\n\x0cpage_context\x18\x02 \x01(\x0b\x32\x18.rpc.PageContextResponse\x12,\n\tvariables\x18\x03 \x03(\x0b\x32\x19.api.quest.types.Variable"\x17\n\x15GetServersListRequest"F\n\x16GetServersListResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x0f\n\x07servers\x18\x02 \x03(\t"\x15\n\x13GetAreasListRequest"B\n\x14GetAreasListResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\r\n\x05\x61reas\x18\x02 \x03(\t"6\n\x1cGetDefaultConfigValueRequest\x12\x16\n\x0evariable_paths\x18\x01 \x03(\t"\xd1\x01\n\x1dGetDefaultConfigValueResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12L\n\x06values\x18\x02 \x03(\x0b\x32<.api.quest.manager.GetDefaultConfigValueResponse.ValuesEntry\x1a\x45\n\x0bValuesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.api.pkg.web.DataValue:\x02\x38\x01"\xfe\x01\n GetExperimentFromEntityIdRequest\x12\x38\n\tentity_id\x18\x01 \x01(\x0b\x32%.api.quest.manager.ExperimentEntityId\x12.\n\nfield_mask\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.FieldMask\x12\x37\n\x13variants_field_mask\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.FieldMask\x12\x37\n\x13variable_field_mask\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.FieldMask"q\n!GetExperimentFromEntityIdResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12/\n\nexperiment\x18\x02 \x01(\x0b\x32\x1b.api.quest.types.Experiment"R\n\x12\x45xperimentEntityId\x12\x10\n\x06\x65xp_id\x18\x01 \x01(\tH\x00\x12\x12\n\x08\x65xp_name\x18\x02 \x01(\tH\x00\x42\x16\n\x14\x65xperiment_entity_id"H\n*GetNumberOfUsersInSegmentExpressionRequest\x12\x1a\n\x12segment_expression\x18\x01 \x01(\t"c\n+GetNumberOfUsersInSegmentExpressionResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x17\n\x0fnumber_of_users\x18\x02 \x01(\x03"m\n\x1eGetMetricsForExperimentRequest\x12\x10\n\x06\x65xp_id\x18\x01 \x01(\tH\x00\x12\x12\n\x08\x65xp_name\x18\x02 \x01(\tH\x00\x12\x18\n\x0e\x65xp_version_id\x18\x03 \x01(\tH\x00\x42\x0b\n\tentity_id"o\n\x1fGetMetricsForExperimentResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12/\n\nexperiment\x18\x02 \x01(\x0b\x32\x1b.api.quest.types.Experiment"A\n#GetSegmentExpressionDynamismRequest\x12\x1a\n\x12segment_expression\x18\x01 \x01(\t"W\n$GetSegmentExpressionDynamismResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x12\n\nis_dynamic\x18\x02 \x01(\x08">\n ValidateSegmentExpressionRequest\x12\x1a\n\x12segment_expression\x18\x01 \x01(\t"R\n!ValidateSegmentExpressionResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x10\n\x08is_valid\x18\x02 \x01(\x08"\'\n\x15ValidateEventsRequest\x12\x0e\n\x06\x65vents\x18\x01 \x03(\t"\xbe\x01\n\x16ValidateEventsResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12R\n\rexistence_map\x18\x02 \x03(\x0b\x32;.api.quest.manager.ValidateEventsResponse.ExistenceMapEntry\x1a\x33\n\x11\x45xistenceMapEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01"\x19\n\x17GetAllLeafLayersRequest"_\n\x18GetAllLeafLayersResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12&\n\x06layers\x18\x02 \x03(\x0b\x32\x16.api.quest.types.Layer"=\n\x13\x43reateLayersRequest\x12&\n\x06layers\x18\x01 \x03(\x0b\x32\x16.api.quest.types.Layer"[\n\x14\x43reateLayersResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12&\n\x06layers\x18\x02 \x03(\x0b\x32\x16.api.quest.types.Layer"J\n GetConflictingExperimentsRequest\x12&\n\x06layers\x18\x01 \x03(\x0b\x32\x16.api.quest.types.Layer"r\n!GetConflictingExperimentsResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x30\n\x0b\x65xperiments\x18\x02 \x03(\x0b\x32\x1b.api.quest.types.Experiment"F\n\x18ValidateVariablesRequest\x12\x16\n\x0evariable_paths\x18\x01 \x03(\t\x12\x12\n\nlayer_name\x18\x02 \x01(\t"X\n\x19ValidateVariablesResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x1e\n\x16invalid_variable_paths\x18\x02 \x03(\t"H\n GetUserBucketAvailabilityRequest\x12\x12\n\nlayer_name\x18\x01 \x01(\t\x12\x10\n\x08\x65xp_name\x18\x02 \x01(\t"\xb8\x01\n!GetUserBucketAvailabilityResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x18\n\x10reserved_percent\x18\x02 \x01(\x03\x12\x19\n\x11remaining_percent\x18\x03 \x01(\x03\x12\'\n\x1fmax_user_buckets_per_experiment\x18\x04 \x01(\x03\x12\x18\n\x10percent_required\x18\x05 \x01(\x03"B\n\x19\x43reateEventDetailsRequest\x12\x12\n\ntable_name\x18\x01 \x01(\t\x12\x11\n\tfrom_date\x18\x02 \x01(\t"t\n\x1a\x43reateEventDetailsResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x39\n\x12\x65vent_details_list\x18\x02 \x03(\x0b\x32\x1d.api.quest.types.EventDetails"D\n\x19GetAllEventDetailsRequest\x12\x13\n\x0bschema_name\x18\x01 \x01(\t\x12\x12\n\ntable_name\x18\x02 \x01(\t"t\n\x1aGetAllEventDetailsResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x39\n\x12\x65vent_details_list\x18\x02 \x03(\x0b\x32\x1d.api.quest.types.EventDetails"\\\n\x1aSaveExperimentDraftRequest\x12>\n\x12\x65xperiment_version\x18\x01 \x01(\x0b\x32".api.quest.types.ExperimentVersion"z\n\x1bSaveExperimentDraftResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12>\n\x12\x65xperiment_version\x18\x02 \x01(\x0b\x32".api.quest.types.ExperimentVersion2\xf1\x18\n\x07Manager\x12\x82\x01\n\x17\x43reateExperimentVersion\x12\x31.api.quest.manager.CreateExperimentVersionRequest\x1a\x32.api.quest.manager.CreateExperimentVersionResponse"\x00\x12\x82\x01\n\x17UpdateExperimentVersion\x12\x31.api.quest.manager.UpdateExperimentVersionRequest\x1a\x32.api.quest.manager.UpdateExperimentVersionResponse"\x00\x12|\n\x15GetExperimentVersions\x12/.api.quest.manager.GetExperimentVersionsRequest\x1a\x30.api.quest.manager.GetExperimentVersionsResponse"\x00\x12g\n\x0eGetExperiments\x12(.api.quest.manager.GetExperimentsRequest\x1a).api.quest.manager.GetExperimentsResponse"\x00\x12j\n\x0f\x43reateVariables\x12).api.quest.manager.CreateVariablesRequest\x1a*.api.quest.manager.CreateVariablesResponse"\x00\x12\x61\n\x0cGetVariables\x12&.api.quest.manager.GetVariablesRequest\x1a\'.api.quest.manager.GetVariablesResponse"\x00\x12\x7f\n\x16UpdateExperimentsCache\x12\x30.api.quest.manager.UpdateExperimentsCacheRequest\x1a\x31.api.quest.manager.UpdateExperimentsCacheResponse"\x00\x12\x7f\n\x16UpdateExperimentStatus\x12\x30.api.quest.manager.UpdateExperimentStatusRequest\x1a\x31.api.quest.manager.UpdateExperimentStatusResponse"\x00\x12g\n\x0eGetServersList\x12(.api.quest.manager.GetServersListRequest\x1a).api.quest.manager.GetServersListResponse"\x00\x12\x61\n\x0cGetAreasList\x12&.api.quest.manager.GetAreasListRequest\x1a\'.api.quest.manager.GetAreasListResponse"\x00\x12z\n\x15GetDefaultConfigValue\x12/.api.quest.manager.GetDefaultConfigValueRequest\x1a\x30.api.quest.manager.GetDefaultConfigValueResponse\x12\x88\x01\n\x19GetExperimentFromEntityId\x12\x33.api.quest.manager.GetExperimentFromEntityIdRequest\x1a\x34.api.quest.manager.GetExperimentFromEntityIdResponse"\x00\x12\xa4\x01\n#GetNumberOfUsersInSegmentExpression\x12=.api.quest.manager.GetNumberOfUsersInSegmentExpressionRequest\x1a>.api.quest.manager.GetNumberOfUsersInSegmentExpressionResponse\x12\x82\x01\n\x17GetMetricsForExperiment\x12\x31.api.quest.manager.GetMetricsForExperimentRequest\x1a\x32.api.quest.manager.GetMetricsForExperimentResponse"\x00\x12\x8f\x01\n\x1cGetSegmentExpressionDynamism\x12\x36.api.quest.manager.GetSegmentExpressionDynamismRequest\x1a\x37.api.quest.manager.GetSegmentExpressionDynamismResponse\x12\x86\x01\n\x19ValidateSegmentExpression\x12\x33.api.quest.manager.ValidateSegmentExpressionRequest\x1a\x34.api.quest.manager.ValidateSegmentExpressionResponse\x12\x65\n\x0eValidateEvents\x12(.api.quest.manager.ValidateEventsRequest\x1a).api.quest.manager.ValidateEventsResponse\x12k\n\x10GetAllLeafLayers\x12*.api.quest.manager.GetAllLeafLayersRequest\x1a+.api.quest.manager.GetAllLeafLayersResponse\x12_\n\x0c\x43reateLayers\x12&.api.quest.manager.CreateLayersRequest\x1a\'.api.quest.manager.CreateLayersResponse\x12\x86\x01\n\x19GetConflictingExperiments\x12\x33.api.quest.manager.GetConflictingExperimentsRequest\x1a\x34.api.quest.manager.GetConflictingExperimentsResponse\x12n\n\x11ValidateVariables\x12+.api.quest.manager.ValidateVariablesRequest\x1a,.api.quest.manager.ValidateVariablesResponse\x12\x86\x01\n\x19GetUserBucketAvailability\x12\x33.api.quest.manager.GetUserBucketAvailabilityRequest\x1a\x34.api.quest.manager.GetUserBucketAvailabilityResponse\x12w\n\x14\x43reateQuestVariables\x12..api.quest.manager.CreateQuestVariablesRequest\x1a/.api.quest.manager.CreateQuestVariablesResponse\x12q\n\x12\x43reateEventDetails\x12,.api.quest.manager.CreateEventDetailsRequest\x1a-.api.quest.manager.CreateEventDetailsResponse\x12q\n\x12GetAllEventDetails\x12,.api.quest.manager.GetAllEventDetailsRequest\x1a-.api.quest.manager.GetAllEventDetailsResponse\x12t\n\x13SaveExperimentDraft\x12-.api.quest.manager.SaveExperimentDraftRequest\x1a..api.quest.manager.SaveExperimentDraftResponseBT\n(com.github.epifi.gamma.api.quest/managerZ(github.com/epifi/gamma/api/quest/managerb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.quest.manager.service_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n(com.github.epifi.gamma.api.quest/managerZ(github.com/epifi/gamma/api/quest/manager"
    )
    _GETDEFAULTCONFIGVALUERESPONSE_VALUESENTRY._options = None
    _GETDEFAULTCONFIGVALUERESPONSE_VALUESENTRY._serialized_options = b"8\001"
    _VALIDATEEVENTSRESPONSE_EXISTENCEMAPENTRY._options = None
    _VALIDATEEVENTSRESPONSE_EXISTENCEMAPENTRY._serialized_options = b"8\001"
    _globals["_CREATEQUESTVARIABLESREQUEST"]._serialized_start = 363
    _globals["_CREATEQUESTVARIABLESREQUEST"]._serialized_end = 438
    _globals["_CREATEQUESTVARIABLESRESPONSE"]._serialized_start = 440
    _globals["_CREATEQUESTVARIABLESRESPONSE"]._serialized_end = 545
    _globals["_UPDATEEXPERIMENTSTATUSREQUEST"]._serialized_start = 547
    _globals["_UPDATEEXPERIMENTSTATUSREQUEST"]._serialized_end = 674
    _globals["_UPDATEEXPERIMENTSTATUSRESPONSE"]._serialized_start = 676
    _globals["_UPDATEEXPERIMENTSTATUSRESPONSE"]._serialized_end = 779
    _globals["_UPDATEEXPERIMENTSCACHEREQUEST"]._serialized_start = 782
    _globals["_UPDATEEXPERIMENTSCACHEREQUEST"]._serialized_end = 919
    _globals["_UPDATEEXPERIMENTSCACHERESPONSE"]._serialized_start = 921
    _globals["_UPDATEEXPERIMENTSCACHERESPONSE"]._serialized_end = 982
    _globals["_CREATEEXPERIMENTVERSIONREQUEST"]._serialized_start = 984
    _globals["_CREATEEXPERIMENTVERSIONREQUEST"]._serialized_end = 1070
    _globals["_CREATEEXPERIMENTVERSIONRESPONSE"]._serialized_start = 1072
    _globals["_CREATEEXPERIMENTVERSIONRESPONSE"]._serialized_end = 1188
    _globals["_UPDATEEXPERIMENTVERSIONREQUEST"]._serialized_start = 1191
    _globals["_UPDATEEXPERIMENTVERSIONREQUEST"]._serialized_end = 1332
    _globals["_UPDATEEXPERIMENTVERSIONRESPONSE"]._serialized_start = 1334
    _globals["_UPDATEEXPERIMENTVERSIONRESPONSE"]._serialized_end = 1450
    _globals["_GETEXPERIMENTVERSIONSREQUEST"]._serialized_start = 1453
    _globals["_GETEXPERIMENTVERSIONSREQUEST"]._serialized_end = 1835
    _globals["_GETEXPERIMENTVERSIONSRESPONSE"]._serialized_start = 1838
    _globals["_GETEXPERIMENTVERSIONSRESPONSE"]._serialized_end = 2000
    _globals["_GETEXPERIMENTSREQUEST"]._serialized_start = 2003
    _globals["_GETEXPERIMENTSREQUEST"]._serialized_end = 2555
    _globals["_GETEXPERIMENTSRESPONSE"]._serialized_start = 2558
    _globals["_GETEXPERIMENTSRESPONSE"]._serialized_end = 2709
    _globals["_CREATEVARIABLESREQUEST"]._serialized_start = 2711
    _globals["_CREATEVARIABLESREQUEST"]._serialized_end = 2797
    _globals["_CREATEVARIABLESRESPONSE"]._serialized_start = 2799
    _globals["_CREATEVARIABLESRESPONSE"]._serialized_end = 2915
    _globals["_GETVARIABLESREQUEST"]._serialized_start = 2918
    _globals["_GETVARIABLESREQUEST"]._serialized_end = 3263
    _globals["_GETVARIABLESRESPONSE"]._serialized_start = 3266
    _globals["_GETVARIABLESRESPONSE"]._serialized_end = 3411
    _globals["_GETSERVERSLISTREQUEST"]._serialized_start = 3413
    _globals["_GETSERVERSLISTREQUEST"]._serialized_end = 3436
    _globals["_GETSERVERSLISTRESPONSE"]._serialized_start = 3438
    _globals["_GETSERVERSLISTRESPONSE"]._serialized_end = 3508
    _globals["_GETAREASLISTREQUEST"]._serialized_start = 3510
    _globals["_GETAREASLISTREQUEST"]._serialized_end = 3531
    _globals["_GETAREASLISTRESPONSE"]._serialized_start = 3533
    _globals["_GETAREASLISTRESPONSE"]._serialized_end = 3599
    _globals["_GETDEFAULTCONFIGVALUEREQUEST"]._serialized_start = 3601
    _globals["_GETDEFAULTCONFIGVALUEREQUEST"]._serialized_end = 3655
    _globals["_GETDEFAULTCONFIGVALUERESPONSE"]._serialized_start = 3658
    _globals["_GETDEFAULTCONFIGVALUERESPONSE"]._serialized_end = 3867
    _globals["_GETDEFAULTCONFIGVALUERESPONSE_VALUESENTRY"]._serialized_start = 3798
    _globals["_GETDEFAULTCONFIGVALUERESPONSE_VALUESENTRY"]._serialized_end = 3867
    _globals["_GETEXPERIMENTFROMENTITYIDREQUEST"]._serialized_start = 3870
    _globals["_GETEXPERIMENTFROMENTITYIDREQUEST"]._serialized_end = 4124
    _globals["_GETEXPERIMENTFROMENTITYIDRESPONSE"]._serialized_start = 4126
    _globals["_GETEXPERIMENTFROMENTITYIDRESPONSE"]._serialized_end = 4239
    _globals["_EXPERIMENTENTITYID"]._serialized_start = 4241
    _globals["_EXPERIMENTENTITYID"]._serialized_end = 4323
    _globals["_GETNUMBEROFUSERSINSEGMENTEXPRESSIONREQUEST"]._serialized_start = 4325
    _globals["_GETNUMBEROFUSERSINSEGMENTEXPRESSIONREQUEST"]._serialized_end = 4397
    _globals["_GETNUMBEROFUSERSINSEGMENTEXPRESSIONRESPONSE"]._serialized_start = 4399
    _globals["_GETNUMBEROFUSERSINSEGMENTEXPRESSIONRESPONSE"]._serialized_end = 4498
    _globals["_GETMETRICSFOREXPERIMENTREQUEST"]._serialized_start = 4500
    _globals["_GETMETRICSFOREXPERIMENTREQUEST"]._serialized_end = 4609
    _globals["_GETMETRICSFOREXPERIMENTRESPONSE"]._serialized_start = 4611
    _globals["_GETMETRICSFOREXPERIMENTRESPONSE"]._serialized_end = 4722
    _globals["_GETSEGMENTEXPRESSIONDYNAMISMREQUEST"]._serialized_start = 4724
    _globals["_GETSEGMENTEXPRESSIONDYNAMISMREQUEST"]._serialized_end = 4789
    _globals["_GETSEGMENTEXPRESSIONDYNAMISMRESPONSE"]._serialized_start = 4791
    _globals["_GETSEGMENTEXPRESSIONDYNAMISMRESPONSE"]._serialized_end = 4878
    _globals["_VALIDATESEGMENTEXPRESSIONREQUEST"]._serialized_start = 4880
    _globals["_VALIDATESEGMENTEXPRESSIONREQUEST"]._serialized_end = 4942
    _globals["_VALIDATESEGMENTEXPRESSIONRESPONSE"]._serialized_start = 4944
    _globals["_VALIDATESEGMENTEXPRESSIONRESPONSE"]._serialized_end = 5026
    _globals["_VALIDATEEVENTSREQUEST"]._serialized_start = 5028
    _globals["_VALIDATEEVENTSREQUEST"]._serialized_end = 5067
    _globals["_VALIDATEEVENTSRESPONSE"]._serialized_start = 5070
    _globals["_VALIDATEEVENTSRESPONSE"]._serialized_end = 5260
    _globals["_VALIDATEEVENTSRESPONSE_EXISTENCEMAPENTRY"]._serialized_start = 5209
    _globals["_VALIDATEEVENTSRESPONSE_EXISTENCEMAPENTRY"]._serialized_end = 5260
    _globals["_GETALLLEAFLAYERSREQUEST"]._serialized_start = 5262
    _globals["_GETALLLEAFLAYERSREQUEST"]._serialized_end = 5287
    _globals["_GETALLLEAFLAYERSRESPONSE"]._serialized_start = 5289
    _globals["_GETALLLEAFLAYERSRESPONSE"]._serialized_end = 5384
    _globals["_CREATELAYERSREQUEST"]._serialized_start = 5386
    _globals["_CREATELAYERSREQUEST"]._serialized_end = 5447
    _globals["_CREATELAYERSRESPONSE"]._serialized_start = 5449
    _globals["_CREATELAYERSRESPONSE"]._serialized_end = 5540
    _globals["_GETCONFLICTINGEXPERIMENTSREQUEST"]._serialized_start = 5542
    _globals["_GETCONFLICTINGEXPERIMENTSREQUEST"]._serialized_end = 5616
    _globals["_GETCONFLICTINGEXPERIMENTSRESPONSE"]._serialized_start = 5618
    _globals["_GETCONFLICTINGEXPERIMENTSRESPONSE"]._serialized_end = 5732
    _globals["_VALIDATEVARIABLESREQUEST"]._serialized_start = 5734
    _globals["_VALIDATEVARIABLESREQUEST"]._serialized_end = 5804
    _globals["_VALIDATEVARIABLESRESPONSE"]._serialized_start = 5806
    _globals["_VALIDATEVARIABLESRESPONSE"]._serialized_end = 5894
    _globals["_GETUSERBUCKETAVAILABILITYREQUEST"]._serialized_start = 5896
    _globals["_GETUSERBUCKETAVAILABILITYREQUEST"]._serialized_end = 5968
    _globals["_GETUSERBUCKETAVAILABILITYRESPONSE"]._serialized_start = 5971
    _globals["_GETUSERBUCKETAVAILABILITYRESPONSE"]._serialized_end = 6155
    _globals["_CREATEEVENTDETAILSREQUEST"]._serialized_start = 6157
    _globals["_CREATEEVENTDETAILSREQUEST"]._serialized_end = 6223
    _globals["_CREATEEVENTDETAILSRESPONSE"]._serialized_start = 6225
    _globals["_CREATEEVENTDETAILSRESPONSE"]._serialized_end = 6341
    _globals["_GETALLEVENTDETAILSREQUEST"]._serialized_start = 6343
    _globals["_GETALLEVENTDETAILSREQUEST"]._serialized_end = 6411
    _globals["_GETALLEVENTDETAILSRESPONSE"]._serialized_start = 6413
    _globals["_GETALLEVENTDETAILSRESPONSE"]._serialized_end = 6529
    _globals["_SAVEEXPERIMENTDRAFTREQUEST"]._serialized_start = 6531
    _globals["_SAVEEXPERIMENTDRAFTREQUEST"]._serialized_end = 6623
    _globals["_SAVEEXPERIMENTDRAFTRESPONSE"]._serialized_start = 6625
    _globals["_SAVEEXPERIMENTDRAFTRESPONSE"]._serialized_end = 6747
    _globals["_MANAGER"]._serialized_start = 6750
    _globals["_MANAGER"]._serialized_end = 9935
# @@protoc_insertion_point(module_scope)
