# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/quest/types/experiment_version.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

from api.quest.types import experiment_pb2 as api_dot_quest_dot_types_dot_experiment__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n(api/quest/types/experiment_version.proto\x12\x0f\x61pi.quest.types\x1a api/quest/types/experiment.proto\x1a\x1fgoogle/protobuf/timestamp.proto"\xf4\x02\n\x11\x45xperimentVersion\x12\n\n\x02id\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x38\n\x06status\x18\x03 \x01(\x0e\x32(.api.quest.types.ExperimentVersionStatus\x12-\n\x08\x65xp_data\x18\x04 \x01(\x0b\x32\x1b.api.quest.types.Experiment\x12\x1b\n\x13requested_reviewers\x18\x05 \x03(\t\x12\x12\n\ncreated_by\x18\x06 \x01(\t\x12\x14\n\x0cprocessed_by\x18\x07 \x01(\t\x12.\n\ncreated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ndeleted_at\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp*\xed\x01\n\x17\x45xperimentVersionStatus\x12)\n%EXPERIMENT_VERSION_STATUS_UNSPECIFIED\x10\x00\x12#\n\x1f\x45XPERIMENT_VERSION_STATUS_DRAFT\x10\x01\x12\x32\n.EXPERIMENT_VERSION_STATUS_WAITING_FOR_APPROVAL\x10\x02\x12&\n"EXPERIMENT_VERSION_STATUS_APPROVED\x10\x03\x12&\n"EXPERIMENT_VERSION_STATUS_DECLINED\x10\x04\x42P\n&com.github.epifi.gamma.api.quest.typesZ&github.com/epifi/gamma/api/quest/typesb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "api.quest.types.experiment_version_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n&com.github.epifi.gamma.api.quest.typesZ&github.com/epifi/gamma/api/quest/types"
    )
    _globals["_EXPERIMENTVERSIONSTATUS"]._serialized_start = 504
    _globals["_EXPERIMENTVERSIONSTATUS"]._serialized_end = 741
    _globals["_EXPERIMENTVERSION"]._serialized_start = 129
    _globals["_EXPERIMENTVERSION"]._serialized_end = 501
# @@protoc_insertion_point(module_scope)
