# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/quest/types/variant.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

from api.quest.types import variable_pb2 as api_dot_quest_dot_types_dot_variable__pb2
from api.types import phone_number_pb2 as api_dot_types_dot_phone__number__pb2
from api.user.group import enums_pb2 as api_dot_user_dot_group_dot_enums__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x1d\x61pi/quest/types/variant.proto\x12\x0f\x61pi.quest.types\x1a\x1e\x61pi/quest/types/variable.proto\x1a\x1c\x61pi/types/phone_number.proto\x1a\x1a\x61pi/user/group/enums.proto\x1a\x1fgoogle/protobuf/timestamp.proto"\xd7\x03\n\x07Variant\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12.\n\x06status\x18\x03 \x01(\x0e\x32\x1e.api.quest.types.VariantStatus\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x0e\n\x06\x65xp_id\x18\x05 \x01(\t\x12\x38\n\x0foverride_policy\x18\x06 \x01(\x0b\x32\x1f.api.quest.types.OverridePolicy\x12\x1a\n\x12is_control_variant\x18\x07 \x01(\x08\x12\x18\n\x10percentage_start\x18\x08 \x01(\x05\x12\x16\n\x0epercentage_end\x18\t \x01(\x05\x12\x17\n\x0flast_updated_by\x18\n \x01(\t\x12.\n\ncreated_at\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ndeleted_at\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\tvariables\x18\x0e \x03(\x0b\x32\x19.api.quest.types.Variable"z\n\x0eOverridePolicy\x12*\n\x0buser_groups\x18\x01 \x03(\x0e\x32\x15.user.group.UserGroup\x12)\n\rphone_numbers\x18\x02 \x03(\x0b\x32\x12.types.PhoneNumber\x12\x11\n\tactor_ids\x18\x03 \x03(\t*h\n\rVariantStatus\x12\x1e\n\x1aVARIANT_STATUS_UNSPECIFIED\x10\x00\x12\x1a\n\x16VARIANT_STATUS_ENABLED\x10\x01\x12\x1b\n\x17VARIANT_STATUS_DISABLED\x10\x02\x42P\n&com.github.epifi.gamma.api.quest.typesZ&github.com/epifi/gamma/api/quest/typesb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.quest.types.variant_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n&com.github.epifi.gamma.api.quest.typesZ&github.com/epifi/gamma/api/quest/types"
    )
    _globals["_VARIANTSTATUS"]._serialized_start = 771
    _globals["_VARIANTSTATUS"]._serialized_end = 875
    _globals["_VARIANT"]._serialized_start = 174
    _globals["_VARIANT"]._serialized_end = 645
    _globals["_OVERRIDEPOLICY"]._serialized_start = 647
    _globals["_OVERRIDEPOLICY"]._serialized_end = 769
# @@protoc_insertion_point(module_scope)
