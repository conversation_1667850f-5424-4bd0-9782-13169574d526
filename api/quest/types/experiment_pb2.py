# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/quest/types/experiment.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

from api.quest.types import variable_pb2 as api_dot_quest_dot_types_dot_variable__pb2
from api.quest.types import variant_pb2 as api_dot_quest_dot_types_dot_variant__pb2
from api.types import device_pb2 as api_dot_types_dot_device__pb2
from api.user.group import enums_pb2 as api_dot_user_dot_group_dot_enums__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n api/quest/types/experiment.proto\x12\x0f\x61pi.quest.types\x1a\x1e\x61pi/quest/types/variable.proto\x1a\x1d\x61pi/quest/types/variant.proto\x1a\x16\x61pi/types/device.proto\x1a\x1a\x61pi/user/group/enums.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1fgoogle/protobuf/timestamp.proto"\xc7\x05\n\nExperiment\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x0c\n\x04tags\x18\x04 \x03(\t\x12\x0c\n\x04\x61rea\x18\x05 \x01(\t\x12\x31\n\x06status\x18\x06 \x01(\x0e\x32!.api.quest.types.ExperimentStatus\x12\x37\n\tcondition\x18\x07 \x01(\x0b\x32$.api.quest.types.ExperimentCondition\x12+\n\x08\x64uration\x18\x08 \x01(\x0b\x32\x19.google.protobuf.Duration\x12.\n\nstart_time\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x0b \x01(\t\x12\x17\n\x0flast_updated_by\x18\x0c \x01(\t\x12.\n\ncreated_at\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ndeleted_at\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\tvariables\x18\x10 \x03(\x0b\x32\x19.api.quest.types.Variable\x12*\n\x08variants\x18\x11 \x03(\x0b\x32\x18.api.quest.types.Variant\x12&\n\x06\x65vents\x18\x12 \x03(\x0b\x32\x16.api.quest.types.Event\x12)\n\x07metrics\x18\x13 \x01(\x0b\x32\x18.api.quest.types.Metrics\x12\r\n\x05layer\x18\x14 \x01(\t"?\n\x0e\x45xperimentList\x12-\n\x08\x65lements\x18\x01 \x03(\x0b\x32\x1b.api.quest.types.Experiment"\xca\x02\n\x13\x45xperimentCondition\x12\x32\n\x13\x61llowed_user_groups\x18\x01 \x03(\x0e\x32\x15.user.group.UserGroup\x12\x42\n\x14\x61llowed_app_versions\x18\x02 \x03(\x0b\x32$.api.quest.types.AppVersionCondition\x12\x18\n\x10percent_required\x18\x03 \x01(\x05\x12<\n\tintervals\x18\x04 \x03(\x0b\x32).api.quest.types.UserLayerSegmentInterval\x12)\n\x07segment\x18\x05 \x01(\x0b\x32\x18.api.quest.types.Segment\x12\x1d\n\x15\x63ustom_attr_cond_expr\x18\x06 \x01(\t\x12\x19\n\x11\x63ustom_attributes\x18\x07 \x03(\t"Z\n\x18UserLayerSegmentInterval\x12\x1f\n\x17percent_start_inclusive\x18\x01 \x01(\x05\x12\x1d\n\x15percent_end_exclusive\x18\x02 \x01(\x05"\x9e\x01\n\x13\x41ppVersionCondition\x12!\n\x08platform\x18\x01 \x01(\x0e\x32\x0f.types.Platform\x12\x17\n\x0fmin_app_version\x18\x02 \x01(\x05\x12\x17\n\x0fmax_app_version\x18\x03 \x01(\x05\x12\x32\n\x13\x61llowed_user_groups\x18\x04 \x03(\x0e\x32\x15.user.group.UserGroup"8\n\x07Segment\x12\x17\n\x0fsegment_id_expr\x18\x01 \x01(\t\x12\x14\n\x0cuse_snapshot\x18\x02 \x01(\x08"\xe4\x01\n\x05\x45vent\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x0e\n\x06server\x18\x04 \x01(\t\x12\x0c\n\x04tags\x18\x05 \x03(\t\x12.\n\ncreated_at\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ndeleted_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp"R\n\x07Metrics\x12\x0e\n\x06\x65xp_id\x18\x01 \x01(\t\x12\x37\n\x0fvariant_metrics\x18\x02 \x03(\x0b\x32\x1e.api.quest.types.VariantMetric"n\n\rVariantMetric\x12\x12\n\nvariant_id\x18\x01 \x01(\t\x12\x14\n\x0cvariant_name\x18\x02 \x01(\t\x12\x33\n\revent_metrics\x18\x03 \x03(\x0b\x32\x1c.api.quest.types.EventMetric"j\n\x0b\x45ventMetric\x12\x12\n\nevent_name\x18\x01 \x01(\t\x12\x18\n\x10impression_count\x18\x02 \x01(\x03\x12\x13\n\x0b\x65vent_count\x18\x03 \x01(\x03\x12\x18\n\x10\x63onversion_ratio\x18\x04 \x01(\x01*\xb1\x01\n\x10\x45xperimentStatus\x12!\n\x1d\x45XPERIMENT_STATUS_UNSPECIFIED\x10\x00\x12\x1d\n\x19\x45XPERIMENT_STATUS_ENABLED\x10\x01\x12\x1e\n\x1a\x45XPERIMENT_STATUS_DISABLED\x10\x02\x12\x1c\n\x18\x45XPERIMENT_STATUS_CLOSED\x10\x03\x12\x1d\n\x19\x45XPERIMENT_STATUS_CREATED\x10\x04\x42P\n&com.github.epifi.gamma.api.quest.typesZ&github.com/epifi/gamma/api/quest/typesb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.quest.types.experiment_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n&com.github.epifi.gamma.api.quest.typesZ&github.com/epifi/gamma/api/quest/types"
    )
    _globals["_EXPERIMENTSTATUS"]._serialized_start = 2192
    _globals["_EXPERIMENTSTATUS"]._serialized_end = 2369
    _globals["_EXPERIMENT"]._serialized_start = 234
    _globals["_EXPERIMENT"]._serialized_end = 945
    _globals["_EXPERIMENTLIST"]._serialized_start = 947
    _globals["_EXPERIMENTLIST"]._serialized_end = 1010
    _globals["_EXPERIMENTCONDITION"]._serialized_start = 1013
    _globals["_EXPERIMENTCONDITION"]._serialized_end = 1343
    _globals["_USERLAYERSEGMENTINTERVAL"]._serialized_start = 1345
    _globals["_USERLAYERSEGMENTINTERVAL"]._serialized_end = 1435
    _globals["_APPVERSIONCONDITION"]._serialized_start = 1438
    _globals["_APPVERSIONCONDITION"]._serialized_end = 1596
    _globals["_SEGMENT"]._serialized_start = 1598
    _globals["_SEGMENT"]._serialized_end = 1654
    _globals["_EVENT"]._serialized_start = 1657
    _globals["_EVENT"]._serialized_end = 1885
    _globals["_METRICS"]._serialized_start = 1887
    _globals["_METRICS"]._serialized_end = 1969
    _globals["_VARIANTMETRIC"]._serialized_start = 1971
    _globals["_VARIANTMETRIC"]._serialized_end = 2081
    _globals["_EVENTMETRIC"]._serialized_start = 2083
    _globals["_EVENTMETRIC"]._serialized_end = 2189
# @@protoc_insertion_point(module_scope)
