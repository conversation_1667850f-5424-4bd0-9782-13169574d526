# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/quest/types/event_details.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n#api/quest/types/event_details.proto\x12\x0f\x61pi.quest.types\x1a\x1fgoogle/protobuf/timestamp.proto"\xe2\x01\n\x0c\x45ventDetails\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x12\n\ntable_name\x18\x03 \x01(\t\x12\x14\n\x0c\x65vent_schema\x18\x04 \x01(\t\x12.\n\ncreated_at\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ndeleted_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.TimestampBP\n&com.github.epifi.gamma.api.quest.typesZ&github.com/epifi/gamma/api/quest/typesb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.quest.types.event_details_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n&com.github.epifi.gamma.api.quest.typesZ&github.com/epifi/gamma/api/quest/types"
    )
    _globals["_EVENTDETAILS"]._serialized_start = 90
    _globals["_EVENTDETAILS"]._serialized_end = 316
# @@protoc_insertion_point(module_scope)
