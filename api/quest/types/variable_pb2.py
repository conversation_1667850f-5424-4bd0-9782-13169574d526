# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/quest/types/variable.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

from api.pkg.web import values_pb2 as api_dot_pkg_dot_web_dot_values__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x1e\x61pi/quest/types/variable.proto\x12\x0f\x61pi.quest.types\x1a\x18\x61pi/pkg/web/values.proto\x1a\x1fgoogle/protobuf/timestamp.proto"\x8b\x03\n\x08Variable\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\'\n\x08\x64\x61tatype\x18\x03 \x01(\x0b\x32\x15.api.pkg.web.Datatype\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x0e\n\x06server\x18\x05 \x01(\t\x12\x0c\n\x04\x61rea\x18\x0c \x01(\t\x12\r\n\x05layer\x18\x06 \x01(\t\x12\x0c\n\x04tags\x18\x07 \x03(\t\x12%\n\x05value\x18\x08 \x01(\x0b\x32\x16.api.pkg.web.DataValue\x12\x1a\n\x12is_config_variable\x18\r \x01(\x08\x12\x19\n\x11is_value_editable\x18\x0e \x01(\x08\x12.\n\ncreated_at\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ndeleted_at\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp"<\n\x0cVariableList\x12,\n\tvariables\x18\x01 \x03(\x0b\x32\x19.api.quest.types.VariableBP\n&com.github.epifi.gamma.api.quest.typesZ&github.com/epifi/gamma/api/quest/typesb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.quest.types.variable_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n&com.github.epifi.gamma.api.quest.typesZ&github.com/epifi/gamma/api/quest/types"
    )
    _globals["_VARIABLE"]._serialized_start = 111
    _globals["_VARIABLE"]._serialized_end = 506
    _globals["_VARIABLELIST"]._serialized_start = 508
    _globals["_VARIABLELIST"]._serialized_end = 568
# @@protoc_insertion_point(module_scope)
