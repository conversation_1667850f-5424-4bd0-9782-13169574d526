# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/quest/types/layer.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n\x1b\x61pi/quest/types/layer.proto\x12\x0f\x61pi.quest.types\x1a\x1fgoogle/protobuf/timestamp.proto\"\xfd\x02\n\x05Layer\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x14\n\x0cparent_layer\x18\x03 \x01(\t\x12\x1e\n\x16is_overlapping_allowed\x18\x04 \x01(\x08\x12\x1c\n\x14variable_expressions\x18\x05 \x03(\t\x12,\n\x06status\x18\x06 \x01(\x0e\x32\x1c.api.quest.types.LayerStatus\x12\x1f\n\x17max_user_segment_bucket\x18\n \x01(\r\x12'\n\x1fmax_user_segment_bucket_for_exp\x18\x0b \x01(\r\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ndeleted_at\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp*_\n\x0bLayerStatus\x12\x1c\n\x18LAYER_STATUS_UNSPECIFIED\x10\x00\x12\x17\n\x13LAYER_STATUS_ACTIVE\x10\x01\x12\x19\n\x15LAYER_STATUS_ARCHIVED\x10\x02\x42P\n&com.github.epifi.gamma.api.quest.typesZ&github.com/epifi/gamma/api/quest/typesb\x06proto3"
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.quest.types.layer_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n&com.github.epifi.gamma.api.quest.typesZ&github.com/epifi/gamma/api/quest/types"
    )
    _globals["_LAYERSTATUS"]._serialized_start = 465
    _globals["_LAYERSTATUS"]._serialized_end = 560
    _globals["_LAYER"]._serialized_start = 82
    _globals["_LAYER"]._serialized_end = 463
# @@protoc_insertion_point(module_scope)
