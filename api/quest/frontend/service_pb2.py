# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/quest/frontend/service.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from api.jarvis import header_pb2 as api_dot_jarvis_dot_header__pb2
from api.jarvis import method_options_pb2 as api_dot_jarvis_dot_method__options__pb2
from api.nebula.method_options import (
    method_options_pb2 as api_dot_nebula_dot_method__options_dot_method__options__pb2,
)
from api.nebula.request import header_pb2 as api_dot_nebula_dot_request_dot_header__pb2
from api.pkg.web import components_pb2 as api_dot_pkg_dot_web_dot_components__pb2
from api.pkg.web import enums_pb2 as api_dot_pkg_dot_web_dot_enums__pb2
from api.quest.frontend import (
    experiment_form_pb2 as api_dot_quest_dot_frontend_dot_experiment__form__pb2,
)
from api.quest.types import experiment_pb2 as api_dot_quest_dot_types_dot_experiment__pb2
from api.quest.types import (
    experiment_version_pb2 as api_dot_quest_dot_types_dot_experiment__version__pb2,
)
from api.rpc import page_pb2 as api_dot_rpc_dot_page__pb2
from api.rpc import status_pb2 as api_dot_rpc_dot_status__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n api/quest/frontend/service.proto\x12\x12\x61pi.quest.frontend\x1a.api/nebula/method_options/method_options.proto\x1a\x1f\x61pi/nebula/request/header.proto\x1a\x1c\x61pi/pkg/web/components.proto\x1a\x17\x61pi/pkg/web/enums.proto\x1a(api/quest/frontend/experiment_form.proto\x1a api/quest/types/experiment.proto\x1a(api/quest/types/experiment_version.proto\x1a\x12\x61pi/rpc/page.proto\x1a\x14\x61pi/rpc/status.proto\x1a\x17\x61pi/jarvis/header.proto\x1a\x1f\x61pi/jarvis/method_options.proto"\xb2\x01\n\x1aNewExperimentPageV2Request\x12.\n\x06header\x18\x01 \x01(\x0b\x32\x1a.api.nebula.request.HeaderB\x02\x18\x01\x12=\n\x0f\x65xperiment_form\x18\x02 \x01(\x0b\x32$.api.quest.frontend.ExperimentFormV2\x12%\n\rjarvis_header\x18\x03 \x01(\x0b\x32\x0e.jarvis.Header"y\n\x1bNewExperimentPageV2Response\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12=\n\x0f\x65xperiment_form\x18\x02 \x01(\x0b\x32$.api.quest.frontend.ExperimentFormV2"{\n\x0fHomePageRequest\x12.\n\x06header\x18\x01 \x01(\x0b\x32\x1a.api.nebula.request.HeaderB\x02\x18\x01\x12\x11\n\tuser_name\x18\x02 \x01(\t\x12%\n\rjarvis_header\x18\x03 \x01(\x0b\x32\x0e.jarvis.Header"\xd1\x03\n\x10HomePageResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12%\n\nhello_text\x18\x02 \x01(\x0b\x32\x11.api.pkg.web.Text\x12+\n\x10user_manual_link\x18\x03 \x01(\x0b\x32\x11.api.pkg.web.Link\x12\'\n\x0cmy_exp_title\x18\x04 \x01(\x0b\x32\x11.api.pkg.web.Text\x12\x30\n\x14my_exps_display_data\x18\x05 \x01(\x0b\x32\x12.api.pkg.web.Table\x12\x32\n\x0f\x61ll_my_exps_btn\x18\x06 \x01(\x0b\x32\x19.api.pkg.web.ActionButton\x12)\n\x0e\x61ll_exps_title\x18\x07 \x01(\x0b\x32\x11.api.pkg.web.Text\x12\x31\n\x15\x61ll_exps_display_data\x18\x08 \x01(\x0b\x32\x12.api.pkg.web.Table\x12/\n\x0c\x61ll_exps_btn\x18\t \x01(\x0b\x32\x19.api.pkg.web.ActionButton\x12.\n\x0bnew_exp_btn\x18\n \x01(\x0b\x32\x19.api.pkg.web.ActionButton"\xff\x01\n\x1aListExperimentsPageRequest\x12.\n\x06header\x18\x01 \x01(\x0b\x32\x1a.api.nebula.request.HeaderB\x02\x18\x01\x12-\n\x0cpage_context\x18\x02 \x01(\x0b\x32\x17.rpc.PageContextRequest\x12\x31\n\rsearch_filter\x18\x03 \x01(\x0b\x32\x16.api.pkg.web.TextInputB\x02\x18\x01\x12\x14\n\x0csearch_query\x18\x04 \x01(\t\x12\x12\n\nexp_status\x18\x05 \x01(\t\x12%\n\rjarvis_header\x18\x06 \x01(\x0b\x32\x0e.jarvis.Header"\xdb\x02\n\x1bListExperimentsPageResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12.\n\x0cpage_context\x18\x02 \x01(\x0b\x32\x18.rpc.PageContextResponse\x12(\n\rall_exp_title\x18\x03 \x01(\x0b\x32\x11.api.pkg.web.Text\x12\x35\n\x0c\x66ilter_input\x18\x04 \x01(\x0b\x32\x1f.api.pkg.web.MultiInputElements\x12+\n\x10user_manual_link\x18\x05 \x01(\x0b\x32\x11.api.pkg.web.Link\x12\x31\n\x15\x61ll_exps_display_data\x18\x06 \x01(\x0b\x32\x12.api.pkg.web.Table\x12.\n\x0bnew_exp_btn\x18\x07 \x01(\x0b\x32\x19.api.pkg.web.ActionButton"\xae\x01\n\x18NewExperimentPageRequest\x12.\n\x06header\x18\x01 \x01(\x0b\x32\x1a.api.nebula.request.HeaderB\x02\x18\x01\x12;\n\x0f\x65xperiment_form\x18\x02 \x01(\x0b\x32".api.quest.frontend.ExperimentForm\x12%\n\rjarvis_header\x18\x03 \x01(\x0b\x32\x0e.jarvis.Header"y\n\x19NewExperimentPageResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12?\n\x13new_experiment_form\x18\x02 \x01(\x0b\x32".api.quest.frontend.ExperimentForm"\xda\x01\n\x14ListVariablesRequest\x12.\n\x06header\x18\x01 \x01(\x0b\x32\x1a.api.nebula.request.HeaderB\x02\x18\x01\x12-\n\x0cpage_context\x18\x02 \x01(\x0b\x32\x17.rpc.PageContextRequest\x12-\n\rsearch_filter\x18\x03 \x01(\x0b\x32\x16.api.pkg.web.TextInput\x12\r\n\x05layer\x18\x04 \x01(\t\x12%\n\rjarvis_header\x18\x05 \x01(\x0b\x32\x0e.jarvis.Header"\x99\x01\n\x15ListVariablesResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12.\n\x0cpage_context\x18\x02 \x01(\x0b\x32\x18.rpc.PageContextResponse\x12\x33\n\rvariable_list\x18\x03 \x03(\x0b\x32\x1c.api.quest.frontend.Variable"\xe7\x01\n\x08Variable\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\r\n\x05layer\x18\x04 \x01(\t\x12\'\n\x0c\x64isplay_comp\x18\x05 \x01(\x0b\x32\x11.api.pkg.web.Text\x12,\n\x0c\x64\x65\x66\x61ult_comp\x18\x06 \x01(\x0b\x32\x16.api.pkg.web.TextInput\x12*\n\ninput_comp\x18\x07 \x01(\x0b\x32\x16.api.pkg.web.TextInput\x12\x1a\n\x12is_config_variable\x18\x08 \x01(\x08"\xb0\x01\n\x1a\x43reateNewExperimentRequest\x12.\n\x06header\x18\x01 \x01(\x0b\x32\x1a.api.nebula.request.HeaderB\x02\x18\x01\x12;\n\x0f\x65xperiment_form\x18\x02 \x01(\x0b\x32".api.quest.frontend.ExperimentForm\x12%\n\rjarvis_header\x18\x03 \x01(\x0b\x32\x0e.jarvis.Header"w\n\x1b\x43reateNewExperimentResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12;\n\x0f\x65xperiment_form\x18\x02 \x01(\x0b\x32".api.quest.frontend.ExperimentForm"\x9e\x01\n\x14GetExperimentRequest\x12.\n\x06header\x18\x01 \x01(\x0b\x32\x1a.api.nebula.request.HeaderB\x02\x18\x01\x12/\n\tentity_id\x18\x02 \x01(\x0b\x32\x1c.api.quest.frontend.EntityId\x12%\n\rjarvis_header\x18\x03 \x01(\x0b\x32\x0e.jarvis.Header"\x91\x01\n\x15GetExperimentResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x44\n\x14\x65xperiment_form_view\x18\x02 \x01(\x0b\x32&.api.quest.frontend.ExperimentFormView\x12\x15\n\rfetch_metrics\x18\x03 \x01(\x08"\xa5\x01\n\x1bGetExperimentMetricsRequest\x12.\n\x06header\x18\x01 \x01(\x0b\x32\x1a.api.nebula.request.HeaderB\x02\x18\x01\x12/\n\tentity_id\x18\x02 \x01(\x0b\x32\x1c.api.quest.frontend.EntityId\x12%\n\rjarvis_header\x18\x03 \x01(\x0b\x32\x0e.jarvis.Header"n\n\x1cGetExperimentMetricsResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x31\n\x14\x65vent_metrics_tables\x18\x02 \x01(\x0b\x32\x13.api.pkg.web.Tables"\xa3\x01\n\x19\x45\x64itExperimentPageRequest\x12.\n\x06header\x18\x01 \x01(\x0b\x32\x1a.api.nebula.request.HeaderB\x02\x18\x01\x12/\n\tentity_id\x18\x02 \x01(\x0b\x32\x1c.api.quest.frontend.EntityId\x12%\n\rjarvis_header\x18\x03 \x01(\x0b\x32\x0e.jarvis.Header"{\n\x1a\x45\x64itExperimentPageResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12@\n\x14\x65\x64it_experiment_form\x18\x02 \x01(\x0b\x32".api.quest.frontend.ExperimentForm"\xab\x01\n\x15\x45\x64itExperimentRequest\x12.\n\x06header\x18\x01 \x01(\x0b\x32\x1a.api.nebula.request.HeaderB\x02\x18\x01\x12;\n\x0f\x65xperiment_form\x18\x02 \x01(\x0b\x32".api.quest.frontend.ExperimentForm\x12%\n\rjarvis_header\x18\x03 \x01(\x0b\x32\x0e.jarvis.Header"r\n\x16\x45\x64itExperimentResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12;\n\x0f\x65xperiment_form\x18\x02 \x01(\x0b\x32".api.quest.frontend.ExperimentForm"\xb9\x01\n\x1dUpdateExperimentStatusRequest\x12.\n\x06header\x18\x01 \x01(\x0b\x32\x1a.api.nebula.request.HeaderB\x02\x18\x01\x12\x0e\n\x06\x65xp_id\x18\x02 \x01(\t\x12\x31\n\x06status\x18\x03 \x01(\x0e\x32!.api.quest.types.ExperimentStatus\x12%\n\rjarvis_header\x18\x04 \x01(\x0b\x32\x0e.jarvis.Header"=\n\x1eUpdateExperimentStatusResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status"\xcc\x01\n$UpdateExperimentVersionStatusRequest\x12.\n\x06header\x18\x01 \x01(\x0b\x32\x1a.api.nebula.request.HeaderB\x02\x18\x01\x12\x13\n\x0b\x65xp_vers_id\x18\x02 \x01(\t\x12\x38\n\x06status\x18\x03 \x01(\x0e\x32(.api.quest.types.ExperimentVersionStatus\x12%\n\rjarvis_header\x18\x04 \x01(\x0b\x32\x0e.jarvis.Header"D\n%UpdateExperimentVersionStatusResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status"\x9f\x01\n*GetNumberOfUsersInSegmentExpressionRequest\x12.\n\x06header\x18\x01 \x01(\x0b\x32\x1a.api.nebula.request.HeaderB\x02\x18\x01\x12\x1a\n\x12segment_expression\x18\x02 \x01(\t\x12%\n\rjarvis_header\x18\x03 \x01(\x0b\x32\x0e.jarvis.Header"c\n+GetNumberOfUsersInSegmentExpressionResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x17\n\x0fnumber_of_users\x18\x02 \x01(\x03"\xa4\x01\n%GetUserBucketProgressBarValuesRequest\x12.\n\x06header\x18\x01 \x01(\x0b\x32\x1a.api.nebula.request.HeaderB\x02\x18\x01\x12\x12\n\nlayer_name\x18\x02 \x01(\t\x12\x10\n\x08\x65xp_name\x18\x03 \x01(\t\x12%\n\rjarvis_header\x18\x04 \x01(\x0b\x32\x0e.jarvis.Header"\xdb\x01\n&GetUserBucketProgressBarValuesResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x11\n\tmin_value\x18\x02 \x01(\x03\x12\x11\n\tmax_value\x18\x03 \x01(\x03\x12\r\n\x05value\x18\x04 \x01(\x03\x12 \n\x05marks\x18\x05 \x03(\x0b\x32\x11.api.pkg.web.Mark\x12\x18\n\x10max_users_in_exp\x18\x06 \x01(\x03\x12#\n\x1bone_percent_number_of_users\x18\x07 \x01(\x03"\x87\x01\n\x1c\x43reateNewExperimentV2Request\x12%\n\rjarvis_header\x18\x01 \x01(\x0b\x32\x0e.jarvis.Header\x12@\n\x12\x65xperiment_form_v2\x18\x02 \x01(\x0b\x32$.api.quest.frontend.ExperimentFormV2"~\n\x1d\x43reateNewExperimentV2Response\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12@\n\x12\x65xperiment_form_v2\x18\x02 \x01(\x0b\x32$.api.quest.frontend.ExperimentFormV2"z\n GetEditExperimentPageDataRequest\x12%\n\rjarvis_header\x18\x03 \x01(\x0b\x32\x0e.jarvis.Header\x12/\n\tentity_id\x18\x02 \x01(\x0b\x32\x1c.api.quest.frontend.EntityId"\x87\x01\n!GetEditExperimentPageDataResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x45\n\x17\x65\x64it_experiment_form_v2\x18\x02 \x01(\x0b\x32$.api.quest.frontend.ExperimentFormV2"\x82\x01\n\x17\x45\x64itExperimentV2Request\x12%\n\rjarvis_header\x18\x01 \x01(\x0b\x32\x0e.jarvis.Header\x12@\n\x12\x65xperiment_form_v2\x18\x02 \x01(\x0b\x32$.api.quest.frontend.ExperimentFormV2"y\n\x18\x45\x64itExperimentV2Response\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12@\n\x12\x65xperiment_form_v2\x18\x02 \x01(\x0b\x32$.api.quest.frontend.ExperimentFormV2"\x85\x01\n\x1aSaveExperimentDraftRequest\x12%\n\rjarvis_header\x18\x01 \x01(\x0b\x32\x0e.jarvis.Header\x12@\n\x12\x65xperiment_form_v2\x18\x02 \x01(\x0b\x32$.api.quest.frontend.ExperimentFormV2"|\n\x1bSaveExperimentDraftResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12@\n\x12\x65xperiment_form_v2\x18\x02 \x01(\x0b\x32$.api.quest.frontend.ExperimentFormV2"\x7f\n%GetDatalayerForGivenExperimentRequest\x12%\n\rjarvis_header\x18\x01 \x01(\x0b\x32\x0e.jarvis.Header\x12/\n\tentity_id\x18\x02 \x01(\x0b\x32\x1c.api.quest.frontend.EntityId"\x8c\x01\n&GetDatalayerForGivenExperimentResponse\x12\x1b\n\x06status\x18\x01 \x01(\x0b\x32\x0b.rpc.Status\x12\x45\n\x17\x65\x64it_experiment_form_v2\x18\x02 \x01(\x0b\x32$.api.quest.frontend.ExperimentFormV22\xb8\x19\n\x08\x46rontend\x12\x89\x01\n\x08HomePage\x12#.api.quest.frontend.HomePageRequest\x1a$.api.quest.frontend.HomePageResponse"2\xca\xa7\xe8j\x0b\x65pifi:quest\xd2\xa7\xe8j\x04view\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04view\x12\xaa\x01\n\x13ListExperimentsPage\x12..api.quest.frontend.ListExperimentsPageRequest\x1a/.api.quest.frontend.ListExperimentsPageResponse"2\xca\xa7\xe8j\x0b\x65pifi:quest\xd2\xa7\xe8j\x04view\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04view\x12\xa4\x01\n\x11NewExperimentPage\x12,.api.quest.frontend.NewExperimentPageRequest\x1a-.api.quest.frontend.NewExperimentPageResponse"2\xca\xa7\xe8j\x0b\x65pifi:quest\xd2\xa7\xe8j\x04\x65\x64it\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04\x65\x64it\x12\x98\x01\n\rListVariables\x12(.api.quest.frontend.ListVariablesRequest\x1a).api.quest.frontend.ListVariablesResponse"2\xca\xa7\xe8j\x0b\x65pifi:quest\xd2\xa7\xe8j\x04view\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04view\x12\xaa\x01\n\x13\x43reateNewExperiment\x12..api.quest.frontend.CreateNewExperimentRequest\x1a/.api.quest.frontend.CreateNewExperimentResponse"2\xca\xa7\xe8j\x0b\x65pifi:quest\xd2\xa7\xe8j\x04\x65\x64it\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04\x65\x64it\x12\x98\x01\n\rGetExperiment\x12(.api.quest.frontend.GetExperimentRequest\x1a).api.quest.frontend.GetExperimentResponse"2\xca\xa7\xe8j\x0b\x65pifi:quest\xd2\xa7\xe8j\x04view\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04view\x12\xad\x01\n\x14GetExperimentMetrics\x12/.api.quest.frontend.GetExperimentMetricsRequest\x1a\x30.api.quest.frontend.GetExperimentMetricsResponse"2\xca\xa7\xe8j\x0b\x65pifi:quest\xd2\xa7\xe8j\x04view\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04view\x12\xa7\x01\n\x12\x45\x64itExperimentPage\x12-.api.quest.frontend.EditExperimentPageRequest\x1a..api.quest.frontend.EditExperimentPageResponse"2\xca\xa7\xe8j\x0b\x65pifi:quest\xd2\xa7\xe8j\x04\x65\x64it\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04\x65\x64it\x12\x9b\x01\n\x0e\x45\x64itExperiment\x12).api.quest.frontend.EditExperimentRequest\x1a*.api.quest.frontend.EditExperimentResponse"2\xca\xa7\xe8j\x0b\x65pifi:quest\xd2\xa7\xe8j\x04\x65\x64it\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04\x65\x64it\x12\xb3\x01\n\x16UpdateExperimentStatus\x12\x31.api.quest.frontend.UpdateExperimentStatusRequest\x1a\x32.api.quest.frontend.UpdateExperimentStatusResponse"2\xca\xa7\xe8j\x0b\x65pifi:quest\xd2\xa7\xe8j\x04\x65\x64it\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04\x65\x64it\x12\xce\x01\n\x1dUpdateExperimentVersionStatus\x12\x38.api.quest.frontend.UpdateExperimentVersionStatusRequest\x1a\x39.api.quest.frontend.UpdateExperimentVersionStatusResponse"8\xca\xa7\xe8j\x0b\x65pifi:quest\xd2\xa7\xe8j\x07\x61pprove\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x07\x61pprove\x12\xda\x01\n#GetNumberOfUsersInSegmentExpression\x12>.api.quest.frontend.GetNumberOfUsersInSegmentExpressionRequest\x1a?.api.quest.frontend.GetNumberOfUsersInSegmentExpressionResponse"2\xca\xa7\xe8j\x0b\x65pifi:quest\xd2\xa7\xe8j\x04\x65\x64it\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04\x65\x64it\x12\xcb\x01\n\x1eGetUserBucketProgressBarValues\x12\x39.api.quest.frontend.GetUserBucketProgressBarValuesRequest\x1a:.api.quest.frontend.GetUserBucketProgressBarValuesResponse"2\xca\xa7\xe8j\x0b\x65pifi:quest\xd2\xa7\xe8j\x04\x65\x64it\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04\x65\x64it\x12\xaa\x01\n\x13NewExperimentPageV2\x12..api.quest.frontend.NewExperimentPageV2Request\x1a/.api.quest.frontend.NewExperimentPageV2Response"2\xca\xa7\xe8j\x0b\x65pifi:quest\xd2\xa7\xe8j\x04\x65\x64it\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04\x65\x64it\x12\x97\x01\n\x15\x43reateNewExperimentV2\x12\x30.api.quest.frontend.CreateNewExperimentV2Request\x1a\x31.api.quest.frontend.CreateNewExperimentV2Response"\x19\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04\x65\x64it\x12\xa3\x01\n\x19GetEditExperimentPageData\x12\x34.api.quest.frontend.GetEditExperimentPageDataRequest\x1a\x35.api.quest.frontend.GetEditExperimentPageDataResponse"\x19\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04\x65\x64it\x12\x88\x01\n\x10\x45\x64itExperimentV2\x12+.api.quest.frontend.EditExperimentV2Request\x1a,.api.quest.frontend.EditExperimentV2Response"\x19\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04\x65\x64it\x12\x91\x01\n\x13SaveExperimentDraft\x12..api.quest.frontend.SaveExperimentDraftRequest\x1a/.api.quest.frontend.SaveExperimentDraftResponse"\x19\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04\x65\x64it\x12\xb2\x01\n\x1eGetDatalayerForGivenExperiment\x12\x39.api.quest.frontend.GetDatalayerForGivenExperimentRequest\x1a:.api.quest.frontend.GetDatalayerForGivenExperimentResponse"\x19\x8a\xe6\xe8j\x0b\x65pifi:quest\x92\xe6\xe8j\x04viewBV\n)com.github.epifi.gamma.api.quest/frontendZ)github.com/epifi/gamma/api/quest/frontendb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.quest.frontend.service_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n)com.github.epifi.gamma.api.quest/frontendZ)github.com/epifi/gamma/api/quest/frontend"
    )
    _NEWEXPERIMENTPAGEV2REQUEST.fields_by_name["header"]._options = None
    _NEWEXPERIMENTPAGEV2REQUEST.fields_by_name["header"]._serialized_options = b"\030\001"
    _HOMEPAGEREQUEST.fields_by_name["header"]._options = None
    _HOMEPAGEREQUEST.fields_by_name["header"]._serialized_options = b"\030\001"
    _LISTEXPERIMENTSPAGEREQUEST.fields_by_name["header"]._options = None
    _LISTEXPERIMENTSPAGEREQUEST.fields_by_name["header"]._serialized_options = b"\030\001"
    _LISTEXPERIMENTSPAGEREQUEST.fields_by_name["search_filter"]._options = None
    _LISTEXPERIMENTSPAGEREQUEST.fields_by_name["search_filter"]._serialized_options = b"\030\001"
    _NEWEXPERIMENTPAGEREQUEST.fields_by_name["header"]._options = None
    _NEWEXPERIMENTPAGEREQUEST.fields_by_name["header"]._serialized_options = b"\030\001"
    _LISTVARIABLESREQUEST.fields_by_name["header"]._options = None
    _LISTVARIABLESREQUEST.fields_by_name["header"]._serialized_options = b"\030\001"
    _CREATENEWEXPERIMENTREQUEST.fields_by_name["header"]._options = None
    _CREATENEWEXPERIMENTREQUEST.fields_by_name["header"]._serialized_options = b"\030\001"
    _GETEXPERIMENTREQUEST.fields_by_name["header"]._options = None
    _GETEXPERIMENTREQUEST.fields_by_name["header"]._serialized_options = b"\030\001"
    _GETEXPERIMENTMETRICSREQUEST.fields_by_name["header"]._options = None
    _GETEXPERIMENTMETRICSREQUEST.fields_by_name["header"]._serialized_options = b"\030\001"
    _EDITEXPERIMENTPAGEREQUEST.fields_by_name["header"]._options = None
    _EDITEXPERIMENTPAGEREQUEST.fields_by_name["header"]._serialized_options = b"\030\001"
    _EDITEXPERIMENTREQUEST.fields_by_name["header"]._options = None
    _EDITEXPERIMENTREQUEST.fields_by_name["header"]._serialized_options = b"\030\001"
    _UPDATEEXPERIMENTSTATUSREQUEST.fields_by_name["header"]._options = None
    _UPDATEEXPERIMENTSTATUSREQUEST.fields_by_name["header"]._serialized_options = b"\030\001"
    _UPDATEEXPERIMENTVERSIONSTATUSREQUEST.fields_by_name["header"]._options = None
    _UPDATEEXPERIMENTVERSIONSTATUSREQUEST.fields_by_name[
        "header"
    ]._serialized_options = b"\030\001"
    _GETNUMBEROFUSERSINSEGMENTEXPRESSIONREQUEST.fields_by_name["header"]._options = None
    _GETNUMBEROFUSERSINSEGMENTEXPRESSIONREQUEST.fields_by_name[
        "header"
    ]._serialized_options = b"\030\001"
    _GETUSERBUCKETPROGRESSBARVALUESREQUEST.fields_by_name["header"]._options = None
    _GETUSERBUCKETPROGRESSBARVALUESREQUEST.fields_by_name[
        "header"
    ]._serialized_options = b"\030\001"
    _FRONTEND.methods_by_name["HomePage"]._options = None
    _FRONTEND.methods_by_name[
        "HomePage"
    ]._serialized_options = b"\312\247\350j\013epifi:quest\322\247\350j\004view\212\346\350j\013epifi:quest\222\346\350j\004view"
    _FRONTEND.methods_by_name["ListExperimentsPage"]._options = None
    _FRONTEND.methods_by_name[
        "ListExperimentsPage"
    ]._serialized_options = b"\312\247\350j\013epifi:quest\322\247\350j\004view\212\346\350j\013epifi:quest\222\346\350j\004view"
    _FRONTEND.methods_by_name["NewExperimentPage"]._options = None
    _FRONTEND.methods_by_name[
        "NewExperimentPage"
    ]._serialized_options = b"\312\247\350j\013epifi:quest\322\247\350j\004edit\212\346\350j\013epifi:quest\222\346\350j\004edit"
    _FRONTEND.methods_by_name["ListVariables"]._options = None
    _FRONTEND.methods_by_name[
        "ListVariables"
    ]._serialized_options = b"\312\247\350j\013epifi:quest\322\247\350j\004view\212\346\350j\013epifi:quest\222\346\350j\004view"
    _FRONTEND.methods_by_name["CreateNewExperiment"]._options = None
    _FRONTEND.methods_by_name[
        "CreateNewExperiment"
    ]._serialized_options = b"\312\247\350j\013epifi:quest\322\247\350j\004edit\212\346\350j\013epifi:quest\222\346\350j\004edit"
    _FRONTEND.methods_by_name["GetExperiment"]._options = None
    _FRONTEND.methods_by_name[
        "GetExperiment"
    ]._serialized_options = b"\312\247\350j\013epifi:quest\322\247\350j\004view\212\346\350j\013epifi:quest\222\346\350j\004view"
    _FRONTEND.methods_by_name["GetExperimentMetrics"]._options = None
    _FRONTEND.methods_by_name[
        "GetExperimentMetrics"
    ]._serialized_options = b"\312\247\350j\013epifi:quest\322\247\350j\004view\212\346\350j\013epifi:quest\222\346\350j\004view"
    _FRONTEND.methods_by_name["EditExperimentPage"]._options = None
    _FRONTEND.methods_by_name[
        "EditExperimentPage"
    ]._serialized_options = b"\312\247\350j\013epifi:quest\322\247\350j\004edit\212\346\350j\013epifi:quest\222\346\350j\004edit"
    _FRONTEND.methods_by_name["EditExperiment"]._options = None
    _FRONTEND.methods_by_name[
        "EditExperiment"
    ]._serialized_options = b"\312\247\350j\013epifi:quest\322\247\350j\004edit\212\346\350j\013epifi:quest\222\346\350j\004edit"
    _FRONTEND.methods_by_name["UpdateExperimentStatus"]._options = None
    _FRONTEND.methods_by_name[
        "UpdateExperimentStatus"
    ]._serialized_options = b"\312\247\350j\013epifi:quest\322\247\350j\004edit\212\346\350j\013epifi:quest\222\346\350j\004edit"
    _FRONTEND.methods_by_name["UpdateExperimentVersionStatus"]._options = None
    _FRONTEND.methods_by_name[
        "UpdateExperimentVersionStatus"
    ]._serialized_options = b"\312\247\350j\013epifi:quest\322\247\350j\007approve\212\346\350j\013epifi:quest\222\346\350j\007approve"
    _FRONTEND.methods_by_name["GetNumberOfUsersInSegmentExpression"]._options = None
    _FRONTEND.methods_by_name[
        "GetNumberOfUsersInSegmentExpression"
    ]._serialized_options = b"\312\247\350j\013epifi:quest\322\247\350j\004edit\212\346\350j\013epifi:quest\222\346\350j\004edit"
    _FRONTEND.methods_by_name["GetUserBucketProgressBarValues"]._options = None
    _FRONTEND.methods_by_name[
        "GetUserBucketProgressBarValues"
    ]._serialized_options = b"\312\247\350j\013epifi:quest\322\247\350j\004edit\212\346\350j\013epifi:quest\222\346\350j\004edit"
    _FRONTEND.methods_by_name["NewExperimentPageV2"]._options = None
    _FRONTEND.methods_by_name[
        "NewExperimentPageV2"
    ]._serialized_options = b"\312\247\350j\013epifi:quest\322\247\350j\004edit\212\346\350j\013epifi:quest\222\346\350j\004edit"
    _FRONTEND.methods_by_name["CreateNewExperimentV2"]._options = None
    _FRONTEND.methods_by_name[
        "CreateNewExperimentV2"
    ]._serialized_options = b"\212\346\350j\013epifi:quest\222\346\350j\004edit"
    _FRONTEND.methods_by_name["GetEditExperimentPageData"]._options = None
    _FRONTEND.methods_by_name[
        "GetEditExperimentPageData"
    ]._serialized_options = b"\212\346\350j\013epifi:quest\222\346\350j\004edit"
    _FRONTEND.methods_by_name["EditExperimentV2"]._options = None
    _FRONTEND.methods_by_name[
        "EditExperimentV2"
    ]._serialized_options = b"\212\346\350j\013epifi:quest\222\346\350j\004edit"
    _FRONTEND.methods_by_name["SaveExperimentDraft"]._options = None
    _FRONTEND.methods_by_name[
        "SaveExperimentDraft"
    ]._serialized_options = b"\212\346\350j\013epifi:quest\222\346\350j\004edit"
    _FRONTEND.methods_by_name["GetDatalayerForGivenExperiment"]._options = None
    _FRONTEND.methods_by_name[
        "GetDatalayerForGivenExperiment"
    ]._serialized_options = b"\212\346\350j\013epifi:quest\222\346\350j\004view"
    _globals["_NEWEXPERIMENTPAGEV2REQUEST"]._serialized_start = 411
    _globals["_NEWEXPERIMENTPAGEV2REQUEST"]._serialized_end = 589
    _globals["_NEWEXPERIMENTPAGEV2RESPONSE"]._serialized_start = 591
    _globals["_NEWEXPERIMENTPAGEV2RESPONSE"]._serialized_end = 712
    _globals["_HOMEPAGEREQUEST"]._serialized_start = 714
    _globals["_HOMEPAGEREQUEST"]._serialized_end = 837
    _globals["_HOMEPAGERESPONSE"]._serialized_start = 840
    _globals["_HOMEPAGERESPONSE"]._serialized_end = 1305
    _globals["_LISTEXPERIMENTSPAGEREQUEST"]._serialized_start = 1308
    _globals["_LISTEXPERIMENTSPAGEREQUEST"]._serialized_end = 1563
    _globals["_LISTEXPERIMENTSPAGERESPONSE"]._serialized_start = 1566
    _globals["_LISTEXPERIMENTSPAGERESPONSE"]._serialized_end = 1913
    _globals["_NEWEXPERIMENTPAGEREQUEST"]._serialized_start = 1916
    _globals["_NEWEXPERIMENTPAGEREQUEST"]._serialized_end = 2090
    _globals["_NEWEXPERIMENTPAGERESPONSE"]._serialized_start = 2092
    _globals["_NEWEXPERIMENTPAGERESPONSE"]._serialized_end = 2213
    _globals["_LISTVARIABLESREQUEST"]._serialized_start = 2216
    _globals["_LISTVARIABLESREQUEST"]._serialized_end = 2434
    _globals["_LISTVARIABLESRESPONSE"]._serialized_start = 2437
    _globals["_LISTVARIABLESRESPONSE"]._serialized_end = 2590
    _globals["_VARIABLE"]._serialized_start = 2593
    _globals["_VARIABLE"]._serialized_end = 2824
    _globals["_CREATENEWEXPERIMENTREQUEST"]._serialized_start = 2827
    _globals["_CREATENEWEXPERIMENTREQUEST"]._serialized_end = 3003
    _globals["_CREATENEWEXPERIMENTRESPONSE"]._serialized_start = 3005
    _globals["_CREATENEWEXPERIMENTRESPONSE"]._serialized_end = 3124
    _globals["_GETEXPERIMENTREQUEST"]._serialized_start = 3127
    _globals["_GETEXPERIMENTREQUEST"]._serialized_end = 3285
    _globals["_GETEXPERIMENTRESPONSE"]._serialized_start = 3288
    _globals["_GETEXPERIMENTRESPONSE"]._serialized_end = 3433
    _globals["_GETEXPERIMENTMETRICSREQUEST"]._serialized_start = 3436
    _globals["_GETEXPERIMENTMETRICSREQUEST"]._serialized_end = 3601
    _globals["_GETEXPERIMENTMETRICSRESPONSE"]._serialized_start = 3603
    _globals["_GETEXPERIMENTMETRICSRESPONSE"]._serialized_end = 3713
    _globals["_EDITEXPERIMENTPAGEREQUEST"]._serialized_start = 3716
    _globals["_EDITEXPERIMENTPAGEREQUEST"]._serialized_end = 3879
    _globals["_EDITEXPERIMENTPAGERESPONSE"]._serialized_start = 3881
    _globals["_EDITEXPERIMENTPAGERESPONSE"]._serialized_end = 4004
    _globals["_EDITEXPERIMENTREQUEST"]._serialized_start = 4007
    _globals["_EDITEXPERIMENTREQUEST"]._serialized_end = 4178
    _globals["_EDITEXPERIMENTRESPONSE"]._serialized_start = 4180
    _globals["_EDITEXPERIMENTRESPONSE"]._serialized_end = 4294
    _globals["_UPDATEEXPERIMENTSTATUSREQUEST"]._serialized_start = 4297
    _globals["_UPDATEEXPERIMENTSTATUSREQUEST"]._serialized_end = 4482
    _globals["_UPDATEEXPERIMENTSTATUSRESPONSE"]._serialized_start = 4484
    _globals["_UPDATEEXPERIMENTSTATUSRESPONSE"]._serialized_end = 4545
    _globals["_UPDATEEXPERIMENTVERSIONSTATUSREQUEST"]._serialized_start = 4548
    _globals["_UPDATEEXPERIMENTVERSIONSTATUSREQUEST"]._serialized_end = 4752
    _globals["_UPDATEEXPERIMENTVERSIONSTATUSRESPONSE"]._serialized_start = 4754
    _globals["_UPDATEEXPERIMENTVERSIONSTATUSRESPONSE"]._serialized_end = 4822
    _globals["_GETNUMBEROFUSERSINSEGMENTEXPRESSIONREQUEST"]._serialized_start = 4825
    _globals["_GETNUMBEROFUSERSINSEGMENTEXPRESSIONREQUEST"]._serialized_end = 4984
    _globals["_GETNUMBEROFUSERSINSEGMENTEXPRESSIONRESPONSE"]._serialized_start = 4986
    _globals["_GETNUMBEROFUSERSINSEGMENTEXPRESSIONRESPONSE"]._serialized_end = 5085
    _globals["_GETUSERBUCKETPROGRESSBARVALUESREQUEST"]._serialized_start = 5088
    _globals["_GETUSERBUCKETPROGRESSBARVALUESREQUEST"]._serialized_end = 5252
    _globals["_GETUSERBUCKETPROGRESSBARVALUESRESPONSE"]._serialized_start = 5255
    _globals["_GETUSERBUCKETPROGRESSBARVALUESRESPONSE"]._serialized_end = 5474
    _globals["_CREATENEWEXPERIMENTV2REQUEST"]._serialized_start = 5477
    _globals["_CREATENEWEXPERIMENTV2REQUEST"]._serialized_end = 5612
    _globals["_CREATENEWEXPERIMENTV2RESPONSE"]._serialized_start = 5614
    _globals["_CREATENEWEXPERIMENTV2RESPONSE"]._serialized_end = 5740
    _globals["_GETEDITEXPERIMENTPAGEDATAREQUEST"]._serialized_start = 5742
    _globals["_GETEDITEXPERIMENTPAGEDATAREQUEST"]._serialized_end = 5864
    _globals["_GETEDITEXPERIMENTPAGEDATARESPONSE"]._serialized_start = 5867
    _globals["_GETEDITEXPERIMENTPAGEDATARESPONSE"]._serialized_end = 6002
    _globals["_EDITEXPERIMENTV2REQUEST"]._serialized_start = 6005
    _globals["_EDITEXPERIMENTV2REQUEST"]._serialized_end = 6135
    _globals["_EDITEXPERIMENTV2RESPONSE"]._serialized_start = 6137
    _globals["_EDITEXPERIMENTV2RESPONSE"]._serialized_end = 6258
    _globals["_SAVEEXPERIMENTDRAFTREQUEST"]._serialized_start = 6261
    _globals["_SAVEEXPERIMENTDRAFTREQUEST"]._serialized_end = 6394
    _globals["_SAVEEXPERIMENTDRAFTRESPONSE"]._serialized_start = 6396
    _globals["_SAVEEXPERIMENTDRAFTRESPONSE"]._serialized_end = 6520
    _globals["_GETDATALAYERFORGIVENEXPERIMENTREQUEST"]._serialized_start = 6522
    _globals["_GETDATALAYERFORGIVENEXPERIMENTREQUEST"]._serialized_end = 6649
    _globals["_GETDATALAYERFORGIVENEXPERIMENTRESPONSE"]._serialized_start = 6652
    _globals["_GETDATALAYERFORGIVENEXPERIMENTRESPONSE"]._serialized_end = 6792
    _globals["_FRONTEND"]._serialized_start = 6795
    _globals["_FRONTEND"]._serialized_end = 10051
# @@protoc_insertion_point(module_scope)
