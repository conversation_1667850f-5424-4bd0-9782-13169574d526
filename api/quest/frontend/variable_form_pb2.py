# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/quest/frontend/variable_form.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from api.pkg.web import values_pb2 as api_dot_pkg_dot_web_dot_values__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n&api/quest/frontend/variable_form.proto\x12\x12\x61pi.quest.frontend\x1a\x18\x61pi/pkg/web/values.proto\"\x8a\x01\n\x0cVariableForm\x12\x13\n\x0bvariable_id\x18\x01 \x01(\t\x12\x15\n\rvariable_path\x18\x02 \x01(\t\x12'\n\x08\x64\x61tatype\x18\x03 \x01(\x0b\x32\x15.api.pkg.web.Datatype\x12%\n\x05value\x18\x04 \x01(\x0b\x32\x16.api.pkg.web.DataValueBV\n)com.github.epifi.gamma.api.quest/frontendZ)github.com/epifi/gamma/api/quest/frontendb\x06proto3"
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "api.quest.frontend.variable_form_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n)com.github.epifi.gamma.api.quest/frontendZ)github.com/epifi/gamma/api/quest/frontend"
    )
    _globals["_VARIABLEFORM"]._serialized_start = 89
    _globals["_VARIABLEFORM"]._serialized_end = 227
# @@protoc_insertion_point(module_scope)
