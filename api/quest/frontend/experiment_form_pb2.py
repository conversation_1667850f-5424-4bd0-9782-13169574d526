# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/quest/frontend/experiment_form.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

from api.pkg.web import components_pb2 as api_dot_pkg_dot_web_dot_components__pb2
from api.pkg.web import values_pb2 as api_dot_pkg_dot_web_dot_values__pb2
from api.quest.frontend import (
    variant_form_pb2 as api_dot_quest_dot_frontend_dot_variant__form__pb2,
)

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n(api/quest/frontend/experiment_form.proto\x12\x12\x61pi.quest.frontend\x1a\x1c\x61pi/pkg/web/components.proto\x1a\x18\x61pi/pkg/web/values.proto\x1a%api/quest/frontend/variant_form.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1fgoogle/protobuf/timestamp.proto"\xe5\x03\n\x0e\x45xperimentForm\x12 \n\x05title\x18\x01 \x01(\x0b\x32\x11.api.pkg.web.Text\x12+\n\x10user_manual_link\x18\x02 \x01(\x0b\x32\x11.api.pkg.web.Link\x12+\n\x10\x65xp_details_form\x18\x03 \x01(\x0b\x32\x11.api.pkg.web.Form\x12/\n\x14metric_segment_title\x18\x04 \x01(\x0b\x32\x11.api.pkg.web.Text\x12.\n\x13metric_segment_form\x18\x05 \x01(\x0b\x32\x11.api.pkg.web.Form\x12\x37\n\rvariant_input\x18\x06 \x01(\x0b\x32 .api.quest.frontend.VariantInput\x12\x32\n\x16variant_variable_table\x18\x07 \x01(\x0b\x32\x12.api.pkg.web.Table\x12)\n\x0e\x61pprover_email\x18\x08 \x01(\x0b\x32\x11.api.pkg.web.Form\x12-\n\nsubmit_btn\x18\t \x01(\x0b\x32\x19.api.pkg.web.ActionButton\x12/\n\tentity_id\x18\n \x01(\x0b\x32\x1c.api.quest.frontend.EntityId"\xa2\x03\n\x12\x45xperimentFormView\x12/\n\tentity_id\x18\x01 \x01(\x0b\x32\x1c.api.quest.frontend.EntityId\x12;\n\x1c\x65xperiment_details_form_view\x18\x02 \x01(\x0b\x32\x15.api.pkg.web.FormView\x12)\n\rvariant_table\x18\x03 \x01(\x0b\x32\x12.api.pkg.web.Table\x12\x32\n\x16variable_variant_table\x18\x04 \x01(\x0b\x32\x12.api.pkg.web.Table\x12\x31\n\x14\x65vent_metrics_tables\x18\x05 \x01(\x0b\x32\x13.api.pkg.web.Tables\x12$\n\x08versions\x18\x06 \x01(\x0b\x32\x12.api.pkg.web.Table\x12\x39\n\x16json_difference_viewer\x18\x07 \x01(\x0b\x32\x19.api.pkg.web.JsonDiffView\x12+\n\x07\x62uttons\x18\x08 \x01(\x0b\x32\x1a.api.pkg.web.ActionButtons"T\n\x08\x45ntityId\x12\x15\n\x0b\x65xp_vers_id\x18\x01 \x01(\tH\x00\x12\x10\n\x06\x65xp_id\x18\x02 \x01(\tH\x00\x12\x12\n\x08\x65xp_name\x18\x03 \x01(\tH\x00\x42\x0b\n\tentity_id"\xc9\x02\n\x10\x45xperimentFormV2\x12/\n\tentity_id\x18\x01 \x01(\x0b\x32\x1c.api.quest.frontend.EntityId\x12\r\n\x05title\x18\x02 \x01(\t\x12\x12\n\npage_infos\x18\x03 \x03(\t\x12/\n\rpage_elements\x18\x04 \x03(\x0b\x32\x18.api.pkg.web.PageElement\x12T\n\x11\x64\x61ta_layer_values\x18\x05 \x03(\x0b\x32\x39.api.quest.frontend.ExperimentFormV2.DataLayerValuesEntry\x1aZ\n\x14\x44\x61taLayerValuesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x31\n\x05value\x18\x02 \x01(\x0b\x32".api.quest.frontend.DatalayerValue:\x02\x38\x01"\xd3\x02\n\x0e\x44\x61talayerValue\x12\x34\n\tdata_type\x18\x01 \x01(\x0e\x32!.api.quest.frontend.DatalayerType\x12\x14\n\nbool_value\x18\x02 \x01(\x08H\x00\x12\x13\n\tint_value\x18\x03 \x01(\x03H\x00\x12\x15\n\x0b\x66loat_value\x18\x04 \x01(\x01H\x00\x12\x16\n\x0cstring_value\x18\x05 \x01(\tH\x00\x12\x30\n\ntime_value\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x00\x12\x33\n\x0e\x64uration_value\x18\x07 \x01(\x0b\x32\x19.google.protobuf.DurationH\x00\x12<\n\rvariant_value\x18\x08 \x01(\x0b\x32#.api.quest.frontend.VariantDataTypeH\x00\x42\x0c\n\ndata_value"T\n\x0fVariantDataType\x12\x41\n\x13variant_input_forms\x18\x01 \x03(\x0b\x32$.api.quest.frontend.VariantInputForm"\xb5\x01\n\x10VariantInputForm\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x18\n\x10roll_out_percent\x18\x03 \x01(\x05\x12\x12\n\nuser_group\x18\x04 \x01(\t\x12\x14\n\x0cphone_number\x18\x05 \x01(\t\x12:\n\x0fvariable_inputs\x18\x06 \x03(\x0b\x32!.api.quest.frontend.VariableInput"Y\n\rVariableInput\x12\x15\n\rvariable_path\x18\x01 \x01(\t\x12\x31\n\ndata_value\x18\x02 \x01(\x0b\x32\x1d.api.quest.frontend.DataValue"\\\n\tDataValue\x12(\n\tdata_type\x18\x01 \x01(\x0b\x32\x15.api.pkg.web.Datatype\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.api.pkg.web.DataValue*\xe7\x01\n\rDatalayerType\x12\x1e\n\x1a\x44\x41TALAYER_TYPE_UNSPECIFIED\x10\x00\x12\x17\n\x13\x44\x41TALAYER_TYPE_BOOL\x10\x01\x12\x16\n\x12\x44\x41TALAYER_TYPE_INT\x10\x02\x12\x18\n\x14\x44\x41TALAYER_TYPE_FLOAT\x10\x03\x12\x19\n\x15\x44\x41TALAYER_TYPE_STRING\x10\x04\x12\x17\n\x13\x44\x41TALAYER_TYPE_TIME\x10\x05\x12\x1b\n\x17\x44\x41TALAYER_TYPE_DURATION\x10\x06\x12\x1a\n\x16\x44\x41TALAYER_TYPE_VARIANT\x10\x07\x42V\n)com.github.epifi.gamma.api.quest/frontendZ)github.com/epifi/gamma/api/quest/frontendb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "api.quest.frontend.experiment_form_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n)com.github.epifi.gamma.api.quest/frontendZ)github.com/epifi/gamma/api/quest/frontend"
    )
    _EXPERIMENTFORMV2_DATALAYERVALUESENTRY._options = None
    _EXPERIMENTFORMV2_DATALAYERVALUESENTRY._serialized_options = b"8\001"
    _globals["_DATALAYERTYPE"]._serialized_start = 2349
    _globals["_DATALAYERTYPE"]._serialized_end = 2580
    _globals["_EXPERIMENTFORM"]._serialized_start = 225
    _globals["_EXPERIMENTFORM"]._serialized_end = 710
    _globals["_EXPERIMENTFORMVIEW"]._serialized_start = 713
    _globals["_EXPERIMENTFORMVIEW"]._serialized_end = 1131
    _globals["_ENTITYID"]._serialized_start = 1133
    _globals["_ENTITYID"]._serialized_end = 1217
    _globals["_EXPERIMENTFORMV2"]._serialized_start = 1220
    _globals["_EXPERIMENTFORMV2"]._serialized_end = 1549
    _globals["_EXPERIMENTFORMV2_DATALAYERVALUESENTRY"]._serialized_start = 1459
    _globals["_EXPERIMENTFORMV2_DATALAYERVALUESENTRY"]._serialized_end = 1549
    _globals["_DATALAYERVALUE"]._serialized_start = 1552
    _globals["_DATALAYERVALUE"]._serialized_end = 1891
    _globals["_VARIANTDATATYPE"]._serialized_start = 1893
    _globals["_VARIANTDATATYPE"]._serialized_end = 1977
    _globals["_VARIANTINPUTFORM"]._serialized_start = 1980
    _globals["_VARIANTINPUTFORM"]._serialized_end = 2161
    _globals["_VARIABLEINPUT"]._serialized_start = 2163
    _globals["_VARIABLEINPUT"]._serialized_end = 2252
    _globals["_DATAVALUE"]._serialized_start = 2254
    _globals["_DATAVALUE"]._serialized_end = 2346
# @@protoc_insertion_point(module_scope)
