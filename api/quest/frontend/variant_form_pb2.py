# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/quest/frontend/variant_form.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from api.pkg.web import components_pb2 as api_dot_pkg_dot_web_dot_components__pb2
from api.pkg.web import enums_pb2 as api_dot_pkg_dot_web_dot_enums__pb2
from api.quest.frontend import (
    variable_form_pb2 as api_dot_quest_dot_frontend_dot_variable__form__pb2,
)
from api.quest.types import variant_pb2 as api_dot_quest_dot_types_dot_variant__pb2
from validate import validate_pb2 as validate_dot_validate__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n%api/quest/frontend/variant_form.proto\x12\x12\x61pi.quest.frontend\x1a\x1c\x61pi/pkg/web/components.proto\x1a\x17\x61pi/pkg/web/enums.proto\x1a&api/quest/frontend/variable_form.proto\x1a\x1d\x61pi/quest/types/variant.proto\x1a\x17validate/validate.proto"\xd4\x01\n\x0bVariantForm\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x17\n\x0frollout_percent\x18\x02 \x01(\x02\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x1a\n\x12is_control_variant\x18\x04 \x01(\x08\x12\x38\n\x0foverride_policy\x18\x05 \x01(\x0b\x32\x1f.api.quest.types.OverridePolicy\x12\x33\n\tvariables\x18\x06 \x03(\x0b\x32 .api.quest.frontend.VariableForm"\xe6\x02\n\x0cVariantInput\x12\x33\n\tcomp_type\x18\x06 \x01(\x0e\x32\x16.api.pkg.web.ComponentB\x08\xfa\x42\x05\x82\x01\x02\x08\x14\x12#\n\x08\x65xp_name\x18\x01 \x01(\x0b\x32\x11.api.pkg.web.Text\x12\x41\n\x14variant_input_schema\x18\x02 \x01(\x0b\x32#.api.quest.frontend.VariantInputRow\x12\x32\n\x0fnew_variant_btn\x18\x03 \x01(\x0b\x32\x19.api.pkg.web.ActionButton\x12;\n\x0evariant_inputs\x18\x04 \x03(\x0b\x32#.api.quest.frontend.VariantInputRow\x12H\n\x1b\x63ontrol_group_variant_input\x18\x05 \x01(\x0b\x32#.api.quest.frontend.VariantInputRow"\x85\x03\n\x0fVariantInputRow\x12\x33\n\tcomp_type\x18\x0b \x01(\x0e\x32\x16.api.pkg.web.ComponentB\x08\xfa\x42\x05\x82\x01\x02\x08\x15\x12)\n\rvariant_label\x18\x01 \x01(\x0b\x32\x12.api.pkg.web.Label\x12\x32\n\x12variant_name_input\x18\x03 \x01(\x0b\x32\x16.api.pkg.web.TextInput\x12\x35\n\x15rollout_percent_input\x18\x05 \x01(\x0b\x32\x16.api.pkg.web.TextInput\x12\x31\n\x11\x64\x65scription_input\x18\x07 \x01(\x0b\x32\x16.api.pkg.web.TextInput\x12=\n\x17override_input_elements\x18\t \x01(\x0b\x32\x1c.api.pkg.web.OrInputElements\x12\x35\n\x12remove_variant_btn\x18\n \x01(\x0b\x32\x19.api.pkg.web.ActionButtonBV\n)com.github.epifi.gamma.api.quest/frontendZ)github.com/epifi/gamma/api/quest/frontendb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "api.quest.frontend.variant_form_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n)com.github.epifi.gamma.api.quest/frontendZ)github.com/epifi/gamma/api/quest/frontend"
    )
    _VARIANTINPUT.fields_by_name["comp_type"]._options = None
    _VARIANTINPUT.fields_by_name[
        "comp_type"
    ]._serialized_options = b"\372B\005\202\001\002\010\024"
    _VARIANTINPUTROW.fields_by_name["comp_type"]._options = None
    _VARIANTINPUTROW.fields_by_name[
        "comp_type"
    ]._serialized_options = b"\372B\005\202\001\002\010\025"
    _globals["_VARIANTFORM"]._serialized_start = 213
    _globals["_VARIANTFORM"]._serialized_end = 425
    _globals["_VARIANTINPUT"]._serialized_start = 428
    _globals["_VARIANTINPUT"]._serialized_end = 786
    _globals["_VARIANTINPUTROW"]._serialized_start = 789
    _globals["_VARIANTINPUTROW"]._serialized_end = 1178
# @@protoc_insertion_point(module_scope)
