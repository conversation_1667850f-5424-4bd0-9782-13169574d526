// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/usstocks/tax/stock_entity_details.proto

package tax

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on StockEntityDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StockEntityDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StockEntityDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StockEntityDetailsMultiError, or nil if none found.
func (m *StockEntityDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *StockEntityDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Symbol

	for idx, item := range m.GetDailyClosingPrices() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockEntityDetailsValidationError{
						field:  fmt.Sprintf("DailyClosingPrices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockEntityDetailsValidationError{
						field:  fmt.Sprintf("DailyClosingPrices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockEntityDetailsValidationError{
					field:  fmt.Sprintf("DailyClosingPrices[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for EntityAddress

	if len(errors) > 0 {
		return StockEntityDetailsMultiError(errors)
	}

	return nil
}

// StockEntityDetailsMultiError is an error wrapping multiple validation errors
// returned by StockEntityDetails.ValidateAll() if the designated constraints
// aren't met.
type StockEntityDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StockEntityDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StockEntityDetailsMultiError) AllErrors() []error { return m }

// StockEntityDetailsValidationError is the validation error returned by
// StockEntityDetails.Validate if the designated constraints aren't met.
type StockEntityDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StockEntityDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StockEntityDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StockEntityDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StockEntityDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StockEntityDetailsValidationError) ErrorName() string {
	return "StockEntityDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e StockEntityDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStockEntityDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StockEntityDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StockEntityDetailsValidationError{}

// Validate checks the field values on DailyClosingPrice with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DailyClosingPrice) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DailyClosingPrice with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DailyClosingPriceMultiError, or nil if none found.
func (m *DailyClosingPrice) ValidateAll() error {
	return m.validate(true)
}

func (m *DailyClosingPrice) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetClosingPriceTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DailyClosingPriceValidationError{
					field:  "ClosingPriceTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DailyClosingPriceValidationError{
					field:  "ClosingPriceTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClosingPriceTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DailyClosingPriceValidationError{
				field:  "ClosingPriceTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClosingPrice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DailyClosingPriceValidationError{
					field:  "ClosingPrice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DailyClosingPriceValidationError{
					field:  "ClosingPrice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClosingPrice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DailyClosingPriceValidationError{
				field:  "ClosingPrice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DailyClosingPriceMultiError(errors)
	}

	return nil
}

// DailyClosingPriceMultiError is an error wrapping multiple validation errors
// returned by DailyClosingPrice.ValidateAll() if the designated constraints
// aren't met.
type DailyClosingPriceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DailyClosingPriceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DailyClosingPriceMultiError) AllErrors() []error { return m }

// DailyClosingPriceValidationError is the validation error returned by
// DailyClosingPrice.Validate if the designated constraints aren't met.
type DailyClosingPriceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DailyClosingPriceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DailyClosingPriceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DailyClosingPriceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DailyClosingPriceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DailyClosingPriceValidationError) ErrorName() string {
	return "DailyClosingPriceValidationError"
}

// Error satisfies the builtin error interface
func (e DailyClosingPriceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDailyClosingPrice.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DailyClosingPriceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DailyClosingPriceValidationError{}
