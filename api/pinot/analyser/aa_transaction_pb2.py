# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/pinot/analyser/aa_transaction.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from api.accounts import account_type_pb2 as api_dot_accounts_dot_account__type__pb2
from api.categorizer import enums_pb2 as api_dot_categorizer_dot_enums__pb2
from api.order.payment import (
    accounting_entry_type_pb2 as api_dot_order_dot_payment_dot_accounting__entry__type__pb2,
)
from api.order.payment import (
    payment_protocol_pb2 as api_dot_order_dot_payment_dot_payment__protocol__pb2,
)
from api.payment_instruments import (
    payment_instrument_pb2 as api_dot_payment__instruments_dot_payment__instrument__pb2,
)
from api.types import actor_pb2 as api_dot_types_dot_actor__pb2
from api.types import bank_pb2 as api_dot_types_dot_bank__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\'api/pinot/analyser/aa_transaction.proto\x12\x08\x61nalyser\x1a\x1f\x61pi/accounts/account_type.proto\x1a-api/order/payment/accounting_entry_type.proto\x1a(api/order/payment/payment_protocol.proto\x1a\x30\x61pi/payment_instruments/payment_instrument.proto\x1a\x1b\x61pi/categorizer/enums.proto\x1a\x15\x61pi/types/actor.proto\x1a\x14\x61pi/types/bank.proto"\xbe\t\n\rAATransaction\x12&\n\x0etransaction_id\x18\x01 \x01(\tR\x0etransaction_id\x12\x1a\n\x08\x61\x63tor_id\x18\x02 \x01(\tR\x08\x61\x63tor_id\x12\x0e\n\x02pi\x18\x03 \x01(\tR\x02pi\x12\x42\n\x07pi_type\x18\x04 \x01(\x0e\x32(.paymentinstrument.PaymentInstrumentTypeR\x07pi_type\x12\x1f\n\x04\x62\x61nk\x18\x05 \x01(\x0e\x32\x0b.types.BankR\x04\x62\x61nk\x12\x1e\n\naccount_id\x18\x06 \x01(\tR\naccount_id\x12\x32\n\x0c\x61\x63\x63ount_type\x18\x07 \x01(\x0e\x32\x0e.accounts.TypeR\x0c\x61\x63\x63ount_type\x12$\n\rwith_actor_id\x18\x08 \x01(\tR\rwith_actor_id\x12\x18\n\x07with_pi\x18\t \x01(\tR\x07with_pi\x12L\n\x0cwith_pi_type\x18\n \x01(\x0e\x32(.paymentinstrument.PaymentInstrumentTypeR\x0cwith_pi_type\x12.\n\x12with_merchant_name\x18\x0b \x01(\tR\x12with_merchant_name\x12*\n\x10with_merchant_id\x18\x0c \x01(\tR\x10with_merchant_id\x12\x16\n\x06\x61mount\x18\r \x01(\x03R\x06\x61mount\x12J\n\x10payment_protocol\x18\x0e \x01(\x0e\x32\x1e.order.payment.PaymentProtocolR\x10payment_protocol\x12N\n\x10transaction_type\x18\x0f \x01(\x0e\x32".order.payment.AccountingEntryTypeR\x10transaction_type\x12"\n\x0contology_ids\x18\x10 \x03(\tR\x0contology_ids\x12L\n\x12\x64isplay_categories\x18\x11 \x03(\x0e\x32\x1c.categorizer.DisplayCategoryR\x12\x64isplay_categories\x12\x1c\n\taa_txn_id\x18\x12 \x01(\tR\taa_txn_id\x12*\n\x10\x65xecuted_at_unix\x18\x13 \x01(\x03R\x10\x65xecuted_at_unix\x12<\n\x19\x65xecuted_at_date_unix_ist\x18\x14 \x01(\x03R\x19\x65xecuted_at_date_unix_ist\x12(\n\x0f\x63reated_at_unix\x18\x15 \x01(\x03R\x0f\x63reated_at_unix\x12\x32\n\x14view_updated_at_unix\x18\x16 \x01(\x03R\x14view_updated_at_unix\x12\x35\n\rl0_ontologies\x18\x17 \x03(\x0e\x32\x0f.categorizer.L0R\rl0_ontologies\x12;\n\x0fwith_actor_type\x18\x19 \x01(\x0e\x32\x11.types.Actor.TypeR\x0fwith_actor_type\x12\x36\n\x16with_derived_entity_id\x18\x1b \x01(\tR\x16with_derived_entity_idBV\n)com.github.epifi.gamma.api.pinot.analyserZ)github.com/epifi/gamma/api/pinot/analyserb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "api.pinot.analyser.aa_transaction_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n)com.github.epifi.gamma.api.pinot.analyserZ)github.com/epifi/gamma/api/pinot/analyser"
    )
    _globals["_AATRANSACTION"]._serialized_start = 300
    _globals["_AATRANSACTION"]._serialized_end = 1514
# @@protoc_insertion_point(module_scope)
