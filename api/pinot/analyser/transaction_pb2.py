# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/pinot/analyser/transaction.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from api.accounts import account_type_pb2 as api_dot_accounts_dot_account__type__pb2
from api.categorizer import enums_pb2 as api_dot_categorizer_dot_enums__pb2
from api.order import order_pb2 as api_dot_order_dot_order__pb2
from api.order import workflow_pb2 as api_dot_order_dot_workflow__pb2
from api.order.payment import (
    accounting_entry_type_pb2 as api_dot_order_dot_payment_dot_accounting__entry__type__pb2,
)
from api.order.payment import (
    payment_protocol_pb2 as api_dot_order_dot_payment_dot_payment__protocol__pb2,
)
from api.order.payment import transaction_pb2 as api_dot_order_dot_payment_dot_transaction__pb2
from api.payment_instruments import (
    payment_instrument_pb2 as api_dot_payment__instruments_dot_payment__instrument__pb2,
)
from api.tiering.external import external_pb2 as api_dot_tiering_dot_external_dot_external__pb2
from api.types import actor_pb2 as api_dot_types_dot_actor__pb2
from api.vendorgateway import vendor_pb2 as api_dot_vendorgateway_dot_vendor__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n$api/pinot/analyser/transaction.proto\x12\x08\x61nalyser\x1a\x1f\x61pi/accounts/account_type.proto\x1a\x1b\x61pi/categorizer/enums.proto\x1a\x15\x61pi/order/order.proto\x1a-api/order/payment/accounting_entry_type.proto\x1a(api/order/payment/payment_protocol.proto\x1a#api/order/payment/transaction.proto\x1a\x18\x61pi/order/workflow.proto\x1a\x30\x61pi/payment_instruments/payment_instrument.proto\x1a#api/tiering/external/external.proto\x1a\x15\x61pi/types/actor.proto\x1a\x1e\x61pi/vendorgateway/vendor.proto"\xef\x13\n\x0bTransaction\x12&\n\x0etransaction_id\x18\x01 \x01(\tR\x0etransaction_id\x12\x1a\n\x08order_id\x18\x02 \x01(\tR\x08order_id\x12\x36\n\nprovenance\x18\x03 \x01(\x0e\x32\x16.order.OrderProvenanceR\nprovenance\x12;\n\x0eui_entry_point\x18\x04 \x01(\x0e\x32\x13.order.UIEntryPointR\x0eui_entry_point\x12$\n\rfrom_actor_id\x18\x05 \x01(\tR\rfrom_actor_id\x12;\n\x0f\x66rom_actor_type\x18\x06 \x01(\x0e\x32\x11.types.Actor.TypeR\x0f\x66rom_actor_type\x12\x18\n\x07\x66rom_pi\x18\x07 \x01(\tR\x07\x66rom_pi\x12L\n\x0c\x66rom_pi_type\x18\x08 \x01(\x0e\x32(.paymentinstrument.PaymentInstrumentTypeR\x0c\x66rom_pi_type\x12\x46\n\x16\x66rom_bank_account_type\x18\t \x01(\x0e\x32\x0e.accounts.TypeR\x16\x66rom_bank_account_type\x12(\n\x0f\x66rom_psp_handle\x18\n \x01(\tR\x0f\x66rom_psp_handle\x12&\n\x0e\x66rom_bank_name\x18\x0b \x01(\tR\x0e\x66rom_bank_name\x12(\n\x0f\x66rom_account_id\x18\x0c \x01(\tR\x0f\x66rom_account_id\x12*\n\x10\x66rom_merchant_id\x18\r \x01(\tR\x10\x66rom_merchant_id\x12.\n\x12\x66rom_merchant_name\x18\x0e \x01(\tR\x12\x66rom_merchant_name\x12 \n\x0bto_actor_id\x18\x0f \x01(\tR\x0bto_actor_id\x12\x37\n\rto_actor_type\x18\x10 \x01(\x0e\x32\x11.types.Actor.TypeR\rto_actor_type\x12\x14\n\x05to_pi\x18\x11 \x01(\tR\x05to_pi\x12H\n\nto_pi_type\x18\x12 \x01(\x0e\x32(.paymentinstrument.PaymentInstrumentTypeR\nto_pi_type\x12\x42\n\x14to_bank_account_type\x18\x13 \x01(\x0e\x32\x0e.accounts.TypeR\x14to_bank_account_type\x12"\n\x0cto_bank_name\x18\x14 \x01(\tR\x0cto_bank_name\x12$\n\rto_psp_handle\x18\x15 \x01(\tR\rto_psp_handle\x12$\n\rto_account_id\x18\x16 \x01(\tR\rto_account_id\x12&\n\x0eto_merchant_id\x18\x17 \x01(\tR\x0eto_merchant_id\x12*\n\x10to_merchant_name\x18\x18 \x01(\tR\x10to_merchant_name\x12\x16\n\x06\x61mount\x18\x19 \x01(\x03R\x06\x61mount\x12\x34\n\x15order_created_at_unix\x18\x1a \x01(\x03R\x15order_created_at_unix\x12@\n\x1btransaction_created_at_unix\x18\x1b \x01(\x03R\x1btransaction_created_at_unix\x12&\n\x0e\x65xpire_at_unix\x18\x1c \x01(\x03R\x0e\x65xpire_at_unix\x12(\n\x0fupdated_at_unix\x18\x1d \x01(\x03R\x0fupdated_at_unix\x12\x32\n\x14view_updated_at_unix\x18\x1e \x01(\x03R\x14view_updated_at_unix\x12*\n\x10\x65xecuted_at_unix\x18\x1f \x01(\x03R\x10\x65xecuted_at_unix\x12<\n\x19\x65xecuted_at_date_unix_ist\x18  \x01(\x03R\x19\x65xecuted_at_date_unix_ist\x12<\n\x0eorder_workflow\x18! \x01(\x0e\x32\x14.order.OrderWorkflowR\x0eorder_workflow\x12\x30\n\x13transaction_remarks\x18" \x01(\tR\x13transaction_remarks\x12J\n\x10payment_protocol\x18# \x01(\x0e\x32\x1e.order.payment.PaymentProtocolR\x10payment_protocol\x12\x39\n\x0cpartner_bank\x18$ \x01(\x0e\x32\x15.vendorgateway.VendorR\x0cpartner_bank\x12#\n\x04tags\x18% \x03(\x0e\x32\x0f.order.OrderTagR\x04tags\x12\x36\n\x0corder_status\x18& \x01(\x0e\x32\x12.order.OrderStatusR\x0corder_status\x12P\n\x12transaction_status\x18\' \x01(\x0e\x32 .order.payment.TransactionStatusR\x12transaction_status\x12\x46\n\x0c\x63redit_debit\x18( \x01(\x0e\x32".order.payment.AccountingEntryTypeR\x0c\x63redit_debit\x12K\n\x0e\x64\x65rived_status\x18) \x01(\x0e\x32#.analyser.Transaction.DerivedStatusR\x0e\x64\x65rived_status\x12"\n\x0contology_ids\x18* \x03(\tR\x0contology_ids\x12L\n\x12\x64isplay_categories\x18+ \x03(\x0e\x32\x1c.categorizer.DisplayCategoryR\x12\x64isplay_categories\x12\x35\n\rl0_ontologies\x18, \x03(\x0e\x32\x0f.categorizer.L0R\rl0_ontologies\x12\x36\n\x16\x66rom_derived_entity_id\x18- \x01(\tR\x16\x66rom_derived_entity_id\x12\x32\n\x14to_derived_entity_id\x18. \x01(\tR\x14to_derived_entity_id\x12@\n\x0f\x66rom_actor_tier\x18/ \x01(\x0e\x32\x16.tiering.external.TierR\x0f\x66rom_actor_tier"g\n\rDerivedStatus\x12\x1e\n\x1a\x44\x45RIVED_STATUS_UNSPECIFIED\x10\x00\x12\x1a\n\x16\x44\x45RIVED_STATUS_SUCCESS\x10\x01\x12\x1a\n\x16\x44\x45RIVED_STATUS_FAILURE\x10\x02\x42V\n)com.github.epifi.gamma.api.pinot.analyserZ)github.com/epifi/gamma/api/pinot/analyserb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.pinot.analyser.transaction_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n)com.github.epifi.gamma.api.pinot.analyserZ)github.com/epifi/gamma/api/pinot/analyser"
    )
    _globals["_TRANSACTION"]._serialized_start = 430
    _globals["_TRANSACTION"]._serialized_end = 2973
    _globals["_TRANSACTION_DERIVEDSTATUS"]._serialized_start = 2870
    _globals["_TRANSACTION_DERIVEDSTATUS"]._serialized_end = 2973
# @@protoc_insertion_point(module_scope)
