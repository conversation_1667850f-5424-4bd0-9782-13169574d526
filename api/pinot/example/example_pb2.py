# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/pinot/example/example.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x1f\x61pi/pinot/example/example.proto\x12\x07\x65xample"\x7f\n\x07\x45xample\x12\x0e\n\x02id\x18\x01 \x01(\tR\x02id\x12\x18\n\x07user_id\x18\x02 \x01(\tR\x07user_id\x12\x12\n\x04tags\x18\x03 \x03(\tR\x04tags\x12\x16\n\x06\x61mount\x18\x04 \x01(\x03R\x06\x61mount\x12\x1e\n\ncreated_at\x18\x05 \x01(\x03R\ncreated_atBT\n(com.github.epifi.gamma.api.pinot.exampleZ(github.com/epifi/gamma/api/pinot/exampleb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.pinot.example.example_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n(com.github.epifi.gamma.api.pinot.exampleZ(github.com/epifi/gamma/api/pinot/example"
    )
    _globals["_EXAMPLE"]._serialized_start = 44
    _globals["_EXAMPLE"]._serialized_end = 171
# @@protoc_insertion_point(module_scope)
