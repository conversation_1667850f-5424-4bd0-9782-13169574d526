# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/pinot/credit_card/transaction.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from api.categorizer import enums_pb2 as api_dot_categorizer_dot_enums__pb2
from api.firefly.accounting.enums import (
    enums_pb2 as api_dot_firefly_dot_accounting_dot_enums_dot_enums__pb2,
)

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n'api/pinot/credit_card/transaction.proto\x12\x0b\x63redit_card\x1a\x1b\x61pi/categorizer/enums.proto\x1a(api/firefly/accounting/enums/enums.proto\"\x8b\n\n\x0bTransaction\x12&\n\x0etransaction_id\x18\x01 \x01(\tR\x0etransaction_id\x12\x1e\n\naccount_id\x18\x02 \x01(\tR\naccount_id\x12&\n\x0e\x63redit_card_id\x18\x03 \x01(\tR\x0e\x63redit_card_id\x12\x1a\n\x08\x61\x63tor_id\x18\x04 \x01(\tR\x08\x61\x63tor_id\x12&\n\x0eother_actor_id\x18\x05 \x01(\tR\x0eother_actor_id\x12\x0e\n\x02pi\x18\x06 \x01(\tR\x02pi\x12\x1a\n\x08other_pi\x18\x07 \x01(\tR\x08other_pi\x12\x16\n\x06\x61mount\x18\x08 \x01(\x03R\x06\x61mount\x12[\n\x12transaction_origin\x18\t \x01(\x0e\x32+.firefly.accounting.enums.TransactionOriginR\x12transaction_origin\x12o\n\x1bvendor_transaction_category\x18\n \x01(\x0e\x32-.firefly.accounting.enums.TransactionCategoryR\x1bvendor_transaction_category\x12M\n\x0c\x63redit_debit\x18\x0b \x01(\x0e\x32).firefly.accounting.enums.TransactionTypeR\x0c\x63redit_debit\x12[\n\x12transaction_status\x18\x0c \x01(\x0e\x32+.firefly.accounting.enums.TransactionStatusR\x12transaction_status\x12 \n\x0bmerchant_id\x18\r \x01(\tR\x0bmerchant_id\x12$\n\rmerchant_name\x18\x0e \x01(\tR\rmerchant_name\x12@\n\x1btransaction_created_at_unix\x18\x0f \x01(\x03R\x1btransaction_created_at_unix\x12\x32\n\x14view_updated_at_unix\x18\x10 \x01(\x03R\x14view_updated_at_unix\x12*\n\x10\x65xecuted_at_unix\x18\x11 \x01(\x03R\x10\x65xecuted_at_unix\x12\x38\n\x17parent_executed_at_unix\x18\x12 \x01(\x03R\x17parent_executed_at_unix\x12\x32\n\x14\x64isplay_ontology_ids\x18\x13 \x03(\tR\x14\x64isplay_ontology_ids\x12(\n\x0f\x64s_ontology_ids\x18\x14 \x03(\tR\x0f\x64s_ontology_ids\x12L\n\x12\x64isplay_categories\x18\x15 \x03(\x0e\x32\x1c.categorizer.DisplayCategoryR\x12\x64isplay_categories\x12\x45\n\x15\x64isplay_l0_ontologies\x18\x16 \x03(\x0e\x32\x0f.categorizer.L0R\x15\x64isplay_l0_ontologies\x12;\n\x10\x64s_l0_ontologies\x18\x17 \x03(\x0e\x32\x0f.categorizer.L0R\x10\x64s_l0_ontologies\x12\x36\n\x16parent_ds_ontology_ids\x18\x18 \x03(\tR\x16parent_ds_ontology_idsB\\\n,com.github.epifi.gamma.api.pinot.credit_cardZ,github.com/epifi/gamma/api/pinot/credit_cardb\x06proto3"
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "api.pinot.credit_card.transaction_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n,com.github.epifi.gamma.api.pinot.credit_cardZ,github.com/epifi/gamma/api/pinot/credit_card"
    _globals["_TRANSACTION"]._serialized_start = 128
    _globals["_TRANSACTION"]._serialized_end = 1419
# @@protoc_insertion_point(module_scope)
