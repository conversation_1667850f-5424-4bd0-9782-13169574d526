# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/simulator/location/service.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2

from api.vendors.google import geocoding_pb2 as api_dot_vendors_dot_google_dot_geocoding__pb2
from api.vendors.inhouse import location_pb2 as api_dot_vendors_dot_inhouse_dot_location__pb2
from api.vendors.maxmind import ip2city_pb2 as api_dot_vendors_dot_maxmind_dot_ip2city__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n$api/simulator/location/service.proto\x12\x12simulator.location\x1a"api/vendors/google/geocoding.proto\x1a"api/vendors/inhouse/location.proto\x1a!api/vendors/maxmind/ip2city.proto\x1a\x1cgoogle/api/annotations.proto")\n\x1bGetCityDetailsFromIPRequest\x12\n\n\x02ip\x18\x01 \x01(\t2\xf3\x03\n\x08Location\x12\x8e\x01\n\x18GetAddressForCoordinates\x12\'.vendors.google.ReverseGeocodingRequest\x1a(.vendors.google.ReverseGeocodingResponse"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/get-address-coordinate\x12\x8f\x01\n\x0fGetAddressForIP\x12/.simulator.location.GetCityDetailsFromIPRequest\x1a-.vendors.maxmind.GetCityDetailsFromIPResponse"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/get-address-ip/{ip}\x12\xc3\x01\n\x1fGetAddressForCoordinatesInhouse\x12\x39.vendors.inhouse.GetLocationDetailsFromCoordinatesRequest\x1a:.vendors.inhouse.GetLocationDetailsFromCoordinatesResponse")\x82\xd3\xe4\x93\x02#\x12!/get-address-coordinate-inhouse/*B^\n-com.github.epifi.gamma.api.simulator.locationZ-github.com/epifi/gamma/api/simulator/locationb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.simulator.location.service_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n-com.github.epifi.gamma.api.simulator.locationZ-github.com/epifi/gamma/api/simulator/location"
    _LOCATION.methods_by_name["GetAddressForCoordinates"]._options = None
    _LOCATION.methods_by_name[
        "GetAddressForCoordinates"
    ]._serialized_options = b"\202\323\344\223\002\031\022\027/get-address-coordinate"
    _LOCATION.methods_by_name["GetAddressForIP"]._options = None
    _LOCATION.methods_by_name[
        "GetAddressForIP"
    ]._serialized_options = b"\202\323\344\223\002\026\022\024/get-address-ip/{ip}"
    _LOCATION.methods_by_name["GetAddressForCoordinatesInhouse"]._options = None
    _LOCATION.methods_by_name[
        "GetAddressForCoordinatesInhouse"
    ]._serialized_options = b"\202\323\344\223\002#\022!/get-address-coordinate-inhouse/*"
    _globals["_GETCITYDETAILSFROMIPREQUEST"]._serialized_start = 197
    _globals["_GETCITYDETAILSFROMIPREQUEST"]._serialized_end = 238
    _globals["_LOCATION"]._serialized_start = 241
    _globals["_LOCATION"]._serialized_end = 740
# @@protoc_insertion_point(module_scope)
