syntax = "proto3";

package simulator.docs;

import "api/vendors/karza/passport_verification.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/simulator/docs";
option java_package = "com.github.epifi.gamma.api.simulator.docs";


service Docs {
  rpc VerifyPassport (vendors.karza.PassportVerificationRequest) returns (vendors.karza.PassportVerificationResponse) {
    option (google.api.http) = {
      post: "/karza/v1/verify_passport"
      body: "*"
    };
  };
}
