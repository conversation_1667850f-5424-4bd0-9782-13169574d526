# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/app_instance_ids.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n api/types/app_instance_ids.proto\x12\x05types"F\n\rAppInstanceId\x12&\n\x04name\x18\x01 \x01(\x0e\x32\x18.types.AppInstanceIdName\x12\r\n\x05value\x18\x02 \x01(\t*t\n\x11\x41ppInstanceIdName\x12\x17\n\x13ID_NAME_UNSPECIFIED\x10\x00\x12\x0f\n\x0bPROSPECT_ID\x10\x01\x12\x17\n\x13\x43LIENT_APPSFLYER_ID\x10\x02\x12\x1c\n\x18\x46IREBASE_APP_INSTANCE_ID\x10\x03\x42\x44\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.types.app_instance_ids_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _globals["_APPINSTANCEIDNAME"]._serialized_start = 115
    _globals["_APPINSTANCEIDNAME"]._serialized_end = 231
    _globals["_APPINSTANCEID"]._serialized_start = 43
    _globals["_APPINSTANCEID"]._serialized_end = 113
# @@protoc_insertion_point(module_scope)
