# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/control_flags.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n\x1d\x61pi/types/control_flags.proto\x12\x05types*^\n\x0b\x43ontrolFlag\x12\x1c\n\x18\x43ONTROL_FLAG_UNSPECIFIED\x10\x00\x12\x17\n\x13\x43ONTROL_FLAG_ENABLE\x10\x01\x12\x18\n\x14\x43ONTROL_FLAG_DISABLE\x10\x02\x42\x44\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3"
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.types.control_flags_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _globals["_CONTROLFLAG"]._serialized_start = 40
    _globals["_CONTROLFLAG"]._serialized_end = 134
# @@protoc_insertion_point(module_scope)
