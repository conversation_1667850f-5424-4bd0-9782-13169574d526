# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/image_type.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x1a\x61pi/types/image_type.proto\x12\x05types*M\n\tImageType\x12\x1a\n\x16IMAGE_TYPE_UNSPECIFIED\x10\x00\x12\x08\n\x04JPEG\x10\x01\x12\x07\n\x03PNG\x10\x02\x12\x07\n\x03PDF\x10\x03\x12\x08\n\x04TIFF\x10\x04*s\n\x10ImageContentType\x12"\n\x1eIMAGE_CONTENT_TYPE_UNSPECIFIED\x10\x00\x12\x1e\n\x1aIMAGE_CONTENT_TYPE_PRODUCT\x10\x01\x12\x1b\n\x17IMAGE_CONTENT_TYPE_LOGO\x10\x02\x42\x44\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.types.image_type_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _globals["_IMAGETYPE"]._serialized_start = 37
    _globals["_IMAGETYPE"]._serialized_end = 114
    _globals["_IMAGECONTENTTYPE"]._serialized_start = 116
    _globals["_IMAGECONTENTTYPE"]._serialized_end = 231
# @@protoc_insertion_point(module_scope)
