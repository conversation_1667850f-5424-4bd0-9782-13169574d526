# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/service_name.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n\x1c\x61pi/types/service_name.proto\x12\x05types*\xe2\x0b\n\x0bServiceName\x12\x17\n\x13SERVICE_UNSPECIFIED\x10\x00\x12\x13\n\x0f\x41\x43\x43RUAL_SERVICE\x10\x01\x12\x11\n\rACTOR_SERVICE\x10\x02\x12\x11\n\rATLAS_SERVICE\x10\x03\x12\x10\n\x0c\x41UTH_SERVICE\x10\x04\x12\x12\n\x0e\x43\x41SBIN_SERVICE\x10\x05\x12\x12\n\x0e\x43\x41SPER_SERVICE\x10\x06\x12\x10\n\x0c\x43\x41RD_SERVICE\x10\x07\x12\x11\n\rCOMMS_SERVICE\x10\x08\x12\x19\n\x15\x43ONNECTED_ACC_SERVICE\x10\t\x12\x1e\n\x1a\x43USTOM_DELAY_QUEUE_SERVICE\x10\n\x12\x0e\n\nCX_SERVICE\x10\x0b\x12\x13\n\x0f\x44\x45POSIT_SERVICE\x10\x0c\x12\x10\n\x0c\x44OCS_SERVICE\x10\r\x12\x14\n\x10\x46RONTEND_SERVICE\x10\x0e\x12\x11\n\rFITTT_SERVICE\x10\x0f\x12\x16\n\x12INAPP_HELP_SERVICE\x10\x10\x12\x1a\n\x16INAPP_REFERRAL_SERVICE\x10\x11\x12\x13\n\x0fINDEXER_SERVICE\x10\x12\x12\x14\n\x10INSIGHTS_SERVICE\x10\x13\x12\x16\n\x12INVESTMENT_SERVICE\x10\x14\x12\x0f\n\x0bKYC_SERVICE\x10\x15\x12\x16\n\x12NLU_ENGINE_SERVICE\x10\x16\x12\x16\n\x12ONBOARDING_SERVICE\x10\x17\x12\x11\n\rORDER_SERVICE\x10\x18\x12\x1e\n\x1aPAYMENT_INSTRUMENT_SERVICE\x10\x19\x12\x1d\n\x19RECURRING_PAYMENT_SERVICE\x10\x1a\x12\x12\n\x0eREWARD_SERVICE\x10\x1b\x12\x0f\n\x0bRMS_SERVICE\x10\x1c\x12\x12\n\x0eRUDDER_SERVICE\x10\x1d\x12\x13\n\x0fSAVINGS_SERVICE\x10\x1e\x12\x12\n\x0eSEARCH_SERVICE\x10\x1f\x12\x1a\n\x16SIMULATOR_GRPC_SERVICE\x10 \x12\x1a\n\x16SIMULATOR_HTTP_SERVICE\x10!\x12\x14\n\x10TIMELINE_SERVICE\x10\"\x12\x15\n\x11TOKENIZER_SERVICE\x10#\x12\x0f\n\x0bUPI_SERVICE\x10$\x12\x10\n\x0cUSER_SERVICE\x10%\x12\x1a\n\x16VENDOR_GATEWAY_SERVICE\x10&\x12\x19\n\x15VENDORMAPPING_SERVICE\x10'\x12\x19\n\x15VENDOR_NOTIFI_SERVICE\x10(\x12!\n\x1dVENDOR_NOTIFI_GATEWAY_SERVICE\x10)\x12\x14\n\x10WAITLIST_SERVICE\x10*\x12\x10\n\x0cTEST_SERVICE\x10+\x12\x14\n\x10MERCHANT_SERVICE\x10,\x12\x18\n\x14WHATSAPP_BOT_SERVICE\x10-\x12\x15\n\x11WEALTH_ONBOARDING\x10.\x12\x14\n\x10\x41\x43\x43OUNTS_SERVICE\x10/\x12\x1c\n\x18\x44YNAMIC_ELEMENTS_SERVICE\x10\x30\x12\x1d\n\x19PRE_APPROVED_LOAN_SERVICE\x10\x31\x12\x13\n\x0f\x46IREFLY_SERVICE\x10\x32\x12\x15\n\x11US_STOCKS_SERVICE\x10\x33\x12\x13\n\x0fTIERING_SERVICE\x10\x34\x12\x10\n\x0cRISK_SERVICE\x10\x35\x12\x0f\n\x0bPAY_SERVICE\x10\x36\x12\x14\n\x10SHIPMENT_SERVICE\x10\x37\x12\x12\n\x0e\x41LFRED_SERVICE\x10\x38\x12\x19\n\x15\x42\x41NK_CUSTOMER_SERVICE\x10\x39\x12\x1a\n\x16P2P_INVESTMENT_SERVICE\x10:\x12\x16\n\x12\x45MPLOYMENT_SERVICE\x10;\x12\x1a\n\x16KYC_COMPLIANCE_SERVICE\x10<\x12\x1a\n\x16\x45RROR_ACTIVITY_SERVICE\x10=\x12\x1a\n\x16SALARY_PROGRAM_SERVICE\x10>\x12)\n%WATSON_MANUAL_TICKET_HANDLING_SERVICE\x10?BD\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3"
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.types.service_name_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _globals["_SERVICENAME"]._serialized_start = 40
    _globals["_SERVICENAME"]._serialized_end = 1546
# @@protoc_insertion_point(module_scope)
