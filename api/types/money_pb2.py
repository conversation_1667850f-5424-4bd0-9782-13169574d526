# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/money.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from validate import validate_pb2 as validate_dot_validate__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x15\x61pi/types/money.proto\x12\x05types\x1a\x17validate/validate.proto"]\n\x05Money\x12\x1f\n\rcurrency_code\x18\x01 \x01(\tB\x08\xfa\x42\x05r\x03\x98\x01\x03\x12\x16\n\x05units\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04"\x02(\x00\x12\x1b\n\x08\x64\x65\x63imals\x18\x03 \x01(\x05\x42\t\xfa\x42\x06\x1a\x04\x18\x63(\x00"\x7f\n\x16\x43ustomAmountConstraint\x12 \n\nmin_amount\x18\x01 \x01(\x0b\x32\x0c.types.Money\x12 \n\nmax_amount\x18\x02 \x01(\x0b\x32\x0c.types.Money\x12!\n\x0bstep_amount\x18\x03 \x01(\x0b\x32\x0c.types.MoneyBD\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.types.money_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _MONEY.fields_by_name["currency_code"]._options = None
    _MONEY.fields_by_name["currency_code"]._serialized_options = b"\372B\005r\003\230\001\003"
    _MONEY.fields_by_name["units"]._options = None
    _MONEY.fields_by_name["units"]._serialized_options = b'\372B\004"\002(\000'
    _MONEY.fields_by_name["decimals"]._options = None
    _MONEY.fields_by_name["decimals"]._serialized_options = b"\372B\006\032\004\030c(\000"
    _globals["_MONEY"]._serialized_start = 57
    _globals["_MONEY"]._serialized_end = 150
    _globals["_CUSTOMAMOUNTCONSTRAINT"]._serialized_start = 152
    _globals["_CUSTOMAMOUNTCONSTRAINT"]._serialized_end = 279
# @@protoc_insertion_point(module_scope)
