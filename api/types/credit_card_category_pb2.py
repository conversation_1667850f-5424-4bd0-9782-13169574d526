# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/credit_card_category.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n$api/types/credit_card_category.proto\x12\x05types*b\n\x12\x43reditCardCategory\x12$\n CREDIT_CARD_CATEGORY_UNSPECIFIED\x10\x00\x12\x0c\n\x08PLATINUM\x10\x01\x12\x0b\n\x07\x43LASSIC\x10\x02\x12\x0b\n\x07PREMIUM\x10\x03\x42\x44\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3"
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.types.credit_card_category_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _globals["_CREDITCARDCATEGORY"]._serialized_start = 47
    _globals["_CREDITCARDCATEGORY"]._serialized_end = 145
# @@protoc_insertion_point(module_scope)
