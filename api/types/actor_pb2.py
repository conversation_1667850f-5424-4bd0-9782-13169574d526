# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/actor.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

from api.types import ownership_pb2 as api_dot_types_dot_ownership__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x15\x61pi/types/actor.proto\x12\x05types\x1a\x19\x61pi/types/ownership.proto\x1a\x1fgoogle/protobuf/timestamp.proto"\x97\x03\n\x05\x41\x63tor\x12\n\n\x02id\x18\x01 \x01(\t\x12#\n\x04type\x18\x02 \x01(\x0e\x32\x11.types.Actor.TypeB\x02\x18\x01\x12\x11\n\tentity_id\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x04 \x01(\t\x12.\n\ncreated_at\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ndeleted_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12#\n\townership\x18\x08 \x01(\x0e\x32\x10.types.Ownership"\x86\x01\n\x04Type\x12\x14\n\x10TYPE_UNSPECIFIED\x10\x00\x12\x08\n\x04USER\x10\x01\x12\x0c\n\x08MERCHANT\x10\x02\x12\x11\n\rEXTERNAL_USER\x10\x03\x12\x15\n\x11\x45XTERNAL_MERCHANT\x10\x04\x12\x13\n\x0fWAITLISTED_USER\x10\x05\x12\r\n\tKYC_AGENT\x10\x06\x1a\x02\x18\x01*\x87\x01\n\tActorType\x12\x14\n\x10TYPE_UNSPECIFIED\x10\x00\x12\x08\n\x04USER\x10\x01\x12\x0c\n\x08MERCHANT\x10\x02\x12\x11\n\rEXTERNAL_USER\x10\x03\x12\x15\n\x11\x45XTERNAL_MERCHANT\x10\x04\x12\x13\n\x0fWAITLISTED_USER\x10\x05\x12\r\n\tKYC_AGENT\x10\x06\x42\x44\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.types.actor_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _ACTOR_TYPE._options = None
    _ACTOR_TYPE._serialized_options = b"\030\001"
    _ACTOR.fields_by_name["type"]._options = None
    _ACTOR.fields_by_name["type"]._serialized_options = b"\030\001"
    _globals["_ACTORTYPE"]._serialized_start = 503
    _globals["_ACTORTYPE"]._serialized_end = 638
    _globals["_ACTOR"]._serialized_start = 93
    _globals["_ACTOR"]._serialized_end = 500
    _globals["_ACTOR_TYPE"]._serialized_start = 366
    _globals["_ACTOR_TYPE"]._serialized_end = 500
# @@protoc_insertion_point(module_scope)
