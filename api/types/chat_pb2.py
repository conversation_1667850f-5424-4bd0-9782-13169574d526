# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/chat.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n\x14\x61pi/types/chat.proto\x12\x05types*\x91\x01\n\x11InAppChatViewType\x12%\n!IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED\x10\x00\x12'\n#IN_APP_CHAT_VIEW_TYPE_FRESHCHAT_SDK\x10\x01\x12,\n(IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW\x10\x02\x42\x44\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3"
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.types.chat_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _globals["_INAPPCHATVIEWTYPE"]._serialized_start = 32
    _globals["_INAPPCHATVIEWTYPE"]._serialized_end = 177
# @@protoc_insertion_point(module_scope)
