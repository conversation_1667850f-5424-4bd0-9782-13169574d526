# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/address_for_identifier.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from api.types import address_pb2 as api_dot_types_dot_address__pb2
from api.types import coordinates_pb2 as api_dot_types_dot_coordinates__pb2
from api.types import identifier_pb2 as api_dot_types_dot_identifier__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n&api/types/address_for_identifier.proto\x12\x05types\x1a\x17\x61pi/types/address.proto\x1a\x1b\x61pi/types/coordinates.proto\x1a\x1a\x61pi/types/identifier.proto"\xc8\x01\n\x14\x41\x64\x64ressForIdentifier\x12.\n\x0fidentifier_type\x18\x01 \x01(\x0e\x32\x15.types.IdentifierType\x12\x18\n\x10identifier_value\x18\x02 \x01(\t\x12%\n\x07\x61\x64\x64ress\x18\x03 \x01(\x0b\x32\x14.types.PostalAddress\x12-\n\x0b\x63oordinates\x18\x04 \x01(\x0b\x32\x18.types.LatLongWithRadius\x12\x10\n\x08h3_index\x18\x05 \x01(\tBD\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "api.types.address_for_identifier_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _globals["_ADDRESSFORIDENTIFIER"]._serialized_start = 132
    _globals["_ADDRESSFORIDENTIFIER"]._serialized_end = 332
# @@protoc_insertion_point(module_scope)
