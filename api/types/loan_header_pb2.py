# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/loan_header.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x1b\x61pi/types/loan_header.proto\x12\x05types"U\n\nLoanHeader\x12(\n\x0cloan_program\x18\x01 \x01(\x0e\x32\x12.types.LoanProgram\x12\x1d\n\x06vendor\x18\x02 \x01(\x0e\x32\r.types.Vendor*\xf3\x01\n\x0bLoanProgram\x12\x1c\n\x18LOAN_PROGRAM_UNSPECIFIED\x10\x00\x12"\n\x1eLOAN_PROGRAM_PRE_APPROVED_LOAN\x10\x01\x12\x1d\n\x19LOAN_PROGRAM_EARLY_SALARY\x10\x02\x12\x15\n\x11LOAN_PROGRAM_FLDG\x10\x03\x12\x1e\n\x1aLOAN_PROGRAM_FED_REAL_TIME\x10\x04\x12\x15\n\x11LOAN_PROGRAM_LAMF\x10\x05\x12\x1e\n\x1aLOAN_PROGRAM_REAL_TIME_ETB\x10\x06\x12\x15\n\x11LOAN_PROGRAM_STPL\x10\x07*\xaf\x01\n\x06Vendor\x12\x16\n\x12VENDOR_UNSPECIFIED\x10\x00\x12\x12\n\x0eVENDOR_FEDERAL\x10\x01\x12\x15\n\x11VENDOR_LIQUILOANS\x10\x02\x12\x0f\n\x0bVENDOR_IDFC\x10\x03\x12\x13\n\x0fVENDOR_FIFTYFIN\x10\x04\x12\x15\n\x11VENDOR_EPIFI_TECH\x10\x05\x12\x14\n\x10VENDOR_MONEYVIEW\x10\x06\x12\x0f\n\x0bVENDOR_ABFL\x10\x07\x42\x44\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.types.loan_header_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _globals["_LOANPROGRAM"]._serialized_start = 126
    _globals["_LOANPROGRAM"]._serialized_end = 369
    _globals["_VENDOR"]._serialized_start = 372
    _globals["_VENDOR"]._serialized_end = 547
    _globals["_LOANHEADER"]._serialized_start = 38
    _globals["_LOANHEADER"]._serialized_end = 123
# @@protoc_insertion_point(module_scope)
