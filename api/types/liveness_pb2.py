# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/liveness.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from api.types import boolean_pb2 as api_dot_types_dot_boolean__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x18\x61pi/types/liveness.proto\x12\x05types\x1a\x17\x61pi/types/boolean.proto"^\n\x1aThreeDimensionalCoordinate\x12\x14\n\x0cx_coordinate\x18\x01 \x01(\x02\x12\x14\n\x0cy_coordinate\x18\x02 \x01(\x02\x12\x14\n\x0cz_coordinate\x18\x03 \x01(\x02"0\n\x07VPNInfo\x12%\n\tvpn_usage\x18\x06 \x01(\x0e\x32\x12.types.BooleanEnumBD\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.types.liveness_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _globals["_THREEDIMENSIONALCOORDINATE"]._serialized_start = 60
    _globals["_THREEDIMENSIONALCOORDINATE"]._serialized_end = 154
    _globals["_VPNINFO"]._serialized_start = 156
    _globals["_VPNINFO"]._serialized_end = 204
# @@protoc_insertion_point(module_scope)
