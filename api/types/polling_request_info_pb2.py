# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/polling_request_info.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n$api/types/polling_request_info.proto\x12\x05types""\n\x12PollingRequestInfo\x12\x0c\n\x04\x62lob\x18\x01 \x01(\x0c"C\n\x13PollingResponseInfo\x12\x0c\n\x04\x62lob\x18\x01 \x01(\x0c\x12\x1e\n\x16retry_interval_seconds\x18\x02 \x01(\x05\x42\x44\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.types.polling_request_info_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _globals["_POLLINGREQUESTINFO"]._serialized_start = 47
    _globals["_POLLINGREQUESTINFO"]._serialized_end = 81
    _globals["_POLLINGRESPONSEINFO"]._serialized_start = 83
    _globals["_POLLINGRESPONSEINFO"]._serialized_end = 150
# @@protoc_insertion_point(module_scope)
