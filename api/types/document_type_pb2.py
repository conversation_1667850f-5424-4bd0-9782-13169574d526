# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/document_type.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n\x1d\x61pi/types/document_type.proto\x12\x05types*\xc5\x01\n\x0c\x44ocumentType\x12\x1d\n\x19\x44OCUMENT_TYPE_UNSPECIFIED\x10\x00\x12\x07\n\x03PAN\x10\x01\x12\n\n\x06\x41\x41\x44HAR\x10\x02\x12\x0c\n\x08VOTER_ID\x10\x03\x12\x0c\n\x08PASSPORT\x10\x04\x12\x13\n\x0f\x44RIVING_LICENCE\x10\x05\x12\x19\n\x15\x44OCUMENT_TYPE_AADHAAR\x10\x02\x12\x15\n\x11\x44OCUMENT_TYPE_PAN\x10\x01\x12\x1a\n\x16\x44OCUMENT_TYPE_PASSPORT\x10\x04\x1a\x02\x10\x01\x42\x44\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3"
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.types.document_type_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _DOCUMENTTYPE._options = None
    _DOCUMENTTYPE._serialized_options = b"\020\001"
    _globals["_DOCUMENTTYPE"]._serialized_start = 41
    _globals["_DOCUMENTTYPE"]._serialized_end = 238
# @@protoc_insertion_point(module_scope)
