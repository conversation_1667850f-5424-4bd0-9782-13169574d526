# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/ownership.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n\x19\x61pi/types/ownership.proto\x12\x05types*\x86\x02\n\tOwnership\x12\x0e\n\nEPIFI_TECH\x10\x00\x12\x10\n\x0c\x45PIFI_WEALTH\x10\x01\x12\x10\n\x0c\x46\x45\x44\x45RAL_BANK\x10\x02\x12\x14\n\x10US_STOCKS_ALPACA\x10\x03\x12\x1d\n\x19P2P_INVESTMENT_LIQUILOANS\x10\x04\x12\x11\n\rLIQUILOANS_PL\x10\x05\x12\x17\n\x13\x46\x45\x44\x45RAL_CREDIT_CARD\x10\x06\x12\x0b\n\x07IDFC_PL\x10\x07\x12\x11\n\rFIFTYFIN_LAMF\x10\x08\x12\x0f\n\x07\x41\x42\x46L_PL\x10\t\x1a\x02\x08\x01\x12\x10\n\x0cMONEYVIEW_PL\x10\n\x12\x11\n\rEPIFI_TECH_V2\x10\x0b\x12\x0e\n\nLOANS_ABFL\x10\x0c\x42\x44\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3"
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.types.ownership_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _OWNERSHIP.values_by_name["ABFL_PL"]._options = None
    _OWNERSHIP.values_by_name["ABFL_PL"]._serialized_options = b"\010\001"
    _globals["_OWNERSHIP"]._serialized_start = 37
    _globals["_OWNERSHIP"]._serialized_end = 299
# @@protoc_insertion_point(module_scope)
