# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/inapphelp_media_uicontext.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n)api/types/inapphelp_media_uicontext.proto\x12\x05types"\xeb\x03\n\x1bInapphelpMediaUIContextMeta\x12J\n\x0c\x66\x61q_category\x18\x01 \x01(\x0b\x32\x32.types.InapphelpMediaUIContextMeta.FAQCategoryMetaH\x00\x12H\n\x0btxn_receipt\x18\x02 \x01(\x0b\x32\x31.types.InapphelpMediaUIContextMeta.TxnReceiptMetaH\x00\x1a=\n\x0f\x46\x41QCategoryMeta\x12\x13\n\x0b\x63\x61tegory_id\x18\x01 \x01(\x03\x12\x15\n\rcategory_name\x18\x02 \x01(\t\x1a\xee\x01\n\x0eTxnReceiptMeta\x12Q\n\x0bstory_state\x18\x01 \x01(\x0e\x32<.types.InapphelpMediaUIContextMeta.TxnReceiptMeta.StoryState"\x88\x01\n\nStoryState\x12\x1b\n\x17STORY_STATE_UNSPECIFIED\x10\x00\x12\x16\n\x12UPI_DEEMED_SUCCESS\x10\x01\x12\x16\n\x12UPI_P2P_FAILED_TXN\x10\x02\x12\x16\n\x12UPI_P2M_FAILED_TXN\x10\x03\x12\x15\n\x11\x45\x43S_ENACH_CHARGES\x10\x04\x42\x06\n\x04metaBD\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "api.types.inapphelp_media_uicontext_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _globals["_INAPPHELPMEDIAUICONTEXTMETA"]._serialized_start = 53
    _globals["_INAPPHELPMEDIAUICONTEXTMETA"]._serialized_end = 544
    _globals["_INAPPHELPMEDIAUICONTEXTMETA_FAQCATEGORYMETA"]._serialized_start = 234
    _globals["_INAPPHELPMEDIAUICONTEXTMETA_FAQCATEGORYMETA"]._serialized_end = 295
    _globals["_INAPPHELPMEDIAUICONTEXTMETA_TXNRECEIPTMETA"]._serialized_start = 298
    _globals["_INAPPHELPMEDIAUICONTEXTMETA_TXNRECEIPTMETA"]._serialized_end = 536
    _globals["_INAPPHELPMEDIAUICONTEXTMETA_TXNRECEIPTMETA_STORYSTATE"]._serialized_start = 400
    _globals["_INAPPHELPMEDIAUICONTEXTMETA_TXNRECEIPTMETA_STORYSTATE"]._serialized_end = 536
# @@protoc_insertion_point(module_scope)
