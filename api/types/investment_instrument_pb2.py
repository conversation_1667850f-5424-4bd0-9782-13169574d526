# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/investment_instrument.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n%api/types/investment_instrument.proto\x12\x05types*\xe6\x02\n\x18InvestmentInstrumentType\x12*\n&INVESTMENT_INSTRUMENT_TYPE_UNSPECIFIED\x10\x00\x12\x10\n\x0cMUTUAL_FUNDS\x10\x01\x12\x0c\n\x08P2P_JUMP\x10\x02\x12\x11\n\rSMART_DEPOSIT\x10\x03\x12\x11\n\rFIXED_DEPOSIT\x10\x04\x12\r\n\tUS_STOCKS\x10\x05\x12\x15\n\x11RECURRING_DEPOSIT\x10\x06\x12\x11\n\rINDIAN_STOCKS\x10\x07\x12\x0f\n\x0bREAL_ESTATE\x10\x08\x12\x07\n\x03\x41IF\x10\t\x12\x12\n\x0ePRIVATE_EQUITY\x10\n\x12\x10\n\x0c\x44IGITAL_GOLD\x10\x0b\x12\x08\n\x04\x43\x41SH\x10\x0c\x12\x12\n\x0e\x44IGITAL_SILVER\x10\r\x12\x08\n\x04\x42OND\x10\x0e\x12\x15\n\x11\x41RT_AND_ARTEFACTS\x10\x0f\x12 \n\x1cPORTFOLIO_MANAGEMENT_SERVICE\x10\x10\x42\x44\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3"
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "api.types.investment_instrument_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _globals["_INVESTMENTINSTRUMENTTYPE"]._serialized_start = 49
    _globals["_INVESTMENTINSTRUMENTTYPE"]._serialized_end = 407
# @@protoc_insertion_point(module_scope)
