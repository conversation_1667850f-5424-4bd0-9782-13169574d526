# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/income_slab.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n\x1b\x61pi/types/income_slab.proto\x12\x05types*\x94\x02\n\nIncomeSlab\x12\x1b\n\x17INCOME_SLAB_UNSPECIFIED\x10\x00\x12\x1b\n\x17INCOME_SLAB_BELOW_1_LAC\x10\x01\x12\x1a\n\x16INCOME_SLAB_1_TO_5_LAC\x10\x02\x12\x1b\n\x17INCOME_SLAB_5_TO_10_LAC\x10\x03\x12\x1c\n\x18INCOME_SLAB_10_TO_25_LAC\x10\x04\x12!\n\x1dINCOME_SLAB_25_LAC_TO_1_CRORE\x10\x05\x12\x1d\n\x19INCOME_SLAB_ABOVE_1_CRORE\x10\x06\x12\x1c\n\x18INCOME_SLAB_ABOVE_25_LAC\x10\x07\x12\x15\n\x11INCOME_SLAB_OTHER\x10\x08\x42\x44\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3"
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.types.income_slab_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _globals["_INCOMESLAB"]._serialized_start = 39
    _globals["_INCOMESLAB"]._serialized_end = 315
# @@protoc_insertion_point(module_scope)
