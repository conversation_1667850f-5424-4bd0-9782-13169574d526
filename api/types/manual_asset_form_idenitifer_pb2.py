# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/manual_asset_form_idenitifer.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n,api/types/manual_asset_form_idenitifer.proto\x12\x05types"V\n\x19ManualAssetFormIdentifier\x12\x14\n\nasset_type\x18\x02 \x01(\tH\x00\x12\x15\n\x0b\x65xternal_id\x18\x03 \x01(\tH\x00\x42\x0c\n\nidentifierBD\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "api.types.manual_asset_form_idenitifer_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _globals["_MANUALASSETFORMIDENTIFIER"]._serialized_start = 55
    _globals["_MANUALASSETFORMIDENTIFIER"]._serialized_end = 141
# @@protoc_insertion_point(module_scope)
