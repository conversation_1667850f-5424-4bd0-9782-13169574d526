# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/device_properties.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

from api.types import device_pb2 as api_dot_types_dot_device__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n!api/types/device_properties.proto\x12\x05types\x1a\x16\x61pi/types/device.proto\x1a\x1fgoogle/protobuf/timestamp.proto"\xbd\x01\n\x1bUserDeviceMaliciousAppsInfo\x12 \n\x08\x61pp_list\x18\x01 \x03(\x0b\x32\x0e.types.AppInfo\x12-\n\tlast_scan\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x0c\x65rror_reason\x18\x03 \x01(\x0e\x32\x16.types.VerifyAppsError\x12\x1f\n\x17safety_net_error_string\x18\x04 \x01(\t"y\n\x12UserDeviceAppsInfo\x12\x34\n\tapps_list\x18\x01 \x03(\x0b\x32!.types.UserDeviceInstalledAppInfo\x12-\n\tlast_scan\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp"\x81\x01\n\x1aUserDeviceInstalledAppInfo\x12\x14\n\x0cpackage_name\x18\x01 \x01(\t\x12\x10\n\x08\x61pp_name\x18\x02 \x01(\t\x12(\n\x0c\x61pp_category\x18\x03 \x01(\x0e\x32\x12.types.AppCategory\x12\x11\n\tapp_flags\x18\x04 \x01(\x05"Q\n\x10\x44\x65viceMemoryInfo\x12\x17\n\x0ftotal_ram_in_mb\x18\x01 \x01(\x04\x12$\n\x1ctotal_internal_storage_in_mb\x18\x02 \x01(\x04"e\n\x07\x41ppInfo\x12\x14\n\x0cpackage_name\x18\x01 \x01(\t\x12\x1a\n\x12sha256_fingerprint\x18\x02 \x01(\t\x12(\n\x0c\x61pk_category\x18\x03 \x01(\x0e\x32\x12.types.APKCategory"g\n\x0e\x41ppVersionInfo\x12!\n\x08platform\x18\x05 \x01(\x0e\x32\x0f.types.Platform\x12\x18\n\x10\x61pp_version_name\x18\x06 \x01(\t\x12\x18\n\x10\x61pp_version_code\x18\x07 \x01(\r*\x80\x01\n\x0fVerifyAppsError\x12!\n\x1dVERIFY_APPS_ERROR_UNSPECIFIED\x10\x00\x12&\n"VERIFY_APPS_ERROR_DISABLED_BY_USER\x10\x01\x12"\n\x1eVERIFY_APPS_ERROR_VENDOR_ERROR\x10\x02*\x90\x02\n\x0b\x41ppCategory\x12\x1c\n\x18\x41PP_CATEGORY_UNSPECIFIED\x10\x00\x12\x15\n\x11\x41PP_CATEGORY_GAME\x10\x01\x12\x16\n\x12\x41PP_CATEGORY_AUDIO\x10\x02\x12\x16\n\x12\x41PP_CATEGORY_VIDEO\x10\x03\x12\x16\n\x12\x41PP_CATEGORY_IMAGE\x10\x04\x12\x17\n\x13\x41PP_CATEGORY_SOCIAL\x10\x05\x12\x15\n\x11\x41PP_CATEGORY_NEWS\x10\x06\x12\x15\n\x11\x41PP_CATEGORY_MAPS\x10\x07\x12\x1d\n\x19\x41PP_CATEGORY_PRODUCTIVITY\x10\x08\x12\x1e\n\x1a\x41PP_CATEGORY_ACCESSIBILITY\x10\t*\x89\x08\n\x0b\x41PKCategory\x12\x1c\n\x18\x41PK_CATEGORY_UNSPECIFIED\x10\x00\x12*\n&APK_CATEGORY_HARMFUL_CATEGORY_BACKDOOR\x10\x01\x12,\n(APK_CATEGORY_HARMFUL_CATEGORY_CALL_FRAUD\x10\x02\x12\x31\n-APK_CATEGORY_HARMFUL_CATEGORY_DATA_COLLECTION\x10\x03\x12\x33\n/APK_CATEGORY_HARMFUL_CATEGORY_DENIAL_OF_SERVICE\x10\x04\x12+\n\'APK_CATEGORY_HARMFUL_CATEGORY_FRAUDWARE\x10\x06\x12\x31\n-APK_CATEGORY_HARMFUL_CATEGORY_GENERIC_MALWARE\x10\x07\x12.\n*APK_CATEGORY_HARMFUL_CATEGORY_HARMFUL_SITE\x10\x08\x12\x34\n0APK_CATEGORY_HARMFUL_CATEGORY_HOSTILE_DOWNLOADER\x10\t\x12\x34\n0APK_CATEGORY_HARMFUL_CATEGORY_NON_ANDROID_THREAT\x10\n\x12*\n&APK_CATEGORY_HARMFUL_CATEGORY_PHISHING\x10\x0b\x12\x36\n2APK_CATEGORY_HARMFUL_CATEGORY_PRIVILEGE_ESCALATION\x10\x0c\x12,\n(APK_CATEGORY_HARMFUL_CATEGORY_RANSOMWARE\x10\r\x12)\n%APK_CATEGORY_HARMFUL_CATEGORY_ROOTING\x10\x0e\x12&\n"APK_CATEGORY_HARMFUL_CATEGORY_SPAM\x10\x0f\x12)\n%APK_CATEGORY_HARMFUL_CATEGORY_SPYWARE\x10\x10\x12,\n(APK_CATEGORY_HARMFUL_CATEGORY_TOLL_FRAUD\x10\x11\x12*\n&APK_CATEGORY_HARMFUL_CATEGORY_TRACKING\x10\x12\x12(\n$APK_CATEGORY_HARMFUL_CATEGORY_TROJAN\x10\x13\x12*\n&APK_CATEGORY_HARMFUL_CATEGORY_UNCOMMON\x10\x14\x12+\n\'APK_CATEGORY_HARMFUL_CATEGORY_WAP_FRAUD\x10\x15\x12\x31\n-APK_CATEGORY_HARMFUL_CATEGORY_WINDOWS_MALWARE\x10\x16\x42\x44\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.types.device_properties_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _globals["_VERIFYAPPSERROR"]._serialized_start = 840
    _globals["_VERIFYAPPSERROR"]._serialized_end = 968
    _globals["_APPCATEGORY"]._serialized_start = 971
    _globals["_APPCATEGORY"]._serialized_end = 1243
    _globals["_APKCATEGORY"]._serialized_start = 1246
    _globals["_APKCATEGORY"]._serialized_end = 2279
    _globals["_USERDEVICEMALICIOUSAPPSINFO"]._serialized_start = 102
    _globals["_USERDEVICEMALICIOUSAPPSINFO"]._serialized_end = 291
    _globals["_USERDEVICEAPPSINFO"]._serialized_start = 293
    _globals["_USERDEVICEAPPSINFO"]._serialized_end = 414
    _globals["_USERDEVICEINSTALLEDAPPINFO"]._serialized_start = 417
    _globals["_USERDEVICEINSTALLEDAPPINFO"]._serialized_end = 546
    _globals["_DEVICEMEMORYINFO"]._serialized_start = 548
    _globals["_DEVICEMEMORYINFO"]._serialized_end = 629
    _globals["_APPINFO"]._serialized_start = 631
    _globals["_APPINFO"]._serialized_end = 732
    _globals["_APPVERSIONINFO"]._serialized_start = 734
    _globals["_APPVERSIONINFO"]._serialized_end = 837
# @@protoc_insertion_point(module_scope)
