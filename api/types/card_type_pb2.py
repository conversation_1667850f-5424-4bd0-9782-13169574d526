# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/types/card_type.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n\x19\x61pi/types/card_type.proto\x12\x05types*@\n\x08\x43\x61rdType\x12\x1d\n\x19\x43\x41RD_CATEGORY_UNSPECIFIED\x10\x00\x12\n\n\x06\x43REDIT\x10\x01\x12\t\n\x05\x44\x45\x42IT\x10\x02\x42\x44\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/typesb\x06proto3"
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.types.card_type_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.typesZ github.com/epifi/gamma/api/types"
    )
    _globals["_CARDTYPE"]._serialized_start = 36
    _globals["_CARDTYPE"]._serialized_end = 100
# @@protoc_insertion_point(module_scope)
