# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/queue/queue_retry_info.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n api/queue/queue_retry_info.proto\x12\x05queue\x1a\x1fgoogle/protobuf/timestamp.proto"{\n\x0eQueueRetryInfo\x12\x14\n\x0cqueue_msg_id\x18\x01 \x01(\t\x12\x0e\n\x06req_id\x18\x02 \x01(\t\x12\x10\n\x08\x61ttempts\x18\x03 \x01(\x05\x12\x31\n\rfirst_attempt\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.TimestampBD\n com.github.epifi.gamma.api.queueZ github.com/epifi/gamma/api/queueb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.queue.queue_retry_info_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.queueZ github.com/epifi/gamma/api/queue"
    )
    _globals["_QUEUERETRYINFO"]._serialized_start = 76
    _globals["_QUEUERETRYINFO"]._serialized_end = 199
# @@protoc_insertion_point(module_scope)
