# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api/queue/consumer_headers.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from api.rpc import status_pb2 as api_dot_rpc_dot_status__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n api/queue/consumer_headers.proto\x12\x05queue\x1a\x14\x61pi/rpc/status.proto"\x86\x01\n\x16\x43onsumerResponseHeader\x12/\n\x06status\x18\x01 \x01(\x0e\x32\x1f.queue.MessageConsumptionStatus\x12\x14\n\x0cnext_timeout\x18\x02 \x01(\x03\x12%\n\x10grpc_status_code\x18\x03 \x01(\x0b\x32\x0b.rpc.Status"0\n\x15\x43onsumerRequestHeader\x12\x17\n\x0fis_last_attempt\x18\x01 \x01(\x08*m\n\x18MessageConsumptionStatus\x12\x16\n\x12STATUS_UNSPECIFIED\x10\x00\x12\x0b\n\x07SUCCESS\x10\x01\x12\x15\n\x11TRANSIENT_FAILURE\x10\x02\x12\x15\n\x11PERMANENT_FAILURE\x10\x03\x42\x44\n com.github.epifi.gamma.api.queueZ github.com/epifi/gamma/api/queueb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "api.queue.consumer_headers_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = (
        b"\n com.github.epifi.gamma.api.queueZ github.com/epifi/gamma/api/queue"
    )
    _globals["_MESSAGECONSUMPTIONSTATUS"]._serialized_start = 252
    _globals["_MESSAGECONSUMPTIONSTATUS"]._serialized_end = 361
    _globals["_CONSUMERRESPONSEHEADER"]._serialized_start = 66
    _globals["_CONSUMERRESPONSEHEADER"]._serialized_end = 200
    _globals["_CONSUMERREQUESTHEADER"]._serialized_start = 202
    _globals["_CONSUMERREQUESTHEADER"]._serialized_end = 250
# @@protoc_insertion_point(module_scope)
