// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/call/enums.proto

package call

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CallType enum to specify what kind of call it is
// this reflects exhaustive list of values which can are applicable for this enum identifier
type CallType int32

const (
	CallType_CALL_TYPE_UNSPECIFIED CallType = 0
	CallType_CALL_TYPE_PROGRESSIVE CallType = 1
	// incoming call to our campaign
	CallType_CALL_TYPE_INBOUND    CallType = 3
	CallType_CALL_TYPE_MANUAL     CallType = 4
	CallType_CALL_TYPE_PREVIEW    CallType = 5
	CallType_CALL_TYPE_MAIL       CallType = 6
	CallType_CALL_TYPE_IVR        CallType = 7
	CallType_CALL_TYPE_CHAT       CallType = 8
	CallType_CALL_TYPE_PREDICTIVE CallType = 9
)

// Enum value maps for CallType.
var (
	CallType_name = map[int32]string{
		0: "CALL_TYPE_UNSPECIFIED",
		1: "CALL_TYPE_PROGRESSIVE",
		3: "CALL_TYPE_INBOUND",
		4: "CALL_TYPE_MANUAL",
		5: "CALL_TYPE_PREVIEW",
		6: "CALL_TYPE_MAIL",
		7: "CALL_TYPE_IVR",
		8: "CALL_TYPE_CHAT",
		9: "CALL_TYPE_PREDICTIVE",
	}
	CallType_value = map[string]int32{
		"CALL_TYPE_UNSPECIFIED": 0,
		"CALL_TYPE_PROGRESSIVE": 1,
		"CALL_TYPE_INBOUND":     3,
		"CALL_TYPE_MANUAL":      4,
		"CALL_TYPE_PREVIEW":     5,
		"CALL_TYPE_MAIL":        6,
		"CALL_TYPE_IVR":         7,
		"CALL_TYPE_CHAT":        8,
		"CALL_TYPE_PREDICTIVE":  9,
	}
)

func (x CallType) Enum() *CallType {
	p := new(CallType)
	*p = x
	return p
}

func (x CallType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CallType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_call_enums_proto_enumTypes[0].Descriptor()
}

func (CallType) Type() protoreflect.EnumType {
	return &file_api_cx_call_enums_proto_enumTypes[0]
}

func (x CallType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CallType.Descriptor instead.
func (CallType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_call_enums_proto_rawDescGZIP(), []int{0}
}

// CallHandleStatus enum describing whether was answered or not
type CallHandleStatus int32

const (
	CallHandleStatus_CALL_HANDLE_STATUS_UNSPECIFIED  CallHandleStatus = 0
	CallHandleStatus_CALL_HANDLE_STATUS_ANSWERED     CallHandleStatus = 1
	CallHandleStatus_CALL_HANDLE_STATUS_NOT_ANSWERED CallHandleStatus = 2
)

// Enum value maps for CallHandleStatus.
var (
	CallHandleStatus_name = map[int32]string{
		0: "CALL_HANDLE_STATUS_UNSPECIFIED",
		1: "CALL_HANDLE_STATUS_ANSWERED",
		2: "CALL_HANDLE_STATUS_NOT_ANSWERED",
	}
	CallHandleStatus_value = map[string]int32{
		"CALL_HANDLE_STATUS_UNSPECIFIED":  0,
		"CALL_HANDLE_STATUS_ANSWERED":     1,
		"CALL_HANDLE_STATUS_NOT_ANSWERED": 2,
	}
)

func (x CallHandleStatus) Enum() *CallHandleStatus {
	p := new(CallHandleStatus)
	*p = x
	return p
}

func (x CallHandleStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CallHandleStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_call_enums_proto_enumTypes[1].Descriptor()
}

func (CallHandleStatus) Type() protoreflect.EnumType {
	return &file_api_cx_call_enums_proto_enumTypes[1]
}

func (x CallHandleStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CallHandleStatus.Descriptor instead.
func (CallHandleStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_call_enums_proto_rawDescGZIP(), []int{1}
}

// AgentCallStatus describing call status for agent entity
type AgentCallStatus int32

const (
	AgentCallStatus_AGENT_CALL_STATUS_UNSPECIFIED AgentCallStatus = 0
	// call is answered by an agent
	AgentCallStatus_AGENT_CALL_STATUS_ANSWERED AgentCallStatus = 1
	// call is not answered by an agent
	AgentCallStatus_AGENT_CALL_STATUS_NOT_ANSWERED AgentCallStatus = 2
	AgentCallStatus_AGENT_CALL_STATUS_NOT_DIALED   AgentCallStatus = 3
	// user did not responded
	AgentCallStatus_AGENT_CALL_STATUS_USER_DISCONNECTED AgentCallStatus = 4
)

// Enum value maps for AgentCallStatus.
var (
	AgentCallStatus_name = map[int32]string{
		0: "AGENT_CALL_STATUS_UNSPECIFIED",
		1: "AGENT_CALL_STATUS_ANSWERED",
		2: "AGENT_CALL_STATUS_NOT_ANSWERED",
		3: "AGENT_CALL_STATUS_NOT_DIALED",
		4: "AGENT_CALL_STATUS_USER_DISCONNECTED",
	}
	AgentCallStatus_value = map[string]int32{
		"AGENT_CALL_STATUS_UNSPECIFIED":       0,
		"AGENT_CALL_STATUS_ANSWERED":          1,
		"AGENT_CALL_STATUS_NOT_ANSWERED":      2,
		"AGENT_CALL_STATUS_NOT_DIALED":        3,
		"AGENT_CALL_STATUS_USER_DISCONNECTED": 4,
	}
)

func (x AgentCallStatus) Enum() *AgentCallStatus {
	p := new(AgentCallStatus)
	*p = x
	return p
}

func (x AgentCallStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AgentCallStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_call_enums_proto_enumTypes[2].Descriptor()
}

func (AgentCallStatus) Type() protoreflect.EnumType {
	return &file_api_cx_call_enums_proto_enumTypes[2]
}

func (x AgentCallStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AgentCallStatus.Descriptor instead.
func (AgentCallStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_call_enums_proto_rawDescGZIP(), []int{2}
}

// CustomerStatus describing call status for end customer
type CustomerStatus int32

const (
	CustomerStatus_CUSTOMER_STATUS_UNSPECIFIED CustomerStatus = 0
	// invalid user number
	CustomerStatus_CUSTOMER_STATUS_INVALID_NUMBER CustomerStatus = 1
	CustomerStatus_CUSTOMER_STATUS_CONGESTION     CustomerStatus = 2
	// user answered the call
	CustomerStatus_CUSTOMER_STATUS_ANSWERED CustomerStatus = 3
	// user did not answered the call
	CustomerStatus_CUSTOMER_STATUS_NOT_ANSWERED          CustomerStatus = 4
	CustomerStatus_CUSTOMER_STATUS_NO_ROUTE_DESTINATION  CustomerStatus = 5
	CustomerStatus_CUSTOMER_STATUS_DIALING               CustomerStatus = 6
	CustomerStatus_CUSTOMER_STATUS_NO_RESPONSE           CustomerStatus = 7
	CustomerStatus_CUSTOMER_STATUS_ISD_DISABLED          CustomerStatus = 8
	CustomerStatus_CUSTOMER_STATUS_RING                  CustomerStatus = 9
	CustomerStatus_CUSTOMER_STATUS_EXCEPTION             CustomerStatus = 10
	CustomerStatus_CUSTOMER_STATUS_SUBSCRIBER_ABSENT     CustomerStatus = 11
	CustomerStatus_CUSTOMER_STATUS_INVALID_NUMBER_FORMAT CustomerStatus = 12
	CustomerStatus_CUSTOMER_STATUS_BUSY                  CustomerStatus = 13
)

// Enum value maps for CustomerStatus.
var (
	CustomerStatus_name = map[int32]string{
		0:  "CUSTOMER_STATUS_UNSPECIFIED",
		1:  "CUSTOMER_STATUS_INVALID_NUMBER",
		2:  "CUSTOMER_STATUS_CONGESTION",
		3:  "CUSTOMER_STATUS_ANSWERED",
		4:  "CUSTOMER_STATUS_NOT_ANSWERED",
		5:  "CUSTOMER_STATUS_NO_ROUTE_DESTINATION",
		6:  "CUSTOMER_STATUS_DIALING",
		7:  "CUSTOMER_STATUS_NO_RESPONSE",
		8:  "CUSTOMER_STATUS_ISD_DISABLED",
		9:  "CUSTOMER_STATUS_RING",
		10: "CUSTOMER_STATUS_EXCEPTION",
		11: "CUSTOMER_STATUS_SUBSCRIBER_ABSENT",
		12: "CUSTOMER_STATUS_INVALID_NUMBER_FORMAT",
		13: "CUSTOMER_STATUS_BUSY",
	}
	CustomerStatus_value = map[string]int32{
		"CUSTOMER_STATUS_UNSPECIFIED":           0,
		"CUSTOMER_STATUS_INVALID_NUMBER":        1,
		"CUSTOMER_STATUS_CONGESTION":            2,
		"CUSTOMER_STATUS_ANSWERED":              3,
		"CUSTOMER_STATUS_NOT_ANSWERED":          4,
		"CUSTOMER_STATUS_NO_ROUTE_DESTINATION":  5,
		"CUSTOMER_STATUS_DIALING":               6,
		"CUSTOMER_STATUS_NO_RESPONSE":           7,
		"CUSTOMER_STATUS_ISD_DISABLED":          8,
		"CUSTOMER_STATUS_RING":                  9,
		"CUSTOMER_STATUS_EXCEPTION":             10,
		"CUSTOMER_STATUS_SUBSCRIBER_ABSENT":     11,
		"CUSTOMER_STATUS_INVALID_NUMBER_FORMAT": 12,
		"CUSTOMER_STATUS_BUSY":                  13,
	}
)

func (x CustomerStatus) Enum() *CustomerStatus {
	p := new(CustomerStatus)
	*p = x
	return p
}

func (x CustomerStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomerStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_call_enums_proto_enumTypes[3].Descriptor()
}

func (CustomerStatus) Type() protoreflect.EnumType {
	return &file_api_cx_call_enums_proto_enumTypes[3]
}

func (x CustomerStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomerStatus.Descriptor instead.
func (CustomerStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_call_enums_proto_rawDescGZIP(), []int{3}
}

// HangUpBy describing which side (agent / customer) ended the call
type HangUpBy int32

const (
	HangUpBy_HANG_UP_BY_UNSPECIFIED   HangUpBy = 0
	HangUpBy_HANG_UP_BY_USER_HANGUP   HangUpBy = 1
	HangUpBy_HANG_UP_BY_AGENT_HANGUP  HangUpBy = 2
	HangUpBy_HANG_UP_BY_SYSTEM_HANGUP HangUpBy = 3
)

// Enum value maps for HangUpBy.
var (
	HangUpBy_name = map[int32]string{
		0: "HANG_UP_BY_UNSPECIFIED",
		1: "HANG_UP_BY_USER_HANGUP",
		2: "HANG_UP_BY_AGENT_HANGUP",
		3: "HANG_UP_BY_SYSTEM_HANGUP",
	}
	HangUpBy_value = map[string]int32{
		"HANG_UP_BY_UNSPECIFIED":   0,
		"HANG_UP_BY_USER_HANGUP":   1,
		"HANG_UP_BY_AGENT_HANGUP":  2,
		"HANG_UP_BY_SYSTEM_HANGUP": 3,
	}
)

func (x HangUpBy) Enum() *HangUpBy {
	p := new(HangUpBy)
	*p = x
	return p
}

func (x HangUpBy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HangUpBy) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_call_enums_proto_enumTypes[4].Descriptor()
}

func (HangUpBy) Type() protoreflect.EnumType {
	return &file_api_cx_call_enums_proto_enumTypes[4]
}

func (x HangUpBy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HangUpBy.Descriptor instead.
func (HangUpBy) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_call_enums_proto_rawDescGZIP(), []int{4}
}

// identifies how ticket was attached to call
type TicketAttachmentType int32

const (
	// unspecified
	TicketAttachmentType_TICKET_ATTACHMENT_TYPE_UNSPECIFIED TicketAttachmentType = 0
	// new ticket was created for call
	TicketAttachmentType_TICKET_ATTACHMENT_TYPE_TICKET_NEW_CREATED TicketAttachmentType = 1
	// existing ticket was appended to call
	TicketAttachmentType_TICKET_ATTACHMENT_TYPE_TICKET_EXISTING_APPENDED TicketAttachmentType = 2
)

// Enum value maps for TicketAttachmentType.
var (
	TicketAttachmentType_name = map[int32]string{
		0: "TICKET_ATTACHMENT_TYPE_UNSPECIFIED",
		1: "TICKET_ATTACHMENT_TYPE_TICKET_NEW_CREATED",
		2: "TICKET_ATTACHMENT_TYPE_TICKET_EXISTING_APPENDED",
	}
	TicketAttachmentType_value = map[string]int32{
		"TICKET_ATTACHMENT_TYPE_UNSPECIFIED":              0,
		"TICKET_ATTACHMENT_TYPE_TICKET_NEW_CREATED":       1,
		"TICKET_ATTACHMENT_TYPE_TICKET_EXISTING_APPENDED": 2,
	}
)

func (x TicketAttachmentType) Enum() *TicketAttachmentType {
	p := new(TicketAttachmentType)
	*p = x
	return p
}

func (x TicketAttachmentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TicketAttachmentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_call_enums_proto_enumTypes[5].Descriptor()
}

func (TicketAttachmentType) Type() protoreflect.EnumType {
	return &file_api_cx_call_enums_proto_enumTypes[5]
}

func (x TicketAttachmentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TicketAttachmentType.Descriptor instead.
func (TicketAttachmentType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_call_enums_proto_rawDescGZIP(), []int{5}
}

// identifies call stage in progress or ended
type CallStage int32

const (
	// unspecified
	CallStage_CALL_STAGE_UNSPECIFIED CallStage = 0
	// initiated, call in queue but agent not assigned
	CallStage_CALL_STAGE_INITIATED CallStage = 1
	// agent assigned and call in progress
	CallStage_CALL_STAGE_AGENT_ASSIGNED CallStage = 2
	// ended
	CallStage_CALL_STAGE_ENDED CallStage = 3
)

// Enum value maps for CallStage.
var (
	CallStage_name = map[int32]string{
		0: "CALL_STAGE_UNSPECIFIED",
		1: "CALL_STAGE_INITIATED",
		2: "CALL_STAGE_AGENT_ASSIGNED",
		3: "CALL_STAGE_ENDED",
	}
	CallStage_value = map[string]int32{
		"CALL_STAGE_UNSPECIFIED":    0,
		"CALL_STAGE_INITIATED":      1,
		"CALL_STAGE_AGENT_ASSIGNED": 2,
		"CALL_STAGE_ENDED":          3,
	}
)

func (x CallStage) Enum() *CallStage {
	p := new(CallStage)
	*p = x
	return p
}

func (x CallStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CallStage) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_call_enums_proto_enumTypes[6].Descriptor()
}

func (CallStage) Type() protoreflect.EnumType {
	return &file_api_cx_call_enums_proto_enumTypes[6]
}

func (x CallStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CallStage.Descriptor instead.
func (CallStage) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_call_enums_proto_rawDescGZIP(), []int{6}
}

// Field mask for attributes which are controlled by human intervention
// this mask does not contains field where ozonetel provides us the callback
type CallDetailsFieldMask int32

const (
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_UNSPECIFIED            CallDetailsFieldMask = 0
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_FRESHDESK_TICKET_ID    CallDetailsFieldMask = 1
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_PRODUCT_CATEGORY       CallDetailsFieldMask = 2
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CALL_DROP_BY_USER      CallDetailsFieldMask = 3
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_TICKET_ATTACHMENT_TYPE CallDetailsFieldMask = 4
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CALL_START_TIME        CallDetailsFieldMask = 5
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CALL_END_TIME          CallDetailsFieldMask = 6
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CALL_DURATION          CallDetailsFieldMask = 7
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CALL_RECORDING_LINK    CallDetailsFieldMask = 8
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CALL_DISPOSITION       CallDetailsFieldMask = 9
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_FALLBACK_RULE          CallDetailsFieldMask = 10
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CALL_STATUS            CallDetailsFieldMask = 11
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_AGENT_STATUS           CallDetailsFieldMask = 12
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CUSTOMER_STATUS        CallDetailsFieldMask = 13
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_DIAL_STATUS            CallDetailsFieldMask = 14
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_HANG_UP_BY             CallDetailsFieldMask = 15
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_PHONE_NAME             CallDetailsFieldMask = 16
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_AGENT_ID               CallDetailsFieldMask = 17
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_AGENT_PHONE_NUMBER     CallDetailsFieldMask = 18
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CALL_STAGE             CallDetailsFieldMask = 19
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_EVENT_TYPE             CallDetailsFieldMask = 20
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CALL_DETAILS_META      CallDetailsFieldMask = 21
	CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_ACTOR_ID               CallDetailsFieldMask = 22
)

// Enum value maps for CallDetailsFieldMask.
var (
	CallDetailsFieldMask_name = map[int32]string{
		0:  "CALL_DETAILS_FIELD_MASK_UNSPECIFIED",
		1:  "CALL_DETAILS_FIELD_MASK_FRESHDESK_TICKET_ID",
		2:  "CALL_DETAILS_FIELD_MASK_PRODUCT_CATEGORY",
		3:  "CALL_DETAILS_FIELD_MASK_CALL_DROP_BY_USER",
		4:  "CALL_DETAILS_FIELD_MASK_TICKET_ATTACHMENT_TYPE",
		5:  "CALL_DETAILS_FIELD_MASK_CALL_START_TIME",
		6:  "CALL_DETAILS_FIELD_MASK_CALL_END_TIME",
		7:  "CALL_DETAILS_FIELD_MASK_CALL_DURATION",
		8:  "CALL_DETAILS_FIELD_MASK_CALL_RECORDING_LINK",
		9:  "CALL_DETAILS_FIELD_MASK_CALL_DISPOSITION",
		10: "CALL_DETAILS_FIELD_MASK_FALLBACK_RULE",
		11: "CALL_DETAILS_FIELD_MASK_CALL_STATUS",
		12: "CALL_DETAILS_FIELD_MASK_AGENT_STATUS",
		13: "CALL_DETAILS_FIELD_MASK_CUSTOMER_STATUS",
		14: "CALL_DETAILS_FIELD_MASK_DIAL_STATUS",
		15: "CALL_DETAILS_FIELD_MASK_HANG_UP_BY",
		16: "CALL_DETAILS_FIELD_MASK_PHONE_NAME",
		17: "CALL_DETAILS_FIELD_MASK_AGENT_ID",
		18: "CALL_DETAILS_FIELD_MASK_AGENT_PHONE_NUMBER",
		19: "CALL_DETAILS_FIELD_MASK_CALL_STAGE",
		20: "CALL_DETAILS_FIELD_MASK_EVENT_TYPE",
		21: "CALL_DETAILS_FIELD_MASK_CALL_DETAILS_META",
		22: "CALL_DETAILS_FIELD_MASK_ACTOR_ID",
	}
	CallDetailsFieldMask_value = map[string]int32{
		"CALL_DETAILS_FIELD_MASK_UNSPECIFIED":            0,
		"CALL_DETAILS_FIELD_MASK_FRESHDESK_TICKET_ID":    1,
		"CALL_DETAILS_FIELD_MASK_PRODUCT_CATEGORY":       2,
		"CALL_DETAILS_FIELD_MASK_CALL_DROP_BY_USER":      3,
		"CALL_DETAILS_FIELD_MASK_TICKET_ATTACHMENT_TYPE": 4,
		"CALL_DETAILS_FIELD_MASK_CALL_START_TIME":        5,
		"CALL_DETAILS_FIELD_MASK_CALL_END_TIME":          6,
		"CALL_DETAILS_FIELD_MASK_CALL_DURATION":          7,
		"CALL_DETAILS_FIELD_MASK_CALL_RECORDING_LINK":    8,
		"CALL_DETAILS_FIELD_MASK_CALL_DISPOSITION":       9,
		"CALL_DETAILS_FIELD_MASK_FALLBACK_RULE":          10,
		"CALL_DETAILS_FIELD_MASK_CALL_STATUS":            11,
		"CALL_DETAILS_FIELD_MASK_AGENT_STATUS":           12,
		"CALL_DETAILS_FIELD_MASK_CUSTOMER_STATUS":        13,
		"CALL_DETAILS_FIELD_MASK_DIAL_STATUS":            14,
		"CALL_DETAILS_FIELD_MASK_HANG_UP_BY":             15,
		"CALL_DETAILS_FIELD_MASK_PHONE_NAME":             16,
		"CALL_DETAILS_FIELD_MASK_AGENT_ID":               17,
		"CALL_DETAILS_FIELD_MASK_AGENT_PHONE_NUMBER":     18,
		"CALL_DETAILS_FIELD_MASK_CALL_STAGE":             19,
		"CALL_DETAILS_FIELD_MASK_EVENT_TYPE":             20,
		"CALL_DETAILS_FIELD_MASK_CALL_DETAILS_META":      21,
		"CALL_DETAILS_FIELD_MASK_ACTOR_ID":               22,
	}
)

func (x CallDetailsFieldMask) Enum() *CallDetailsFieldMask {
	p := new(CallDetailsFieldMask)
	*p = x
	return p
}

func (x CallDetailsFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CallDetailsFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_call_enums_proto_enumTypes[7].Descriptor()
}

func (CallDetailsFieldMask) Type() protoreflect.EnumType {
	return &file_api_cx_call_enums_proto_enumTypes[7]
}

func (x CallDetailsFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CallDetailsFieldMask.Descriptor instead.
func (CallDetailsFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_call_enums_proto_rawDescGZIP(), []int{7}
}

// specifies failures which can happen in append ticket to call flow
type AppendExistingTicketToCallFailureType int32

const (
	// unspecified
	AppendExistingTicketToCallFailureType_APPEND_EXISTING_TICKET_TO_CALL_FAILURE_TYPE_UNSPECIFIED AppendExistingTicketToCallFailureType = 0
	// update ticket stage failed
	AppendExistingTicketToCallFailureType_APPEND_EXISTING_TICKET_TO_CALL_FAILURE_TYPE_UPDATE_TICKET_FAILED AppendExistingTicketToCallFailureType = 1
	// add private note failed
	AppendExistingTicketToCallFailureType_APPEND_EXISTING_TICKET_TO_CALL_FAILURE_TYPE_ADD_PRIVATE_NOTE_IN_TICKET_FAILED AppendExistingTicketToCallFailureType = 2
	// update in db failed
	AppendExistingTicketToCallFailureType_APPEND_EXISTING_TICKET_TO_CALL_FAILURE_TYPE_DB_UPDATE_FAILED AppendExistingTicketToCallFailureType = 3
)

// Enum value maps for AppendExistingTicketToCallFailureType.
var (
	AppendExistingTicketToCallFailureType_name = map[int32]string{
		0: "APPEND_EXISTING_TICKET_TO_CALL_FAILURE_TYPE_UNSPECIFIED",
		1: "APPEND_EXISTING_TICKET_TO_CALL_FAILURE_TYPE_UPDATE_TICKET_FAILED",
		2: "APPEND_EXISTING_TICKET_TO_CALL_FAILURE_TYPE_ADD_PRIVATE_NOTE_IN_TICKET_FAILED",
		3: "APPEND_EXISTING_TICKET_TO_CALL_FAILURE_TYPE_DB_UPDATE_FAILED",
	}
	AppendExistingTicketToCallFailureType_value = map[string]int32{
		"APPEND_EXISTING_TICKET_TO_CALL_FAILURE_TYPE_UNSPECIFIED":                       0,
		"APPEND_EXISTING_TICKET_TO_CALL_FAILURE_TYPE_UPDATE_TICKET_FAILED":              1,
		"APPEND_EXISTING_TICKET_TO_CALL_FAILURE_TYPE_ADD_PRIVATE_NOTE_IN_TICKET_FAILED": 2,
		"APPEND_EXISTING_TICKET_TO_CALL_FAILURE_TYPE_DB_UPDATE_FAILED":                  3,
	}
)

func (x AppendExistingTicketToCallFailureType) Enum() *AppendExistingTicketToCallFailureType {
	p := new(AppendExistingTicketToCallFailureType)
	*p = x
	return p
}

func (x AppendExistingTicketToCallFailureType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppendExistingTicketToCallFailureType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_call_enums_proto_enumTypes[8].Descriptor()
}

func (AppendExistingTicketToCallFailureType) Type() protoreflect.EnumType {
	return &file_api_cx_call_enums_proto_enumTypes[8]
}

func (x AppendExistingTicketToCallFailureType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppendExistingTicketToCallFailureType.Descriptor instead.
func (AppendExistingTicketToCallFailureType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_call_enums_proto_rawDescGZIP(), []int{8}
}

// specifies failures which can happen in create ticket for call flow
type CreateTicketForCallFailureType int32

const (
	// unspecified
	CreateTicketForCallFailureType_CREATE_TICKET_FOR_CALL_FAILURE_TYPE_UNSPECIFIED CreateTicketForCallFailureType = 0
	// get agent step failed
	CreateTicketForCallFailureType_CREATE_TICKET_FOR_CALL_FAILURE_TYPE_GET_AGENT_FAILED CreateTicketForCallFailureType = 1
	// update in db step failed
	CreateTicketForCallFailureType_CREATE_TICKET_FOR_CALL_FAILURE_TYPE_UPDATE_IN_DB_FAILED CreateTicketForCallFailureType = 2
	// create ticket failed
	CreateTicketForCallFailureType_CREATE_TICKET_FOR_CALL_FAILURE_TYPE_CREATE_TICKET_FAILED CreateTicketForCallFailureType = 3
)

// Enum value maps for CreateTicketForCallFailureType.
var (
	CreateTicketForCallFailureType_name = map[int32]string{
		0: "CREATE_TICKET_FOR_CALL_FAILURE_TYPE_UNSPECIFIED",
		1: "CREATE_TICKET_FOR_CALL_FAILURE_TYPE_GET_AGENT_FAILED",
		2: "CREATE_TICKET_FOR_CALL_FAILURE_TYPE_UPDATE_IN_DB_FAILED",
		3: "CREATE_TICKET_FOR_CALL_FAILURE_TYPE_CREATE_TICKET_FAILED",
	}
	CreateTicketForCallFailureType_value = map[string]int32{
		"CREATE_TICKET_FOR_CALL_FAILURE_TYPE_UNSPECIFIED":          0,
		"CREATE_TICKET_FOR_CALL_FAILURE_TYPE_GET_AGENT_FAILED":     1,
		"CREATE_TICKET_FOR_CALL_FAILURE_TYPE_UPDATE_IN_DB_FAILED":  2,
		"CREATE_TICKET_FOR_CALL_FAILURE_TYPE_CREATE_TICKET_FAILED": 3,
	}
)

func (x CreateTicketForCallFailureType) Enum() *CreateTicketForCallFailureType {
	p := new(CreateTicketForCallFailureType)
	*p = x
	return p
}

func (x CreateTicketForCallFailureType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateTicketForCallFailureType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_call_enums_proto_enumTypes[9].Descriptor()
}

func (CreateTicketForCallFailureType) Type() protoreflect.EnumType {
	return &file_api_cx_call_enums_proto_enumTypes[9]
}

func (x CreateTicketForCallFailureType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateTicketForCallFailureType.Descriptor instead.
func (CreateTicketForCallFailureType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_call_enums_proto_rawDescGZIP(), []int{9}
}

// specifies the source from which ticket details were fetched for users
type PastTicketsServingSourceForCall int32

const (
	// unspecified
	PastTicketsServingSourceForCall_PAST_TICKETS_SERVING_SOURCE_FOR_CALL_UNSPECIFIED PastTicketsServingSourceForCall = 0
	// from database
	PastTicketsServingSourceForCall_PAST_TICKETS_SERVING_SOURCE_FOR_CALL_SUPPORT_TICKETS_DB PastTicketsServingSourceForCall = 1
	// from freshdesk
	PastTicketsServingSourceForCall_PAST_TICKETS_SERVING_SOURCE_FOR_CALL_FRESHDESK PastTicketsServingSourceForCall = 2
)

// Enum value maps for PastTicketsServingSourceForCall.
var (
	PastTicketsServingSourceForCall_name = map[int32]string{
		0: "PAST_TICKETS_SERVING_SOURCE_FOR_CALL_UNSPECIFIED",
		1: "PAST_TICKETS_SERVING_SOURCE_FOR_CALL_SUPPORT_TICKETS_DB",
		2: "PAST_TICKETS_SERVING_SOURCE_FOR_CALL_FRESHDESK",
	}
	PastTicketsServingSourceForCall_value = map[string]int32{
		"PAST_TICKETS_SERVING_SOURCE_FOR_CALL_UNSPECIFIED":        0,
		"PAST_TICKETS_SERVING_SOURCE_FOR_CALL_SUPPORT_TICKETS_DB": 1,
		"PAST_TICKETS_SERVING_SOURCE_FOR_CALL_FRESHDESK":          2,
	}
)

func (x PastTicketsServingSourceForCall) Enum() *PastTicketsServingSourceForCall {
	p := new(PastTicketsServingSourceForCall)
	*p = x
	return p
}

func (x PastTicketsServingSourceForCall) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PastTicketsServingSourceForCall) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_call_enums_proto_enumTypes[10].Descriptor()
}

func (PastTicketsServingSourceForCall) Type() protoreflect.EnumType {
	return &file_api_cx_call_enums_proto_enumTypes[10]
}

func (x PastTicketsServingSourceForCall) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PastTicketsServingSourceForCall.Descriptor instead.
func (PastTicketsServingSourceForCall) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_call_enums_proto_rawDescGZIP(), []int{10}
}

type AttachToCallTicketFetchStatus int32

const (
	AttachToCallTicketFetchStatus_ATTACH_TO_CALL_TICKET_FETCH_STATUS_UNSPECIFIED                                       AttachToCallTicketFetchStatus = 0
	AttachToCallTicketFetchStatus_ATTACH_TO_CALL_TICKET_FETCH_STATUS_TICKET_NOT_FOUND_IN_DB                            AttachToCallTicketFetchStatus = 1
	AttachToCallTicketFetchStatus_ATTACH_TO_CALL_TICKET_FETCH_STATUS_TICKET_FETCH_ERROR                                AttachToCallTicketFetchStatus = 2
	AttachToCallTicketFetchStatus_ATTACH_TO_CALL_TICKET_FETCH_STATUS_TICKET_FOUND_IN_DB_WITH_CLOSED_OR_RESOLVED_STATUS AttachToCallTicketFetchStatus = 3
	AttachToCallTicketFetchStatus_ATTACH_TO_CALL_TICKET_FETCH_STATUS_TICKET_FOUND_IN_DB_WITH_UNRELATED_STATUS          AttachToCallTicketFetchStatus = 4
)

// Enum value maps for AttachToCallTicketFetchStatus.
var (
	AttachToCallTicketFetchStatus_name = map[int32]string{
		0: "ATTACH_TO_CALL_TICKET_FETCH_STATUS_UNSPECIFIED",
		1: "ATTACH_TO_CALL_TICKET_FETCH_STATUS_TICKET_NOT_FOUND_IN_DB",
		2: "ATTACH_TO_CALL_TICKET_FETCH_STATUS_TICKET_FETCH_ERROR",
		3: "ATTACH_TO_CALL_TICKET_FETCH_STATUS_TICKET_FOUND_IN_DB_WITH_CLOSED_OR_RESOLVED_STATUS",
		4: "ATTACH_TO_CALL_TICKET_FETCH_STATUS_TICKET_FOUND_IN_DB_WITH_UNRELATED_STATUS",
	}
	AttachToCallTicketFetchStatus_value = map[string]int32{
		"ATTACH_TO_CALL_TICKET_FETCH_STATUS_UNSPECIFIED":                                       0,
		"ATTACH_TO_CALL_TICKET_FETCH_STATUS_TICKET_NOT_FOUND_IN_DB":                            1,
		"ATTACH_TO_CALL_TICKET_FETCH_STATUS_TICKET_FETCH_ERROR":                                2,
		"ATTACH_TO_CALL_TICKET_FETCH_STATUS_TICKET_FOUND_IN_DB_WITH_CLOSED_OR_RESOLVED_STATUS": 3,
		"ATTACH_TO_CALL_TICKET_FETCH_STATUS_TICKET_FOUND_IN_DB_WITH_UNRELATED_STATUS":          4,
	}
)

func (x AttachToCallTicketFetchStatus) Enum() *AttachToCallTicketFetchStatus {
	p := new(AttachToCallTicketFetchStatus)
	*p = x
	return p
}

func (x AttachToCallTicketFetchStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AttachToCallTicketFetchStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_call_enums_proto_enumTypes[11].Descriptor()
}

func (AttachToCallTicketFetchStatus) Type() protoreflect.EnumType {
	return &file_api_cx_call_enums_proto_enumTypes[11]
}

func (x AttachToCallTicketFetchStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AttachToCallTicketFetchStatus.Descriptor instead.
func (AttachToCallTicketFetchStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_call_enums_proto_rawDescGZIP(), []int{11}
}

// CallDetailsMetaType specifies what kind of meta information we are storing
type CallDetailsMetaType int32

const (
	// unspecified
	CallDetailsMetaType_CALL_DETAILS_META_TYPE_UNSPECIFIED CallDetailsMetaType = 0
	// denotes reason why new ticket was created for a call
	CallDetailsMetaType_CALL_DETAILS_META_TYPE_NEW_TICKET_CREATION_REASON CallDetailsMetaType = 1
)

// Enum value maps for CallDetailsMetaType.
var (
	CallDetailsMetaType_name = map[int32]string{
		0: "CALL_DETAILS_META_TYPE_UNSPECIFIED",
		1: "CALL_DETAILS_META_TYPE_NEW_TICKET_CREATION_REASON",
	}
	CallDetailsMetaType_value = map[string]int32{
		"CALL_DETAILS_META_TYPE_UNSPECIFIED":                0,
		"CALL_DETAILS_META_TYPE_NEW_TICKET_CREATION_REASON": 1,
	}
)

func (x CallDetailsMetaType) Enum() *CallDetailsMetaType {
	p := new(CallDetailsMetaType)
	*p = x
	return p
}

func (x CallDetailsMetaType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CallDetailsMetaType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_call_enums_proto_enumTypes[12].Descriptor()
}

func (CallDetailsMetaType) Type() protoreflect.EnumType {
	return &file_api_cx_call_enums_proto_enumTypes[12]
}

func (x CallDetailsMetaType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CallDetailsMetaType.Descriptor instead.
func (CallDetailsMetaType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_call_enums_proto_rawDescGZIP(), []int{12}
}

var File_api_cx_call_enums_proto protoreflect.FileDescriptor

var file_api_cx_call_enums_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x63, 0x78, 0x2e, 0x63, 0x61,
	0x6c, 0x6c, 0x2a, 0xd9, 0x01, 0x0a, 0x08, 0x43, 0x61, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x19, 0x0a, 0x15, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x41,
	0x4c, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53,
	0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x49, 0x4e, 0x42, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10,
	0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c,
	0x10, 0x04, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x50, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x41, 0x4c,
	0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x06, 0x12, 0x11, 0x0a,
	0x0d, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x56, 0x52, 0x10, 0x07,
	0x12, 0x12, 0x0a, 0x0e, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x48,
	0x41, 0x54, 0x10, 0x08, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x50, 0x52, 0x45, 0x44, 0x49, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x09, 0x2a, 0x7c,
	0x0a, 0x10, 0x43, 0x61, 0x6c, 0x6c, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x48,
	0x41, 0x4e, 0x44, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x4e, 0x53,
	0x57, 0x45, 0x52, 0x45, 0x44, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x41, 0x4c, 0x4c, 0x5f,
	0x48, 0x41, 0x4e, 0x44, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x41, 0x4e, 0x53, 0x57, 0x45, 0x52, 0x45, 0x44, 0x10, 0x02, 0x2a, 0xc3, 0x01, 0x0a,
	0x0f, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x21, 0x0a, 0x1d, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x4c,
	0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x4e, 0x53, 0x57, 0x45, 0x52, 0x45,
	0x44, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x4c,
	0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4e, 0x53,
	0x57, 0x45, 0x52, 0x45, 0x44, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x47, 0x45, 0x4e, 0x54,
	0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x44, 0x49, 0x41, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x47, 0x45,
	0x4e, 0x54, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x53, 0x45, 0x52, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44,
	0x10, 0x04, 0x2a, 0xe4, 0x03, 0x0a, 0x0e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45,
	0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f,
	0x4e, 0x47, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x4e,
	0x53, 0x57, 0x45, 0x52, 0x45, 0x44, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x55, 0x53, 0x54,
	0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x41, 0x4e, 0x53, 0x57, 0x45, 0x52, 0x45, 0x44, 0x10, 0x04, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f,
	0x5f, 0x52, 0x4f, 0x55, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x53, 0x54, 0x49, 0x4e, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x05, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x49, 0x4e, 0x47, 0x10,
	0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45,
	0x10, 0x07, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x53, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c,
	0x45, 0x44, 0x10, 0x08, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x09, 0x12, 0x1d,
	0x0a, 0x19, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0a, 0x12, 0x25, 0x0a,
	0x21, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x42, 0x45, 0x52, 0x5f, 0x41, 0x42, 0x53, 0x45,
	0x4e, 0x54, 0x10, 0x0b, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x10, 0x0c, 0x12,
	0x18, 0x0a, 0x14, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x42, 0x55, 0x53, 0x59, 0x10, 0x0d, 0x2a, 0x7d, 0x0a, 0x08, 0x48, 0x61, 0x6e,
	0x67, 0x55, 0x70, 0x42, 0x79, 0x12, 0x1a, 0x0a, 0x16, 0x48, 0x41, 0x4e, 0x47, 0x5f, 0x55, 0x50,
	0x5f, 0x42, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1a, 0x0a, 0x16, 0x48, 0x41, 0x4e, 0x47, 0x5f, 0x55, 0x50, 0x5f, 0x42, 0x59, 0x5f,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x48, 0x41, 0x4e, 0x47, 0x55, 0x50, 0x10, 0x01, 0x12, 0x1b, 0x0a,
	0x17, 0x48, 0x41, 0x4e, 0x47, 0x5f, 0x55, 0x50, 0x5f, 0x42, 0x59, 0x5f, 0x41, 0x47, 0x45, 0x4e,
	0x54, 0x5f, 0x48, 0x41, 0x4e, 0x47, 0x55, 0x50, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x48, 0x41,
	0x4e, 0x47, 0x5f, 0x55, 0x50, 0x5f, 0x42, 0x59, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f,
	0x48, 0x41, 0x4e, 0x47, 0x55, 0x50, 0x10, 0x03, 0x2a, 0xa2, 0x01, 0x0a, 0x14, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x41, 0x54, 0x54, 0x41,
	0x43, 0x48, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2d, 0x0a, 0x29, 0x54, 0x49, 0x43,
	0x4b, 0x45, 0x54, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x48, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x33, 0x0a, 0x2f, 0x54, 0x49, 0x43, 0x4b,
	0x45, 0x54, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x48, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49,
	0x4e, 0x47, 0x5f, 0x41, 0x50, 0x50, 0x45, 0x4e, 0x44, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x76, 0x0a,
	0x09, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x41,
	0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x53,
	0x54, 0x41, 0x47, 0x45, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01,
	0x12, 0x1d, 0x0a, 0x19, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x41,
	0x47, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x53, 0x53, 0x49, 0x47, 0x4e, 0x45, 0x44, 0x10, 0x02, 0x12,
	0x14, 0x0a, 0x10, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x45, 0x4e,
	0x44, 0x45, 0x44, 0x10, 0x03, 0x2a, 0x82, 0x08, 0x0a, 0x14, 0x43, 0x61, 0x6c, 0x6c, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x27,
	0x0a, 0x23, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2f, 0x0a, 0x2b, 0x43, 0x41, 0x4c, 0x4c, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x44, 0x45, 0x53, 0x4b, 0x5f, 0x54, 0x49, 0x43,
	0x4b, 0x45, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x41, 0x4c, 0x4c,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x10, 0x02, 0x12, 0x2d, 0x0a, 0x29, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x42, 0x59, 0x5f, 0x55,
	0x53, 0x45, 0x52, 0x10, 0x03, 0x12, 0x32, 0x0a, 0x2e, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x48, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x04, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x41, 0x4c,
	0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f,
	0x54, 0x49, 0x4d, 0x45, 0x10, 0x05, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x45, 0x4e, 0x44, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10,
	0x06, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x4c,
	0x4c, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x07, 0x12, 0x2f, 0x0a, 0x2b,
	0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x45, 0x43,
	0x4f, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x08, 0x12, 0x2c, 0x0a,
	0x28, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x49,
	0x53, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x29, 0x0a, 0x25, 0x43,
	0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x41, 0x4c, 0x4c, 0x42, 0x41, 0x43, 0x4b, 0x5f,
	0x52, 0x55, 0x4c, 0x45, 0x10, 0x0a, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x0b, 0x12,
	0x28, 0x0a, 0x24, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x0c, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x41, 0x4c,
	0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x10, 0x0d, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x0e, 0x12,
	0x26, 0x0a, 0x22, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x48, 0x41, 0x4e, 0x47, 0x5f,
	0x55, 0x50, 0x5f, 0x42, 0x59, 0x10, 0x0f, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x41, 0x4c, 0x4c, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x10, 0x12,
	0x24, 0x0a, 0x20, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54,
	0x5f, 0x49, 0x44, 0x10, 0x11, 0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d,
	0x42, 0x45, 0x52, 0x10, 0x12, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x10, 0x13, 0x12, 0x26, 0x0a,
	0x22, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x10, 0x14, 0x12, 0x2d, 0x0a, 0x29, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x4d, 0x45,
	0x54, 0x41, 0x10, 0x15, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x16, 0x2a, 0xbf, 0x02, 0x0a, 0x25, 0x41,
	0x70, 0x70, 0x65, 0x6e, 0x64, 0x45, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x54, 0x6f, 0x43, 0x61, 0x6c, 0x6c, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x37, 0x41, 0x50, 0x50, 0x45, 0x4e, 0x44, 0x5f, 0x45,
	0x58, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x54,
	0x4f, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x44, 0x0a, 0x40, 0x41, 0x50, 0x50, 0x45, 0x4e, 0x44, 0x5f, 0x45, 0x58, 0x49, 0x53,
	0x54, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x54, 0x4f, 0x5f, 0x43,
	0x41, 0x4c, 0x4c, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x51, 0x0a, 0x4d, 0x41, 0x50, 0x50, 0x45, 0x4e,
	0x44, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45,
	0x54, 0x5f, 0x54, 0x4f, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41,
	0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45,
	0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x40, 0x0a, 0x3c, 0x41, 0x50,
	0x50, 0x45, 0x4e, 0x44, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x49,
	0x43, 0x4b, 0x45, 0x54, 0x5f, 0x54, 0x4f, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x42, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x2a, 0x8a, 0x02, 0x0a,
	0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x46, 0x6f, 0x72,
	0x43, 0x61, 0x6c, 0x6c, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x33, 0x0a, 0x2f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x38, 0x0a, 0x34, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x54,
	0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x45, 0x54, 0x5f,
	0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x3b,
	0x0a, 0x37, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x5f,
	0x44, 0x42, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x3c, 0x0a, 0x38, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x46, 0x4f, 0x52,
	0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x2a, 0xc8, 0x01, 0x0a, 0x1f, 0x50, 0x61,
	0x73, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x12, 0x34, 0x0a,
	0x30, 0x50, 0x41, 0x53, 0x54, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x53, 0x5f, 0x53, 0x45,
	0x52, 0x56, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x46, 0x4f, 0x52,
	0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x3b, 0x0a, 0x37, 0x50, 0x41, 0x53, 0x54, 0x5f, 0x54, 0x49, 0x43, 0x4b,
	0x45, 0x54, 0x53, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x55, 0x50, 0x50,
	0x4f, 0x52, 0x54, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x53, 0x5f, 0x44, 0x42, 0x10, 0x01,
	0x12, 0x32, 0x0a, 0x2e, 0x50, 0x41, 0x53, 0x54, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x53,
	0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x44, 0x45,
	0x53, 0x4b, 0x10, 0x02, 0x2a, 0xf8, 0x02, 0x0a, 0x1d, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x54,
	0x6f, 0x43, 0x61, 0x6c, 0x6c, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x46, 0x65, 0x74, 0x63, 0x68,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x2e, 0x41, 0x54, 0x54, 0x41, 0x43, 0x48,
	0x5f, 0x54, 0x4f, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f,
	0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x3d, 0x0a, 0x39, 0x41, 0x54,
	0x54, 0x41, 0x43, 0x48, 0x5f, 0x54, 0x4f, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x49, 0x43,
	0x4b, 0x45, 0x54, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x5f, 0x49, 0x4e, 0x5f, 0x44, 0x42, 0x10, 0x01, 0x12, 0x39, 0x0a, 0x35, 0x41, 0x54, 0x54,
	0x41, 0x43, 0x48, 0x5f, 0x54, 0x4f, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x49, 0x43, 0x4b,
	0x45, 0x54, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x45, 0x52, 0x52,
	0x4f, 0x52, 0x10, 0x02, 0x12, 0x58, 0x0a, 0x54, 0x41, 0x54, 0x54, 0x41, 0x43, 0x48, 0x5f, 0x54,
	0x4f, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x46, 0x45,
	0x54, 0x43, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45,
	0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x5f, 0x44, 0x42, 0x5f, 0x57, 0x49,
	0x54, 0x48, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x5f, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x53,
	0x4f, 0x4c, 0x56, 0x45, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x03, 0x12, 0x4f,
	0x0a, 0x4b, 0x41, 0x54, 0x54, 0x41, 0x43, 0x48, 0x5f, 0x54, 0x4f, 0x5f, 0x43, 0x41, 0x4c, 0x4c,
	0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x5f, 0x49, 0x4e, 0x5f, 0x44, 0x42, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x55, 0x4e, 0x52,
	0x45, 0x4c, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x04, 0x2a,
	0x74, 0x0a, 0x13, 0x43, 0x61, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x4d, 0x65,
	0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x35,
	0x0a, 0x31, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x4d,
	0x45, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x54, 0x49, 0x43,
	0x4b, 0x45, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x10, 0x01, 0x42, 0x48, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x5a, 0x22, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_call_enums_proto_rawDescOnce sync.Once
	file_api_cx_call_enums_proto_rawDescData = file_api_cx_call_enums_proto_rawDesc
)

func file_api_cx_call_enums_proto_rawDescGZIP() []byte {
	file_api_cx_call_enums_proto_rawDescOnce.Do(func() {
		file_api_cx_call_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_call_enums_proto_rawDescData)
	})
	return file_api_cx_call_enums_proto_rawDescData
}

var file_api_cx_call_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 13)
var file_api_cx_call_enums_proto_goTypes = []interface{}{
	(CallType)(0),                              // 0: cx.call.CallType
	(CallHandleStatus)(0),                      // 1: cx.call.CallHandleStatus
	(AgentCallStatus)(0),                       // 2: cx.call.AgentCallStatus
	(CustomerStatus)(0),                        // 3: cx.call.CustomerStatus
	(HangUpBy)(0),                              // 4: cx.call.HangUpBy
	(TicketAttachmentType)(0),                  // 5: cx.call.TicketAttachmentType
	(CallStage)(0),                             // 6: cx.call.CallStage
	(CallDetailsFieldMask)(0),                  // 7: cx.call.CallDetailsFieldMask
	(AppendExistingTicketToCallFailureType)(0), // 8: cx.call.AppendExistingTicketToCallFailureType
	(CreateTicketForCallFailureType)(0),        // 9: cx.call.CreateTicketForCallFailureType
	(PastTicketsServingSourceForCall)(0),       // 10: cx.call.PastTicketsServingSourceForCall
	(AttachToCallTicketFetchStatus)(0),         // 11: cx.call.AttachToCallTicketFetchStatus
	(CallDetailsMetaType)(0),                   // 12: cx.call.CallDetailsMetaType
}
var file_api_cx_call_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_cx_call_enums_proto_init() }
func file_api_cx_call_enums_proto_init() {
	if File_api_cx_call_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_call_enums_proto_rawDesc,
			NumEnums:      13,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_cx_call_enums_proto_goTypes,
		DependencyIndexes: file_api_cx_call_enums_proto_depIdxs,
		EnumInfos:         file_api_cx_call_enums_proto_enumTypes,
	}.Build()
	File_api_cx_call_enums_proto = out.File
	file_api_cx_call_enums_proto_rawDesc = nil
	file_api_cx_call_enums_proto_goTypes = nil
	file_api_cx_call_enums_proto_depIdxs = nil
}
