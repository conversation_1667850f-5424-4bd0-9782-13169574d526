<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Community</title>
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@500&display=swap" rel="stylesheet">
    <link rel="stylesheet"
        href="https://cdn.rawgit.com/mfd/********************************/raw/e06a670afcb2b861ed2ac4a1ef752d062ef6b46b/Gilroy.css"
    >
    <!-- common styles -->
    <style>
        * {
            box-sizing: border-box;
        }

        html,
        body {
            padding: 0;
            margin: 0;
            height: 100%;
        }

        .t-ellipsis {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .width-100p {
            width: 100%;
        }
        .height-100p {
            height: 100%;
        }
        .cpointer {
            cursor: pointer;
            -webkit-tap-highlight-color: transparent;
        }
    </style>
    <!-- flex styles -->
    <style>
        .cflex {
            display: flex;
        }
        .cflex-col {
            flex-direction: column;
        }
        /* justify content */
        .cflex-jus-cen {
            justify-content: center;
        }
        .cflex-jus-sp-bt {
            justify-content: space-between;
        }
        .cflex-jus-sta {
            justify-content: flex-start;
        }
        /* align items */
        .cflex-ali-cen {
            align-items: center;
        }
        .clfex-ali-sta {
            align-items: flex-start;
        }
    </style>
    <!-- font styles -->
    <style>
        .font-gilroy {
            font-family: 'Gilroy', sans-serif;
        }
        .font-inter {
            font-family: 'Inter', sans-serif;
        }
        .f-we-500 {
            font-weight: 500;
        }
        .f-we-600 {
            font-weight: 600;
        }
        .f-we-700 {
            font-weight: 700;
        }
        .f-we-bold {
            font-weight: bold;
        }
    </style>
    <!-- colors -->
    <style>
        .clr-charcoal {
            color: #424242;
        }
        .clr-gray1 {
            color: #333333;
        }
        .clr-mid-gray {
            color: #9E9C9F;
        }
        .clr-forest-green {
            color: #00B899;
        }
        .clr-white {
            color: #ffffff;
        }
    </style>
    <!-- login styles -->
    <style>
        #body-container {
            background: #F5F5F5;
        }
        #login-container {
            background: #F5F5F5;
            height: 100%;
            width: 80%;
            max-width: 500px;
            padding: 3em 0;
        }
        #title {
            font-size: 3em;
            margin-bottom: 1em;
        }
        #google-btn {
            border-radius: 10px;
            border: 1.5px solid #424242;
            margin-bottom: 3em;
            padding: 1em 0;
        }
        #google-txt {
            margin-left: 1em;
            padding-top: 2px;
        }
        #subtitle {
            font-size: 1.5em;
            margin-bottom: 1.5em;
        }
        .input-container {
            position: relative;
            margin-bottom: 2em;
            border: none;
            outline: none;
            border-radius: 10px;
            padding: 2em 0;
            background-color: #ffffff;
        }
        .input-container:focus-within, .input-container:hover {
            box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.14),
                        0px 5px 10px rgba(0, 0, 0, 0.04),
                        0px 0px 15px rgba(0, 0, 0, 0.05);
        }
        .input-element {
            position: absolute;
            font-size: 1.25em;
            border: none;
            outline: none;
            padding: 0 1rem;
        }
        .input-label {
            position: absolute;
            left: 0;
            top: 20%;
            padding: 0 1rem;
            pointer-events: none;
            font-size: 0.8em;
        }
        /* placeholder */
        input::-webkit-input-placeholder { font-size: 1em; font-weight: 500; font-family: 'Inter', sans-serif; color: #9E9C9F; }
        input::-moz-placeholder { font-size: 1em; font-weight: 500; font-family: 'Inter', sans-serif; color: #9E9C9F; }
        input:-ms-input-placeholder { font-size: 1em; font-weight: 500; font-family: 'Inter', sans-serif; color: #9E9C9F; }
        input:-moz-placeholder { font-size: 1em; font-weight: 500; font-family: 'Inter', sans-serif; color: #9E9C9F; }
        #forgot-pwd-ctner {
            margin-bottom: 4em;
        }
        #forgot-pwd {
            font-size: 1.25em;
            margin-left: 5px;
        }
        #footer-txt {
            font-size: 1.25em;
        }
        #sign-up-txt {
            margin-left: 5px;
        }
        #login-btn {
            font-size: 1em;
            background-color: #424242;
            padding: 1em 1.5em;
            border-radius: 10px;
        }

        @media screen and (max-width: 570px) {
            #login-container {
                font-size: 12px;
            }
            #footer-txt {
                display: flex;
                flex-direction: column;
            }
            #sign-up-txt {
                margin-left: 0;
                margin-top: 5px;
            }
            #login-btn {
                padding: 0.8em 1em;
            }
        }
    </style>
</head>
<body>
    <div id="body-container" class="cflex cflex-jus-cen width-100p height-100p">
        <div id="login-container" class="cflex cflex-col cflex-jus-sta cflex-ali-cen">
            <div id="title" class="clr-charcoal font-gilroy f-we-500">
                Sign Up
            </div>
            <div id="google-btn" class="cflex cflex-jus-cen width-100p cpointer"
                onclick="singUpViaGoogle()"
            >
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M17.64 9.20443C17.64 8.56625 17.5827 7.95262 17.4764 7.36353H9V10.8449H13.8436C13.635 11.9699 13.0009 12.9231 12.0477 13.5613V15.8194H14.9564C16.6582 14.2526 17.64 11.9453 17.64 9.20443Z" fill="#4285F4"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.99976 18C11.4298 18 13.467 17.1941 14.9561 15.8195L12.0475 13.5613C11.2416 14.1013 10.2107 14.4204 8.99976 14.4204C6.65567 14.4204 4.67158 12.8372 3.96385 10.71H0.957031V13.0418C2.43794 15.9831 5.48158 18 8.99976 18Z" fill="#34A853"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M3.96409 10.7098C3.78409 10.1698 3.68182 9.59301 3.68182 8.99983C3.68182 8.40664 3.78409 7.82983 3.96409 7.28983V4.95801H0.957273C0.347727 6.17301 0 7.54755 0 8.99983C0 10.4521 0.347727 11.8266 0.957273 13.0416L3.96409 10.7098Z" fill="#FBBC05"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.99976 3.57955C10.3211 3.57955 11.5075 4.03364 12.4402 4.92545L15.0216 2.34409C13.4629 0.891818 11.4257 0 8.99976 0C5.48158 0 2.43794 2.01682 0.957031 4.95818L3.96385 7.29C4.67158 5.16273 6.65567 3.57955 8.99976 3.57955Z" fill="#EA4335"/>
                </svg>
                <span id="google-txt" class="clr-gray1 font-gilroy f-we-700">SIGN UP VIA GOOGLE</span>
            </div>
            <div id="subtitle" class="clr-charcoal font-gilroy f-we-600">
                Or sign up via email
            </div>
            <div class="input-container width-100p">
                <input type="email" name="Email Address" id="email-input" class="input-element clr-mid-gray font-gilroy f-we-500 width-100p" 
                    placeholder="Enter your Email Address"
                >
                <label class="input-label clr-mid-gray font-gilroy f-we-600" for="Email Address">
                    EMAIL ADDRESS
                </label>
            </div>
            <div class="input-container width-100p">
                <input type="password" name="Password" id="password-input" class="input-element clr-mid-gray font-gilroy f-we-500 width-100p"
                    placeholder="Enter your Password"
                >
                <label class="input-label clr-mid-gray font-gilroy f-we-600" for="Password">
                    PASSWORD
                </label>
            </div>
            <div id="forgot-pwd-ctner" class="cflex cflex-jus-sta cflex-ali-cen width-100p">
                <input type="checkbox" name="tnc checkbox" id="tnc-checkbox" class="cpointer clr-mid-gray">
                <div id="forgot-pwd" class="clr-charcoal font-inter f-we-500">I agree to the terms and conditions of using Fi</div>
            </div>
            <div id="footer" class="cflex cflex-jus-sp-bt cflex-ali-cen width-100p">
                <div id="footer-txt">
                    <span class="clr-charcoal font-inter f-we-500">Already have an account?</span>
                    <span id="sign-up-txt" class="clr-forest-green font-gilroy f-we-500 cpointer">Login</span>
                </div>
                <div id="login-btn" class="clr-white font-gilroy f-we-700 cpointer"
                    onclick="signUpUser()"
                >
                    SIGN UP
                </div>
            </div>
        </div>
    </div>
    <!-- api token -->
    <script src="../../js/api-token.js"></script>
    <script defer>
        // const apiToken = ''; // uncomment during deployment
        const httpOptions = {
            headers: new Headers({
                Authorization: apiToken
            }),
        };
        const IS_DEPLOYMENT_ENV = false;
        const getHttpOptions = () => {
            if (IS_DEPLOYMENT_ENV) return {};
            return httpOptions;
        };
        const getBaseUrl = () => {
            if (IS_DEPLOYMENT_ENV) return '';
            return 'https://epifi.vanillastaging.com';
        };
    </script>
    <!-- abe12!QW -->
    <!-- sign up api -->
    <script defer>
        const googleSignInLink = 'https://accounts.google.com/o/oauth2/v2/auth/oauthchooseaccount?response_type=code&client_id=************-hjpr3f7e8h6nimlg5uj6og4psqrpems8.apps.googleusercontent.com&redirect_uri=https%3A%2F%2Fepifi.vanillastaging.com%2Fentry%2Fgooglesignin&scope=email%20openid%20profile&state=eyJ0YXJnZXQiOiJkaXNjdXNzaW9ucyIsImNpZCI6Imdvb2dsZXNpZ25pbiIsInRva2VuIjoiSEhKRFhORVQyRFZBMURIRDNaN1lYUjNPM0NRUURDM1MifQ%3D%3D&flowName=GeneralOAuthFlow';
        
        const singUpViaGoogle = () => {
            window.location.href = googleSignInLink;
        }
        const signUpUser = async () => {
            try {
                const email = document.getElementById('email-input').value;
                const password = document.getElementById('password-input').value;
                // tnc check
                const isTncChecked = document.getElementById('tnc-checkbox').checked;
                if (!isTncChecked) return;
                // min length check
                if (email.length <= 3 || password.length <= 3) return;
                const name = 'nikhil1';
                const discoveryText = '';
                const payload = {
                    email,
                    password,
                    name,
                    discoveryText
                };
                const reqBody = {
                    method: 'post',
                    body: JSON.stringify(payload)
                };
                const fetchOptions = {
                    ...reqBody,
                    ...getHttpOptions(),
                };
                const url = `${getBaseUrl()}/api/v2/users/register`;
                console.log('fetchOptions: ', fetchOptions);
                const newUser = await fetch(url, fetchOptions)
                .then(res => res.json());
                console.log('newUser: ', newUser);
            } catch (err) {
                console.error('Error while signing up user: ', err);
            }
        };
    </script>

</body>
</html>