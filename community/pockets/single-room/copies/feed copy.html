<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Room Feed</title>
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.rawgit.com/mfd/********************************/raw/e06a670afcb2b861ed2ac4a1ef752d062ef6b46b/Gilroy.css">
    <style>
        * {
            box-sizing: border-box;
        }
        body {
            padding: 0;
            margin: 0;
        }
        .t-ellipsis {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .line-clamp {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        #room-feed--container {
            display: flex;
            flex-direction: column;
            width: 80%;
            justify-content: start;
            align-items: center;
            margin: auto;
        }
        #feed--no-of-posts {
            font-family: 'Inter', sans-serif;
            font-weight: 500;
            color: #9E9C9F;
            font-size: 20px;
            text-align: center;
        }
        #feed-post--container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }
        .each-post--container {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 150px;
        }
        .user-post--separator {
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
            width: 50px;
            /* min-width: 60px; */
            justify-content: space-between;
        }
        .post-user-img {
            min-width: 50px;
            min-height: 50px;
            border-radius: 100%;
        }
        .post-vertical-line {
            width: 2px;
            height: calc(100% - 50px - 1rem);
            background: #F5F5F5;
            /* background: #1d1b1b; */
        }
        .post-text--container {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            /* width: 80%; */
            width: calc(100% - 50px);
            height: 100%;
            /* padding: 0 1rem; */
            padding-left: 1rem;
        }
        .post-title {
            font-family: 'Gilroy', sans-serif;
            font-weight: 600;
            color: #424242;
            font-size: 24px;
            width: 100%;
            /* text-align: center; */
        }
        .post-timeline {
            font-family: 'Inter', sans-serif;
            font-weight: 500;
            color: #9E9C9F;
            font-size: 14px;
            /* text-align: center; */
            margin-bottom: 1rem;
            width: 100%;
        }
        .post-description {
            font-family: 'Inter', sans-serif;
            font-weight: 500;
            color: #424242;
            font-size: 20px;
            /* text-align: center; */
            margin-bottom: 1rem;
            width: 100%;
        }
        .post-icons {
            font-family: 'Inter', sans-serif;
            font-weight: 500;
            color: #9E9C9F;
            font-size: 16px;
            /* text-decoration: underline; */
        }

    </style>
</head>
<body>
    <div id="room-feed--container">
        <div id="feed--no-of-posts">0 Posts</div>
        <div id="feed-post--container">
            <div class="each-post--container">
                <span class="user-post--separator">
                    <img class="post-user-img" src="https://we.vanillicon.com/v2/e1b51268b75e0123ed06d4ddcaf56d8a.svg">
                    <div class="post-vertical-line"></div>
                </span>
                <span class="post-text--container">
                    <div class="post-title t-ellipsis">
                        What is a Neo Bank? It is a kind of digital bank. Rather than being physically present at specific locations, neo-banking is entirely online. It is a kind of digital bank. Rather than being physically present at specific locations, neo-banking is entirely online.
                        It is a kind of digital bank. Rather than being physically present at specific locations, neo-banking is entirely online. It is a kind of digital bank. Rather than being physically present at specific locations, neo-banking is entirely online.
                        It is a kind of digital bank. Rather than being physically present at specific locations, neo-banking is entirely online.
                    </div>
                    <div class="post-timeline t-ellipsis">
                        Posted 2 days ago
                    </div>
                    <div class="post-description line-clamp">
                        It is a kind of digital bank.
                    </div>
                    <!-- <div>
                        t is a kind of digital bank. Rather than being physically present at specific locations, neo-banking is entirely online. It is a kind of digital bank. Rather than being physically present at specific locations, neo-banking is entirely online.
                        It is a kind of digital bank. Rather than being physically present at specific locations, neo-banking is entirely online.
                        It is a kind of digital bank. Rather than being physically present at specific locations, neo-banking is entirely online.
                    </div> -->
                    <div class="post-icons">
                        &#x2661; <u>234</u>  &#x2661; <u>135</u>
                    </div>
                </span>
            </div>
        </div>
    </div>
    <script defer>
        // Set Data Related to category
        const setCategoryData = categoryData => {
            const noOfPosts = categoryData.countAllDiscussions;
            const posts = `<div id="feed--no-of-posts">${noOfPosts} Posts</div>`;
            const postsEle = document.getElementById('feed--no-of-posts');
            postsEle.innerHTML = posts;
        };
        // Get Category related data
        const xhr = new XMLHttpRequest();
        xhr.open('GET', 'https://epifi.vanillastaging.com/api/v2/categories/11', true);
        const token = '';
        xhr.setRequestHeader('Authorization', token);
        xhr.send();
        xhr.onload = () => {
            if (xhr.readyState === 4) { // readyState 4 means the request is done.
                if (xhr.status === 200) { // status 200 is a successful return.
                    const categoryData = JSON.parse(xhr.responseText);
                    console.log(xhr.responseText); // 'This is the returned text.'
                    setCategoryData(categoryData);
                } else {
                    console.log('Error: ' + xhr.status); // An error occurred during the request.
                }
            }
        };
    </script>
</body>
</html>