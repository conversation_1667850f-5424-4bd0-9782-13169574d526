<style>
    .create-text-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0px;
        position: relative;
        background: #FFFFFF;
        border-radius: 10px;
        padding: 0%;
    }

    .create-text-heading {
        position: relative;
        font-family: <PERSON><PERSON>;
        font-style: normal;
        font-weight: 500;
        font-size: 48px;
        line-height: 110%;
        margin-top: 80px;
        color: #424242;
    }

    .discussion-room-dropdown-text {
        position: relative;
        font-family: Inter;
        font-style: normal;
        font-weight: 500;
        font-size: 20px;
        line-height: 120%;
        color: #424242;
        margin-right: 10px;
    }

    .discussion-room-form {
        margin-top: 30px;
    }

    .discussion-room-dropdown-container {
        align-items: center;
        padding: 16px 20px;
        position: relative;
        width: 331px;
        background: #F5F5F5;
        border-radius: 10px;
    }

    .discussion-room-dropdown-option {
        position: static;
        font-family: <PERSON><PERSON>;
        font-style: normal;
        font-weight: bold;
        font-size: 16px;
        line-height: 110%;
        display: flex;
        align-items: flex-end;
        letter-spacing: 0.45px;
        text-transform: uppercase;
        color: #424242;
        margin: 5px 0px;
    }    

    .grey-section-box {
        display: flex;
        flex-direction: column;
        padding: 50px 80px;
        position: static;
        width: 100%;
        background: #F5F5F5;
        flex: none;
        order: 1;
        flex-grow: 0;
        margin: 0px 0px;
    }
    .post-type-radio-buttons {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-items: center;
        padding: 0px;
        position: static;
        background: #FFFFFF;
        border-radius: 10px;
        flex: none;
        order: 0;
        align-self: center;
        flex-grow: 0;
    }

    .post-type-radio-button {
        position: relative;
        margin: 10px;
        padding: 20px;
        border-radius: 10px;
        flex: none;
        flex-grow: 0;
        margin: 0px 0px;
        font-family: Gilroy;
        background-color: #FFFFFF;
        font-style: normal;
        font-weight: bold;
        font-size: 16px;
        line-height: 110%;
        display: flex;
        align-items: flex-end;
        letter-spacing: 0.45px;
        text-transform: uppercase;
        color: #9E9C9F;
        cursor: pointer;
    }

    .post-type-radio-button [type='radio'] {
        display: none; 
    } 

    .post-type-radio-button checked {
        background-color: #424242;
        color: #FFFFFF;
    }

    .create-poll-form-title-box {
        display: flex;
        flex-direction: column;
        padding: 18px;
        align-self: center;
        margin-top: 40px;
        width: 810px;
        height: auto;
        background: #FFFFFF;
        border-radius: 6px;
    }

    .create-poll-form-title-box input {
        height: auto;
        border: 0px;
        margin-top: 5px;
        border-color: #FFFFFF;
    }

    .create-poll-form-description-box {
        display: flex;
        flex-direction: column;
        padding: 18px;
        margin-top: 40px;
        align-self: center;
        width: 810px;
        height: auto;
        background: #FFFFFF;
        border-radius: 6px;
    }

    .create-poll-form-description-box input {
        height: auto;
        border: 0px; 
        margin-top: 5px;
        border-color: #FFFFFF;
    }

    .create-poll-form-tags-box {
        display: flex;
        flex-direction: column;
        padding: 18px;
        align-self: center;
        margin-top: 10px;
        width: 810px;
        height: auto;
        background: #FFFFFF;
        border-radius: 6px;
    }

    .create-poll-form-tags-box input {
        height: auto;
        border: 0px; 
        margin-top: 5px;
        border-color: #FFFFFF;
    }

    .create-poll-form-title {
        position: static;
        font-family: Gilroy;
        font-style: normal;
        font-weight: 600;
        font-size: 14px;
        line-height: 100%;
        display: flex;
        letter-spacing: 0.45px;
        text-transform: uppercase;
        color: #9E9C9F;
        flex: none;
        order: 0;
        flex-grow: 0;
        margin: 0px 7px;
    }

    .create-post-form-add-image {
        position: relative;
        font-family: Gilroy;
        font-style: normal;
        font-weight: bold;
        font-size: 16px;
        line-height: 110%;
        border: none;
        background-color: F5F5F5;
        padding: 30px;
        display: flex;
        align-items: flex-end;
        letter-spacing: 0.45px;
        text-transform: uppercase;
        align-self: center;
        color: #00B899;
        flex: none;
        order: 0;
        flex-grow: 0;
        margin-left: 10px 0px;
        cursor: pointer;
    }
    .create-post-form-add-image-box {
        display: none; 
        position: fixed; 
        z-index: 1;
        overflow: auto;
        position: static;
        width: 810px;
        height: 298px;
        background: #FFFFFF;
        border-radius: 6px;
        align-self: center;
        padding: auto;
        flex: none;
        order: 3;
        flex-grow: 0;
        margin: 0px 40px;
    }

    .create-post-form-add-image-box-content1 {
        position: relative;
        font-family: Inter;
        font-style: normal;
        font-weight: 500;
        font-size: 20px;
        line-height: 120%;
        text-align: center;
        color: #424242;
        margin-top: 91px;
        order: 0;
    }

    .create-post-form-add-image-box-content2 {
        position: relative;
        font-family: Gilroy;
        font-style: normal;
        font-weight: bold;
        font-size: 16px;
        line-height: 110%;
        text-align: center;
        margin-left: 280px;
        letter-spacing: 0.45px;
        text-transform: uppercase;
        margin-top: 30px;
        color: #00B899;
        cursor: pointer;
        order: 1;
    }

    .create-post-form-add-image-box-content3 {
        position: relative;
        font-family: Inter;
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        line-height: 140%;
        text-align: center;
        margin-top: 30px;
        order: 2;
        color: #9E9C9F;
    }

    .close {
        color: #aaaaaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
    }

    .close:hover, .close:focus {
        color: #000;
        text-decoration: none;
        cursor: pointer;
    }

    .create-poll-footer {
        position: static;
        width: 970px;
        height: 90px;
        flex: none;
        order: 2;
        flex-grow: 0;
    }

    .create-poll-footer-cancel {
        position: absolute;
        width: 63px;
        height: 18px;
        right: 208px;
        bottom: 34px;
        font-family: Gilroy;
        font-style: normal;
        font-weight: bold;
        font-size: 16px;
        line-height: 110%;
        display: flex;
        align-items: flex-end;
        letter-spacing: 0.45px;
        text-transform: uppercase;
        color: #00B899;
        cursor: pointer;
    }

    .create-poll-footer-submit {
        position: absolute;
        width: 98px;
        height: 50px;
        right: 80px;
        bottom: 20px;
        background: #424242;
        border-radius: 10px;
        color: #FFFFFF;
        cursor: pointer;
    }
    .create-poll-footer-submit-text {
        color: #FFFFFF;
        margin-left: 17px;
        margin-top: 15px;
    }

    .create-poll-vertical-line {
        position: relative;
        width: 2px;
        height: auto;
        margin-top: 20px;
        background: #C4C4C4;
        border-radius: 6px;
    }

    .create-poll-option-box {
        position: relative;
        width: 768px;
        height: 45px;
        margin: 20px;
        background: #FFFFFF;
        padding: 18px;
        border-radius: 6px;
    }

    .create-poll-option-box-option-number {
        font-family: Gilroy;
        font-style: normal;
        font-weight: 600;
        font-size: 14px;
        line-height: 100%;
        display: flex;
        letter-spacing: 0.45px;
        text-transform: uppercase;
        color: #9E9C9F;
        flex: none;
        order: 0;
        flex-grow: 0;
    }

    .create-poll-option-box-name {
        font-family: Inter;
        font-style: normal;
        font-weight: 500;
        font-size: 20px;
        line-height: 120%;
        color: #424242;
        flex: none;
        order: 1;
        flex-grow: 0;
    }

    .create-poll-add-option {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 20px;
        width: 219px;
        height: 46px;
        background: #F5F5F5;
        border-radius: 10px;
        position: relative;
        font-family: Gilroy;
        font-style: normal;
        font-weight: bold;
        font-size: 16px;
        line-height: 110%;
        display: flex;
        align-items: flex-end;
        letter-spacing: 0.45px;
        text-transform: uppercase;
        color: #00B899;
        margin-left: 10px;
        cursor: pointer;
    }

    .alignCenter {
        align-self: center;
    }

    .alignAuto {
        position: relative;
        margin-left: 290px;
    }
    
</style>

<div class="create-text-container">
    <div class="create-text-heading">Create New Post</div>
    <div>
        <form class="discussion-room-form">
            <label for="discuss-room" class="discussion-room-dropdown-text">Discussion Room</label>
            <select name="discuss-room" id="discuss-room" class="discussion-room-dropdown-container">
                <option value="Mutual Funds" class="discussion-room-dropdown-option">MUTUAL FUNDS</option>
            </select>
        </form>
    </div>
    <div class="grey-section-box">
        <div class="post-type-radio-buttons">
            <label class="post-type-radio-button">
                TEXT
                <input class="set-height-text" type="radio" name="text" checked>
            </label>
            <label class="post-type-radio-button">
                POLLS
                <input class="set-height-polls" type="radio" name="polls">
            </label>
            <label class="post-type-radio-button">
                SURVEYS
                <input class="set-height-surveys" type="radio" name="surveys">
            </label>
        </div>
            <div class="create-poll-form-title-box">
                <label for="poll-title" class="create-poll-form-title">TITLE</label>
                <input id="poll-title" type="text">
            </div>
            <div class="create-poll-form-description-box">
                <label for="description" class="create-poll-form-title">POLL QUESTION</label>
                <input id="description" type="text">
            </div>
            <div class="alignAuto">
                <div class="create-poll-vertical-line">
                    <div class="create-poll-option-box">
                        <div class="create-poll-option-box-option-number">OPTION 1</div>
                        <div class="create-poll-option-box-name">Gold ETFs</div>
                    </div>
                    <div class="create-poll-option-box">
                        <div class="create-poll-option-box-option-number">OPTION 2</div>
                        <div class="create-poll-option-box-name">Liquid Funds</div>
                    </div>
                    <div class="create-poll-add-option">ADD ANOTHER OPTION</div>
                </div>
            </div>
            <div class="alignCenter">
                <button class="create-post-form-add-image" id="addImgBtn">ADD IMAGE</button>
                <div class="create-post-form-add-image-box" id="myPopup">
                    <span class="close">&times;</span>
                    <div class="create-post-form-add-image-box-content1">Drop your images here</div>
                    <input type="file" id="img" name="img" accept="image/*" class="create-post-form-add-image-box-content2">
                    <div class="create-post-form-add-image-box-content3">Max file size 5MB</div>
                </div>
            </div>
            <div class="create-poll-form-tags-box">
                <label for="tags" class="create-poll-form-title">TAGS</label>
                <input id="tags" type="tags">
            </div>
    </div>
    <div class="create-poll-footer">
        <div class="create-poll-footer-cancel">CANCEL</div>
        <div class="create-poll-footer-submit">
            <div class="create-poll-footer-submit-text">SUBMIT</div>
        </div>
    </div>
</div>

<script defer>
    var modal = document.getElementById("myPopup");

    // Get the button that opens the modal
    var btn = document.getElementById("addImgBtn");

    // Get the <span> element that closes the modal
    var span = document.getElementsByClassName("close")[0];

    // When the user clicks the button, open the modal 
    btn.onclick = function() {
    modal.style.display = "block";
    }

    // When the user clicks on <span> (x), close the modal
    span.onclick = function() {
    modal.style.display = "none";
    }

    // When the user clicks anywhere outside of the modal, close it
    window.onclick = function(event) {
        if (event.target == modal) {
            modal.style.display = "none";
        }
    }
    console.log('this is js');
    // Creates dynamic cards and adds them to DOM.
    const createCard = (users) => {
        const cardContainer = document.getElementById('discuss-room');
        let cardsList = '';
        console.log('users: ', users);
        for ( const user of users) {
            console.log('user: ', user);
            const eachCard =
            `
            <div class="leaderboard-card">
                <option value="${user.name}" class="discussion-room-dropdown-option">${user.name}</option>
            </div>
            `;
            cardsList += eachCard;
        }
        cardContainer.innerHTML = cardsList;
    };
    // Mapping category id with name
    let temp12=[];
    const run = (users) => {
        for(const user of users) {
            temp12[user.name]=user.categoryID;
        }
        //console.log(temp12);
    }
    // Fetches data from the api
    const xh = new XMLHttpRequest();
    xh.open('GET', 'https://epifi.vanillastaging.com/api/v2/categories', true);
    const tokenLeaderboard = '';
    xh.setRequestHeader('Authorization', tokenLeaderboard);
    xh.send();
    xh.onload = () => {
        //console.log('inside on ready');
        const DONE = 4; // readyState 4 means the request is done.
        const OK = 200; // status 200 is a successful return.
        if (xh.readyState === DONE) {
            if (xh.status === OK) {
                const users = JSON.parse(xh.responseText);
                console.log(xh.responseText); // 'This is the returned text.'
                createCard(users);
                run(users);
            } else {
                console.log('Error: ' + xh.status); // An error occurred during the request.
            }
        }
    };
    let params = {
        "pollID": 5,
        "name": "string",
        "discussionID": 0,
        "countOptions": 0,
        "countVotes": 0,
        "insertUserID": 0,
        "dateInserted": "",
        "updateUserID": null,
        "dateUpdated": null
    }
    //post after taking input
    function submitPost() {
        params.body = document.getElementById('description').value;
        params.name = document.getElementById('title').value;
        params.categoryID = temp12[document.getElementById('discuss-room').value];
        console.log(params);
        http.send(JSON.stringify(params));
    }

    var http = new XMLHttpRequest();
    var url = 'https://epifi.vanillastaging.com/api/v2/polls';
    http.open('POST', url, true);

    //Send the proper header information along with the request
    const tokenPost = '';
    http.setRequestHeader('Authorization', tokenPost);

    http.onreadystatechange = function() {//Call a function when the state changes.
        console.log('inside on ready');
        const DONE = 4; // readyState 4 means the request is done.
        const OK = 200; // status 200 is a successful return.
        if (http.readyState === DONE) {
            if (http.status === OK) {
                alert(http.responseText);
            } else {
                console.log('Error: ' + http.status); // An error occurred during the request.
            }
        }
    }
</script>
