<title>Fi Community - Story</title>
<link rel="preconnect" href="https://fonts.gstatic.com">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@500&display=swap" rel="stylesheet">
<link rel="stylesheet"
    href="https://cdn.rawgit.com/mfd/********************************/raw/e06a670afcb2b861ed2ac4a1ef752d062ef6b46b/Gilroy.css">
<style>
    * {
        box-sizing: border-box
    }

    html,
    body {
        padding: 0;
        margin: 0;
        height: 100%
    }

    .cpointer {
        cursor: pointer;
        -webkit-tap-highlight-color: transparent
    }

    .cflex {
        display: flex
    }

    #cover-story {
        display: none
    }

    #category-container {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        justify-content: flex-start;
        align-items: center;
        margin: auto;
        font-size: 16px
    }

    #category-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        background: #F5F5F5;
        width: 100%;
        padding: 3em
    }

    #search-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 50px;
        font-size: 50px
    }

    .fi-icon {
        height: 100%
    }

    .h-poster-input-box {
        position: relative;
        width: 70%;
        height: 100%
    }

    ::placeholder {
        color: #9E9C9F
    }

    #sh-poster-input {
        padding: 22px 25px 22px 73px;
        padding-left: 73px;
        padding-right: 15px;
        height: 100%;
        vertical-align: top;
        outline: none;
        border: none;
        border-radius: 10px;
        width: -webkit-fill-available;
        font-weight: 500;
        font-size: 0.3em;
        line-height: 120%;
        color: #9E9C9F
    }

    #sh-poster-input:focus {
        box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.14), 0px 5px 10px rgba(0, 0, 0, 0.04), 0px 0px 15px rgba(0, 0, 0, 0.05)
    }

    #sh-poster-input:not(:placeholder-shown)~.h-poster-close-box {
        display: flex;
        align-items: center;
        justify-content: center
    }

    .h-poster-search-box {
        position: absolute;
        height: 22px;
        width: 22px;
        top: calc(100%/2 - 9px);
        left: 28px
    }

    .h-poster-close-box {
        position: absolute;
        height: 24px;
        width: 24px;
        top: calc(50% - 12px);
        right: 28px;
        border-radius: 100%;
        cursor: pointer;
        background-color: #9e9c9f;
        color: #fff;
        display: none;
        font-size: initial
    }

    #sh-poster-input:not(:placeholder-shown)~.h-poster-close-box {
        display: flex;
        align-items: center;
        justify-content: center
    }

    #profile-img {
        height: 100%;
        border-radius: 100%
    }

    .sty_h-poster-option-box {
        position: absolute;
        top: 83px;
        padding: 30px;
        left: 0px;
        right: 0;
        background-color: #fff;
        border-radius: 10px;
        display: none;
        z-index: 1;
        box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.14), 0px 5px 10px rgba(0, 0, 0, 0.04), 0px 0px 15px rgba(0, 0, 0, 0.05)
    }

    .h-poster-diss-card {
        display: flex;
        align-items: center
    }

    .h-poster-diss-card-text {
        font-family: Gilroy;
        font-weight: 600;
        font-size: 24px;
        line-height: 115%;
        color: #424242
    }

    .h-poster-diss-card-text-desc {
        font-family: Inter;
        font-weight: 500;
        font-size: 16px;
        line-height: 140%;
        color: #9e9c9f
    }

    .h-poster-diss-card-img {
        height: 50px;
        width: 50px;
        overflow: hidden;
        border-radius: 100%;
        margin-right: 15px
    }

    .sty_h-poster-diss-title {
        font-family: Inter;
        font-weight: 500;
        font-size: 16px;
        line-height: 140%;
        color: #9e9c9f;
        margin-bottom: 15px
    }

    #sty_hPosterDissCardHolder>div:not(:last-child) {
        margin-bottom: 30px
    }

    .show {
        display: block !important
    }

    .profile-img-cover {
        width: 100%;
        height: 100%
    }

    /* .each-post--container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
        height: auto;
        font-size: 50px;
        margin-top: 1em
    } */

    .user-post--separator {
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 100%;
        width: 1em;
        justify-content: space-between
    }

    .post-user-img {
        min-width: 1em;
        min-height: 1em;
        width: 1em;
        height: 1em;
        border-radius: 100%
    }

    .post-text--container {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        width: calc(100% - 1em);
        height: 100%;
        padding: 0 0 1rem 1rem;
        font-size: 24px
    }

    .post-title {
        font-family: 'Gilroy', sans-serif;
        font-weight: 600;
        color: #424242;
        font-size: 36px;
        width: 100%
    }

    .line-clamp-title {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden
    }

    .post-timeline {
        font-family: 'Inter', sans-serif;
        font-weight: 400;
        color: #9E9C9F;
        font-size: 0.6em;
        margin-bottom: 1rem;
        width: 100%
    }

    .t-ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis
    }

    .post-description {
        font-family: 'Inter';
        font-weight: 400;
        color: #424242;
        font-size: 20px;
        margin-bottom: 1rem;
        width: 100%
    }

    .line-clamp-description {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden
    }

    .post-icons {
        font-family: 'Inter', sans-serif;
        font-weight: 500;
        color: #9E9C9F;
        /* padding-left: 65px; */
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    #disc-post-icon-container {
        padding-left: calc(30px + 50px + 1rem);
    }

    .sty_svg-icons {
        vertical-align: middle;
        margin-right: 5px;
    }

    .post-vertical-line {
        width: 2px;
        height: calc(100% - 1em - 1rem);
        background: #F5F5F5
    }

    #sty_room-feed--container {
        display: none;
        flex-direction: column;
        width: 100%;
        height: 100%;
        justify-content: start;
        align-items: center;
        margin: auto;
        padding: 3em 12em;
        background-color: #fff
    }

    #sty_discussion-loader {
        width: 70px;
        height: 70px;
        margin: 20px auto
    }

    #sty_discussion-loader img {
        width: 100%;
        height: 100%
    }

    .room-feed--container-class {
        width: 790px;
        margin: auto
    }

    #sty_feed-more-comments {
        font-family: 'Gilroy', sans-serif;
        font-weight: 700;
        font-size: 16px;
        color: #00B899;
        text-align: center;
        padding: 30px 0;
        cursor: pointer
    }

    .fed-more-posts {
        font-family: 'Gilroy', sans-serif;
        font-weight: 700;
        font-size: 16px;
        color: #00B899
    }

    #loadingDiscussion {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: auto
    }

    .icon-count {
        margin-right: 1rem
    }

    .hline {
        width: 100%;
        height: 2px;
        left: 335px;
        background: #F5F5F5;
        border-radius: 5px;
        margin-top: 18px;
        margin-bottom: 18px
    }

    .sharebtn {
        font-family: Gilroy;
        font-style: normal;
        font-weight: bold;
        font-size: 16px;
        margin-right: 0px;
        letter-spacing: 0.45px;
        text-transform: uppercase;
        color: #00B899;
        display: flex;
        margin: auto 10px
    }

    .btn-container {
        display: inline-block;
        margin-top: 30px;
        font-size: 16px;
    }

    .total-comments-heading {
        font-family: Gilroy;
        font-style: normal;
        font-weight: 600;
        font-size: 24px;
        line-height: 115%;
        color: #424242;
        margin-top: 71px
    }

    .add-comment-heading {
        font-family: Gilroy;
        font-style: normal;
        font-weight: 600;
        font-size: 24px;
        line-height: 115%;
        color: #424242
    }

    .add-comment-box {
        background: #F5F5F5;
        border-radius: 10px;
        min-height: 168px;
        margin-top: 15px;
        width: 100%;
        padding: 20px;
        position: relative
    }

    .post-reply-btn {
        width: 132px;
        height: 50px;
        position: absolute;
        right: 20px;
        bottom: 20px;
        background: #424242;
        border-radius: 10px;
        padding-top: 18px;
        padding-left: 20px
    }

    .post-reply-btn-text {
        top: calc(50% - 18px/2 + 2px);
        font-family: Gilroy;
        font-style: normal;
        font-weight: bold;
        font-size: 16px;
        line-height: 110%;
        letter-spacing: 0.45px;
        text-transform: uppercase;
        color: #FFF
    }

    .poll-option-box {
        width: 100%;
        height: auto;
        background: #F5F5F5;
        border-radius: 10px;
        padding: 30px
    }

    .poll-option-box-title {
        font-family: Inter;
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        line-height: 140%;
        color: #333
    }

    .each-poll-option-box {
        background: #FFF;
        border-radius: 10px;
        padding: 14px;
        margin-top: 20px
    }

    .each-poll-option-box-text {
        font-family: Inter;
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        line-height: 140%;
        display: flex;
        align-items: flex-end;
        color: #424242;
        margin-left: 20px
    }

    .poll-opt-btn {
        width: 98px;
        height: 50px;
        background: #424242;
        border-radius: 10px;
        padding-top: 18px;
        padding-left: 20px;
        float: right;
        margin-top: 30px;
        cursor: pointer
    }

    .poll-opt-btn-text {
        font-family: Gilroy;
        font-style: normal;
        font-weight: bold;
        font-size: 16px;
        line-height: 110%;
        display: flex;
        align-items: flex-end;
        letter-spacing: 0.45px;
        text-transform: uppercase;
        color: #FFF
    }

    #discussion-delete-btn, #discussion-edit-btn, #top-comments, #user-input-container {
        display: none
    }

    @media screen and (max-width:768px) {
        #category-container {
            font-size: 10px
        }

        #search-container {
            height: 45px !important
        }

        .h-poster-close-box {
            height: 22px;
            width: 22px;
            top: calc(100%/2 - 9px);
            right: 15px
        }

        .sty_h-poster-option-box {
            top: 60px;
            padding: 20px
        }

        .each-post--container {
            font-size: 44px
        }
        #disc-post-icon-container {
            padding-left: calc(30px + 44px + 1rem);
        }

        .post-text--container {
            font-size: 19px
        }

        .sharebtn {
            font-size: 10px
        }

        .line-clamp-description {
            -webkit-line-clamp: 3
        }
    }

    @media screen and (max-width:425px) {
        #category-container {
            font-size: 5px
        }

        #search-container {
            height: 40px !important
        }

        .h-poster-input-box {
            width: 65% !important
        }

        .h-poster-close-box {
            height: 22px;
            width: 22px;
            top: calc(100%/2 - 9px);
            right: 15px
        }

        .sty_h-poster-option-box {
            top: 60px;
            padding: 20px
        }

        .each-post--container {
            font-size: 36px
        }
        #disc-post-icon-container {
            padding-left: calc(30px + 36px + 1rem);
        }

        .post-text--container {
            font-size: 16px
        }

        .sharebtn {
            font-size: 5px
        }

        .line-clamp-description {
            -webkit-line-clamp: 4
        }

        #sty_room-feed--container {
            padding: 3em 3em
        }

        .hline {
            width: 100%;
            height: 1px;
            transform: matrix(1, 0, 0, -1, 0, 0)
        }

        .total-comments-heading {
            margin-top: 39px;
            margin-bottom: 44px;
            font-size: 16px
        }

        .add-comment-heading {
            font-size: 16px
        }

    }
</style>
<style>
    .bulletin {
        background: #f5f5f5;
    }

    .bulletin-wrapper {
        max-width: 1440px;
        margin: auto;
        padding: 80px 85px;
        bottom: 10px;
        overflow: hidden
    }

    .bulletin--grid {
        display: grid;
        grid-template-columns: repeat(8, calc(25% - 23px));
        grid-template-areas: 'one one four five seven seven ten eleven''two three four six eight nine ten twelve';
        gap: 30px;
        overflow: auto;
        width: calc(100%)
    }

    .bulletin--grid--item {
        border-radius: 10px;
        background-color: #fff;
        height: 100%;
        min-height: 185px;
        overflow: hidden;
        cursor: pointer;
    }

    .bulletin--grid--item-1 {
        grid-area: one;
        display: flex;
        max-height: 185px
    }

    .item--content {
        flex-basis: calc(100% - 240px);
        display: flex;
        flex-direction: column;
        height: 100%;
        justify-content: space-between
    }

    img {
        border-radius: 10px
    }

    .content--top {
        font-family: Gilroy;
        font-style: normal;
        font-weight: 600;
        font-size: 24px;
        line-height: 115%;
        color: #424242
    }

    .content--bottom {
        font-family: Inter;
        font-style: normal;
        font-weight: 400;
        font-size: 20px;
        line-height: 120%;
        color: #9E9C9F
    }

    .padding-30 {
        padding: 30px
    }

    .bulletin--grid--item-2 {
        grid-area: two
    }

    .bulletin--grid--item-3 {
        grid-area: three
    }

    .bulletin--grid--item-4 {
        grid-area: four
    }

    .bulletin--grid--item-5 {
        grid-area: five
    }

    .bulletin--grid--item-6 {
        grid-area: six
    }

    .bulletin--grid--item-7 {
        grid-area: seven;
        display: flex;
        max-height: 185px
    }

    .bulletin--grid--item-8 {
        grid-area: eight
    }

    .bulletin--grid--item-9 {
        grid-area: nine
    }

    .bulletin--grid--item-10 {
        grid-area: ten
    }

    .bulletin--grid--item-11 {
        grid-area: eleven
    }

    .bulletin--grid--item-12 {
        grid-area: twelve
    }

    @media (max-width:1290px) {
        .bulletin--grid {
            display: grid;
            grid-template-columns: repeat(8, calc((100% / 3) - 20px))
        }
    }
</style>
<div id="cover-story">
    <div style="display: flex; flex-direction: column;">
        <div id="category-container">
            <div id="category-info">
                <div id="search-container">
                    <a href="/">
                        <svg class="fi-icon" width="52" height="52" viewBox="0 0 52 52" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M37.6 19.0283C34.074 19.0283 31.2046 16.2139 31.2046 12.7556C31.2046 9.29791 34.074 6.48437 37.6 6.48437C41.1283 6.48437 43.9992 9.29791 43.9992 12.7556C43.9992 16.2139 41.1283 19.0283 37.6 19.0283Z"
                                fill="#00B899"></path>
                            <path
                                d="M48.4766 45.4347H43.063C37.5786 45.4347 32.0444 41.5264 32.0444 32.7956V23.751H43.2273V31.172C43.2273 33.7751 44.0856 34.2519 45.7754 34.2519H48.4766V45.4347Z"
                                fill="#00B899"></path>
                            <path
                                d="M3.53076 45.4637H14.7872V37.0483C14.7872 34.797 15.5005 34.2147 18.256 34.2147H28.2689V23.7205H14.7872V20.7575C14.7872 17.9282 15.5652 16.9733 17.8693 16.9733H28.2548V6.48438H15.8099C7.77678 6.48438 3.53076 10.624 3.53076 18.4563V45.4637Z"
                                fill="#00B899"></path>
                        </svg>
                    </a>

                    <div class="h-poster-input-box"><input id="sh-poster-input" type="text"
                            placeholder="Search discussions" onkeyup="sonInputChange(this.value)" /><svg
                            class="h-poster-search-box" width="20" height="20" viewBox="0 0 20 20" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M7.05371 0.571411C6.45829 0.571411 5.88435 0.648141 5.33189 0.801601C4.77944 0.955061 4.26381 1.17297 3.78502 1.45534C3.30622 1.73771 2.8704 2.07532 2.47754 2.46817C2.08468 2.86103 1.744 3.29993 1.4555 3.78486C1.16699 4.26979 0.94908 4.78542 0.801758 5.33174C0.654436 5.87805 0.577705 6.45199 0.571568 7.05355C0.571568 7.64898 0.648298 8.22292 0.801758 8.77537C0.955217 9.32783 1.17313 9.84345 1.4555 10.3222C1.73786 10.801 2.07547 11.2369 2.46833 11.6297C2.86119 12.0226 3.30008 12.3633 3.78502 12.6518C4.26995 12.9403 4.78557 13.1582 5.33189 13.3055C5.87821 13.4528 6.45215 13.5296 7.05371 13.5357C7.82101 13.5357 8.55455 13.4068 9.25432 13.149C9.9541 12.8912 10.6017 12.5198 11.1971 12.0349L18.4251 19.2536C18.5417 19.3702 18.6798 19.4286 18.8394 19.4286C18.999 19.4286 19.1371 19.3702 19.2538 19.2536C19.3704 19.137 19.4287 18.9989 19.4287 18.8393C19.4287 18.6797 19.3704 18.5416 19.2538 18.4249L12.035 11.197C12.5199 10.6077 12.8913 9.96315 13.1491 9.26338C13.4069 8.5636 13.5359 7.82699 13.5359 7.05355C13.5359 6.45813 13.4591 5.88419 13.3057 5.33174C13.1522 4.77928 12.9343 4.26365 12.6519 3.78486C12.3696 3.30607 12.0319 2.87024 11.6391 2.47738C11.2462 2.08452 10.8073 1.74384 10.3224 1.45534C9.83747 1.16684 9.32185 0.948922 8.77553 0.801601C8.22921 0.654279 7.65527 0.57755 7.05371 0.571411ZM7.05371 12.3571C6.32324 12.3571 5.63574 12.219 4.99121 11.9428C4.34668 11.6666 3.78502 11.286 3.30622 10.801C2.82743 10.3161 2.44991 9.75445 2.17369 9.11605C1.89746 8.47766 1.75628 7.79016 1.75014 7.05355C1.75014 6.32309 1.88825 5.63559 2.16448 4.99105C2.44071 4.34652 2.82129 3.78486 3.30622 3.30607C3.79115 2.82727 4.35282 2.44976 4.99121 2.17353C5.6296 1.8973 6.3171 1.75612 7.05371 1.74998C7.78418 1.74998 8.47168 1.8881 9.11621 2.16432C9.76074 2.44055 10.3224 2.82113 10.8012 3.30607C11.28 3.791 11.6575 4.35266 11.9337 4.99105C12.21 5.62945 12.3511 6.31695 12.3573 7.05355C12.3573 7.78402 12.2192 8.47152 11.9429 9.11605C11.6667 9.76059 11.2861 10.3222 10.8012 10.801C10.3163 11.2798 9.7546 11.6573 9.11621 11.9336C8.47782 12.2098 7.79032 12.351 7.05371 12.3571Z"
                                fill="#9E9C9F" stroke="#9E9C9F" stroke-width="0.428571"></path>
                        </svg><span class="h-poster-close-box" onclick="onClose()">
                            &times;
                        </span>
                        <div id="sty_hPosterOptionBox" class="sty_h-poster-option-box">
                            <div class="sty_h-poster-diss-title">Discussions</div>
                            <div id="sty_hPosterDissCardHolder"></div>
                        </div>
                    </div><img onclick="location.href='/profile';" id="profile-img" src="">
                </div>
            </div>
            <div id="sty_room-feed--container">
                <div id="loadingDiscussion"></div>
            </div>
            <div id="sty_discussion-loader" class="room-feed--container-class"><img
                    src="https://us.v-cdn.net/6032409/uploads/AJHFG85AXYG9/animation-200-kmw01inn.gif" alt="loader" />
            </div>
        </div>
        <div class="bulletin">
            <div class="bulletin-wrapper">
                <div class="bulletin--grid">
                    <div class="bulletin--grid--item bulletin--grid--item-1">
                        <div class="item--content padding-30"></div>
                        <div class="item-1--svg"><svg width="240" height="185" viewBox="0 0 240 185" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                    <rect width="240" height="185" fill="#FFD5D4"></rect>
                                    <path
                                        d="M225 200C225 172.152 213.938 145.445 194.246 125.754C174.555 106.062 147.848 95 120 95C92.1523 95 65.4451 106.062 45.7538 125.754C26.0625 145.445 15 172.152 15 200L120 200H225Z"
                                        fill="white"></path>
                                    <path
                                        d="M120 90C147.848 90 174.555 78.9375 194.246 59.2462C213.938 39.5549 225 12.8477 225 -15C225 -42.8477 213.938 -69.5549 194.246 -89.2462C174.555 -108.938 147.848 -120 120 -120L120 -15L120 90Z"
                                        fill="white"></path>
                                </g>
                                <defs>
                                    <clipPath id="clip0">
                                        <rect width="240" height="185" fill="white"></rect>
                                    </clipPath>
                                </defs>
                            </svg></div>
                    </div>
                    <div class="bulletin--grid--item bulletin--grid--item-2 item--content padding-30"></div>
                    <div class="bulletin--grid--item bulletin--grid--item-3 item--content padding-30"></div>
                    <div class="bulletin--grid--item bulletin--grid--item-4 item--content padding-30"></div>
                    <div class="bulletin--grid--item bulletin--grid--item-5 item--content padding-30"></div>
                    <div class="bulletin--grid--item bulletin--grid--item-6 item--content padding-30"></div>
                    <div class="bulletin--grid--item bulletin--grid--item-7">
                        <div class="item--content padding-30"></div>
                        <div class="item-1--svg"><svg width="240" height="185" viewBox="0 0 240 185" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0)">
                                    <rect width="240" height="185" fill="#FFD5D4"></rect>
                                    <path
                                        d="M225 200C225 172.152 213.938 145.445 194.246 125.754C174.555 106.062 147.848 95 120 95C92.1523 95 65.4451 106.062 45.7538 125.754C26.0625 145.445 15 172.152 15 200L120 200H225Z"
                                        fill="white"></path>
                                    <path
                                        d="M120 90C147.848 90 174.555 78.9375 194.246 59.2462C213.938 39.5549 225 12.8477 225 -15C225 -42.8477 213.938 -69.5549 194.246 -89.2462C174.555 -108.938 147.848 -120 120 -120L120 -15L120 90Z"
                                        fill="white"></path>
                                </g>
                                <defs>
                                    <clipPath id="clip0">
                                        <rect width="240" height="185" fill="white"></rect>
                                    </clipPath>
                                </defs>
                            </svg></div>
                    </div>
                    <div class="bulletin--grid--item bulletin--grid--item-8 item--content padding-30"></div>
                    <div class="bulletin--grid--item bulletin--grid--item-9 item--content padding-30"></div>
                    <div class="bulletin--grid--item bulletin--grid--item-10 item--content padding-30"></div>
                    <div class="bulletin--grid--item bulletin--grid--item-11 item--content padding-30"></div>
                    <div class="bulletin--grid--item bulletin--grid--item-12 item--content padding-30"></div>
                </div>
            </div>
        </div>
    </div>
    <script defer>
        const sty_apiToken = ''; const sty_httpOptions = { headers: new Headers({ Authorization: sty_apiToken }), }; const sty_IS_DEPLOYMENT_ENV = true; const sty_getHttpOptions = () => { if (sty_IS_DEPLOYMENT_ENV) return {}; return sty_httpOptions; };
        const sty_getBaseUrl = () => { if (sty_IS_DEPLOYMENT_ENV) return ''; return 'https://epifi.vanillastaging.com'; };
    </script>
    <script defer>
        const userData = {}; let storySignedInUserData = null; let CATEGORY_ID = null; let cachePostData = null; let cacheCommentArr = [];</script>
    <script defer>
        const sty_getSignedInUserData = async () => {
            try {
                if (storySignedInUserData) return storySignedInUserData; const userData = await fetch(`${sty_getBaseUrl()}/api/v2/users/me`, sty_getHttpOptions())
                    .then(res => res.json()); storySignedInUserData = userData; return storySignedInUserData;
            } catch (err) { console.error('Error while fetching signed in user data: ', err); return null; }
        }; const ssetProfileImg = async () => { const userData = await sty_getSignedInUserData(); const profileImgDOM = document.getElementById('profile-img'); if (userData.email) { const photoUrl = userData.photoUrl; profileImgDOM.src = photoUrl; } else { profileImgDOM.style.visibility = 'hidden'; } };</script>
    <script defer>
        const getFormattedTime = (date) => {
            const second = Math.floor((new Date() - new Date(date)) / 1000); const minute = Math.floor(second / 60); const hour = Math.floor(minute / 60); const day = Math.floor(hour / 24); if (day > 0) { return `${day} ${day === 1 ? 'day' : 'days'}`; } if (hour > 0) { return `${hour} ${hour === 1 ? 'hour' : 'hours'}`; } if (minute > 0) { return `${minute} ${minute === 1 ? 'minute' : 'minutes'}`; }
            return `${second} ${second === 1 ? 'second' : 'seconds'}`;
        };
    </script>
    <script defer>
        const sremoveSuggestionCard = () => {
            document
                .getElementById('sty_hPosterOptionBox')
                .classList.remove('show'); document.getElementById('sty_hPosterDissCardHolder').innerHTML = '';
        };
        const addSuggestionCard = (data) => {
            const root = document.getElementById(
                'sty_hPosterDissCardHolder'
            ).innerHTML = data.join(''); document.getElementById('sty_hPosterOptionBox').classList.add('show');
        };
        const getUserData = async (userId) => {
            try {
                if (userData.hasOwnProperty(userId)) { return userData[userId]; }
                const url = `${sty_getBaseUrl()}/api/v2/users/${userId}`; thisUserData = await fetch(url, sty_getHttpOptions())
                    .then((res) => res.json()); userData[userId] = thisUserData; return thisUserData;
            } catch (err) { console.error('error while fetching user data: ', err); }
        };
        const getUserPhotoUrl = async (id) => await getUserData(id).then((data) => data.photoUrl);
        const sgetFilteredDiscussionData = async (value) => {
            const queryParams = `query=${value}&page=1&limit=5`; const data = await fetch(
                `${sty_getBaseUrl()}/api/v2/discussions/search?${queryParams}`,
                sty_getHttpOptions()).then((response) => response.json());
                if (data) {
                    let photoUrls = [];
                    for (const discussion of data) {
                        photoUrls.push(await getUserPhotoUrl(discussion.insertUserID));
                    }
                    const element = data.map((value, index) => {
                        return (
                            `<div><a href=${value.url} style="all: unset;cursor: pointer"><div class="h-poster-diss-card"><div class="h-poster-diss-card-img"><img
src=${photoUrls[index]}
class="profile-img-cover"
/></div><div class="h-poster-diss-card-text"><div><p class="h-poster-diss-card-text-title">
${value.name}
</p></div><div><p class="h-poster-diss-card-text-desc">
Posted ${getFormattedTime(value.dateInserted)} ago
</p></div></div></div></a></div>`);
                    });
                    addSuggestionCard(element);
                }
        }; const sty_debounce = (func, wait) => {
            let timeout; return function executedFunction(...args) {
                const later = () => { clearTimeout(timeout); func(...args); }; clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }; const ssearchDiscussions = (value) => { if (value.length < 3) { sremoveSuggestionCard(); return; }; sgetFilteredDiscussionData(value); }; const sonInputChange = sty_debounce(ssearchDiscussions, 300); const onClose = () => { sremoveSuggestionCard(); document.getElementById('sh-poster-input').value = ''; };
    </script>
    <script defer>
        const sty_getReaction = async (reactionType, resourceType, resourceId) => { const queryParams = `type=${reactionType}&page=1&limit=10`; const url = `${sty_getBaseUrl()}/api/v2/${resourceType}/${resourceId}/reactions?${queryParams}`; return await fetch(url, sty_getHttpOptions()).then((res) => res.json()); };
        const sty_addReaction = async (reactionType, resourceType, resourceId) => {
            const payload = { reactionType, }; const reqBody = {
                method: 'post',
                body: JSON.stringify(payload),
            }; const trasientKey = window.gdn.meta.TransientKey; const customHeaders = { headers: new Headers({ 'x-transient-key': trasientKey, }), }; const fetchOptions = {
                ...reqBody,
                ...sty_getHttpOptions(),
                ...customHeaders,
            }; const url = `${sty_getBaseUrl()}/api/v2/${resourceType}/${resourceId}/reactions`; return await fetch(url, fetchOptions).then((res) => res.json());
        };
        const sty_removeReaction = async (resourceType, resourceId, userId) => {
            const reqBody = { method: 'delete', }; const fetchOptions = {
                ...reqBody,
                ...sty_getHttpOptions(),
            }; const url = `${sty_getBaseUrl()}/api/v2/${resourceType}/${resourceId}/reactions/${userId}`; return await fetch(url, fetchOptions);
        };
        const sty_updateLikeCount = (resourceData, index, className) => {
            const likeCountDOM = document.querySelectorAll(className)[index];
            let curLikes = parseInt(likeCountDOM.textContent); if (resourceData.likeInfo.hasLiked) { curLikes -= 1; } else { curLikes += 1; }
            likeCountDOM.textContent = curLikes.toString();
        };
        const sty_navToDiscussion = (discussionId) => window.location.href = `${sty_getBaseUrl()}/discussion/${discussionId}`;
        let sty_updateInProgress = false;
        const sty_updateLike = async (index, className) => {
            if (sty_updateInProgress) return;
            let resourceData = cachePostData; // '.post-like-icon-count'
            if (className === '.comment-like-icon-count') resourceData = cacheCommentArr[index];
            sty_updateInProgress = true;
            sty_updateLikeCount(resourceData, index, className);
            const {resourceType, resourceId} = resourceData;
            if (resourceData.likeInfo.hasLiked) {
                const signedInUser = await sty_getSignedInUserData();
                const userId = signedInUser.userID;
                removeReactionRes = await sty_removeReaction(resourceType, resourceId, userId);
            } else {
                addReactionRes = await sty_addReaction('Like', resourceType, resourceId);
            }
            resourceData.likeInfo.hasLiked = !resourceData.likeInfo.hasLiked;
            sty_updateInProgress = false;
        };
        const sty_hasUserLiked = (likes, signedInUser) => {
            for (const reaction of likes) { if (reaction.userID === signedInUser.userID) return true; }
            return false;
        };
        const sty_getLikesInfo = async (resourceId, resourceType) => {
            const likes = await sty_getReaction('Like', resourceType, resourceId); const signedInUser = await sty_getSignedInUserData(); const hasLiked = sty_hasUserLiked(likes, signedInUser); return {
                likeCount: likes.length,
                hasLiked,
            };
        };
    </script>
    <script defer>
        const showAdminButtons = async () => { const signedInUser = await sty_getSignedInUserData(); const userData = await getUserData(signedInUser.userID); const roles = userData.roles; const adminRole = roles.filter(roleInfo => roleInfo.name === 'Administrator'); if (adminRole.length > 0) {
            document.getElementById('discussion-delete-btn').style.display = 'flex';
            document.getElementById('discussion-edit-btn').style.display = 'flex';
        } };
        let deletePressed = 0; const deleteDiscussion = async (discussionId) => {
            deletePressed += 1; if (deletePressed < 2) { document.getElementById('delete-btn-txt').innerHTML = 'CONFIRM DELETE'; return; }
            const reqBody = { method: 'delete', }; const fetchOptions = {
                ...reqBody,
                ...sty_getHttpOptions(),
            }; const url = `${sty_getBaseUrl()}/api/v2/discussions/${discussionId}`; await fetch(url, fetchOptions); window.location.href = '/categories';
        };
        const editDiscussion = (discussionId) => window.location.href = `/post/editdiscussion/${discussionId}`;
    </script>
    <script defer>
        const postNewComment = async (discussID) => {
            postReplyDOM = document.getElementById('post-reply'); const newCommentText = postReplyDOM.innerText; if (!newCommentText) return; const commentInfo = {
                body: newCommentText,
                discussionID: discussID,
                format: 'markdown'
            }; const reqBody = {
                method: 'post',
                body: JSON.stringify(commentInfo),
            }; const trasientKey = window.gdn.meta.TransientKey; const customHeaders = {
                headers: new Headers({
                    accept: 'application/json',
                    'Content-Type': 'application/json',
                    'x-transient-key': trasientKey,
                }),
            }; const fetchOptions = {
                ...reqBody,
                ...sty_getHttpOptions(),
                ...customHeaders,
            }; try { const data = await fetch(`${sty_getBaseUrl()}/api/v2/comments`, fetchOptions).then(res => res.json()); postReplyDOM.innerText = ''; loadDiscussion(discussID); } catch (error) { console.error('Error while posting a new comment: ', error); }
        }
        let sty_pageNumber = 1; const sty_PAGE_LIMIT = 10; const loadTopComments = async (discussID) => {
            const queryParams = `discussionID=${discussID}&page=${sty_pageNumber}&limit=${sty_PAGE_LIMIT}`; const data = await fetch(
                `${sty_getBaseUrl()}/api/v2/comments?${queryParams}`,
                sty_getHttpOptions()).then((response) => response.json()); let allCards = '';
                let commentIndex = 0;
                for (const comment of data) {
                    const userInfoData = await getUserData(comment.insertUserID);
                    const timestamp = getFormattedTime(comment.dateInserted);
                    const likeInfo = await sty_getLikesInfo(comment.commentID, 'comments');
                    cacheCommentArr.push({
                        ...comment,
                        commentIndex,
                        likeInfo,
                        resourceId: comment.commentID,
                        resourceType: 'comments',
                    });
                    eachCard = `
<div class="each-post--container"><span class="user-post--separator"><img class="post-user-img" src="${userInfoData.photoUrl}"><div class="post-vertical-line"></div></span><span class="post-text--container"><span><div class="post-title line-clamp-title">
${userInfoData.name}
</div></span><div class="post-timeline t-ellipsis">
${timestamp} ago
</div><div class="post-description">
${comment.body}
</div>
<div class="btn-container"><div class="post-icons">
    <span onclick="sty_updateLike('${commentIndex}', '.comment-like-icon-count')" class="cpointer">
        <svg class="sty_svg-icons" width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11.75 5.81282e-06C12.3385 5.81282e-06 12.8906 0.111985 13.4062 0.335943C13.9219 0.559902 14.3724 0.861985 14.7578 1.24219C15.1432 1.6224 15.4453 2.07292 15.6641 2.59376C15.8828 3.11459 15.9948 3.66667 16 4.25C16 4.81771 15.8932 5.36198 15.6797 5.88282C15.4661 6.40365 15.1589 6.86198 14.7578 7.25782L8 14.0078L1.24219 7.25782C0.846354 6.86198 0.541667 6.40365 0.328125 5.88282C0.114583 5.36198 0.00520833 4.81771 0 4.25C0 3.66667 0.109375 3.11719 0.328125 2.60157C0.546875 2.08594 0.851562 1.63542 1.24219 1.25001C1.63281 0.864589 2.08333 0.559902 2.59375 0.335943C3.10417 0.111985 3.65625 5.81282e-06 4.25 5.81282e-06C4.68229 5.81282e-06 5.06771 0.049485 5.40625 0.148443C5.74479 0.247402 6.05729 0.382818 6.34375 0.554693C6.63021 0.726568 6.90625 0.937506 7.17188 1.18751C7.4375 1.43751 7.71354 1.70573 8 1.99219C8.28646 1.70053 8.5599 1.4323 8.82031 1.18751C9.08073 0.942714 9.35677 0.734381 9.64844 0.562506C9.9401 0.390631 10.2552 0.25261 10.5938 0.148443C10.9323 0.0442766 11.3177 -0.00520252 11.75 5.81282e-06ZM14.0469 6.54688C14.3542 6.23959 14.5885 5.88803 14.75 5.49219C14.9115 5.09636 14.9922 4.6823 14.9922 4.25C14.9922 3.79688 14.9089 3.37501 14.7422 2.98438C14.5755 2.59376 14.3464 2.25261 14.0547 1.96094C13.763 1.66928 13.4193 1.44271 13.0234 1.28126C12.6276 1.1198 12.2031 1.03646 11.75 1.03126C11.3125 1.03126 10.9245 1.09896 10.5859 1.23438C10.2474 1.3698 9.9349 1.55209 9.64844 1.78126C9.36198 2.01042 9.08854 2.26303 8.82812 2.53907C8.56771 2.81511 8.29167 3.10417 8 3.40626C7.71875 3.12501 7.44531 2.84115 7.17969 2.55469C6.91406 2.26823 6.63802 2.01042 6.35156 1.78126C6.0651 1.55209 5.75 1.36459 5.40625 1.21876C5.0625 1.07292 4.67708 1.00001 4.25 1.00001C3.80208 1.00001 3.38021 1.08334 2.98438 1.25001C2.58854 1.41667 2.24479 1.64844 1.95312 1.94532C1.66146 2.24219 1.42969 2.58855 1.25781 2.98438C1.08594 3.38021 1 3.80209 1 4.25C1 4.6823 1.08073 5.09636 1.24219 5.49219C1.40365 5.88803 1.64062 6.23959 1.95312 6.54688L8 12.5938L14.0469 6.54688Z" fill="#9E9C9F"></path></svg><u class="icon-count comment-like-icon-count">${likeInfo.likeCount}</u>
    </span>
    </div></div></span></div>
`; allCards += eachCard;
commentIndex += 1;
                }
            const commentDOM = document.getElementById('sty_comment-container'); if (data.length < sty_PAGE_LIMIT) { const seeMorePostDOM = document.getElementById('sty_feed-more-comments'); seeMorePostDOM.style.display = 'none'; } else { sty_pageNumber += 1; }
            let existingComments = commentDOM.innerHTML; if (sty_pageNumber === 1) existingComments = ''; const latestComments = existingComments + allCards; commentDOM.innerHTML = latestComments;
        }
        const loadPollOptions = async (discussID) => {
            let queryParams = `discussionID=${discussID}&page=1&limit=5`; const data = await fetch(
                `${sty_getBaseUrl()}/api/v2/polls?${queryParams}`,
                sty_getHttpOptions()).then((response) => response.json()); queryParams = `${data[0].pollID}/options`; const dataPoll = await fetch(
                    `${sty_getBaseUrl()}/api/v2/polls/${queryParams}`,
                    sty_getHttpOptions()).then((response) => response.json()); let allPollOptions = ''; for (const option of dataPoll) {
                        const eachPollOption = `
<div class="each-poll-option-box"><div style="display: flex;flex-direction: row;"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="1" y="1" width="22" height="22" rx="4" fill="white" stroke="#00B899" stroke-width="2"></rect></svg><div class="each-poll-option-box-text" style="margin-block-start: 0em;margin-block-end: 0em;">${option.body}</div></div></div>
`; allPollOptions += eachPollOption;
                    }
            allPollOptions += `
<div class="poll-opt-btn"><div class="poll-opt-btn-text">SUBMIT</div></div>
`; document.getElementById('pollOptions').innerHTML = allPollOptions;
        }
        const pollHtmlPlaceHolder = (dataType) => {
            if (dataType !== 'poll') return ''; return (`
<div class="poll-option-box"><div class="poll-option-box-title">Select one option</div><div id="pollOptions"></div></div>
`)
        }; const showAddCommentSection = async (myData, data) => {
            const signedInUserObj = await sty_getSignedInUserData(); if (!signedInUserObj.email) return ''; return (`
<span class="user-post--separator"><img class="post-user-img" src="${myData.photoUrl}"></span><span class="post-text--container"><span><div class="add-comment-heading line-clamp-title">
Add Comment
</div></span><div class="add-comment-box"><div id="post-reply" contenteditable="true" style="min-height:78px;" placeholder="What are your thoughts"></div><div class="post-reply-btn cpointer" onclick="postNewComment(${data.discussionID})"><div class="post-reply-btn-text">Post Reply</div></div></div></span>
`);
        };
        const loadDiscussion = async (discussionID) => {
            document.getElementById('sty_room-feed--container').style.display = 'none'; document.getElementById('sty_discussion-loader').style.display = 'block'; sremoveSuggestionCard();
            document.getElementById('sh-poster-input').value = ''; const myData = await sty_getSignedInUserData(); const discID = discussionID; const data = await fetch(
                `${sty_getBaseUrl()}/api/v2/discussions/${discID}`,
                sty_getHttpOptions()).then((response) => response.json()); const userData = await getUserData(data.insertUserID); const userImg = userData.photoUrl; const timestamp = getFormattedTime(data.dateInserted); const likeInfo = await sty_getLikesInfo(data.discussionID, 'discussions'); cachePostData = {
                    ...data,
                    userImg,
                    timestamp,
                    likeInfo,
                    resourceId: data.discussionID,
                    resourceType: 'discussions',
                }; const addCommentHtml = await showAddCommentSection(myData, data); const newPostHtml = `
<div class="each-post--container"><span class="user-post--separator"><img class="post-user-img" src="${userImg}"></span><span class="post-text--container"><span><div class="post-title line-clamp-title">
${data.name}
</div></span><div class="post-timeline t-ellipsis">
${timestamp} ago
</div><div class="post-description">
${data.body}
</div>
${pollHtmlPlaceHolder(data.type)}
</span></div><div class="hline"></div><div id="disc-post-icon-container" class="post-icons"><div><span onclick="sty_updateLike('0', '.post-like-icon-count')" class="cpointer"><svg class="sty_svg-icons" width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11.75 5.81282e-06C12.3385 5.81282e-06 12.8906 0.111985 13.4062 0.335943C13.9219 0.559902 14.3724 0.861985 14.7578 1.24219C15.1432 1.6224 15.4453 2.07292 15.6641 2.59376C15.8828 3.11459 15.9948 3.66667 16 4.25C16 4.81771 15.8932 5.36198 15.6797 5.88282C15.4661 6.40365 15.1589 6.86198 14.7578 7.25782L8 14.0078L1.24219 7.25782C0.846354 6.86198 0.541667 6.40365 0.328125 5.88282C0.114583 5.36198 0.00520833 4.81771 0 4.25C0 3.66667 0.109375 3.11719 0.328125 2.60157C0.546875 2.08594 0.851562 1.63542 1.24219 1.25001C1.63281 0.864589 2.08333 0.559902 2.59375 0.335943C3.10417 0.111985 3.65625 5.81282e-06 4.25 5.81282e-06C4.68229 5.81282e-06 5.06771 0.049485 5.40625 0.148443C5.74479 0.247402 6.05729 0.382818 6.34375 0.554693C6.63021 0.726568 6.90625 0.937506 7.17188 1.18751C7.4375 1.43751 7.71354 1.70573 8 1.99219C8.28646 1.70053 8.5599 1.4323 8.82031 1.18751C9.08073 0.942714 9.35677 0.734381 9.64844 0.562506C9.9401 0.390631 10.2552 0.25261 10.5938 0.148443C10.9323 0.0442766 11.3177 -0.00520252 11.75 5.81282e-06ZM14.0469 6.54688C14.3542 6.23959 14.5885 5.88803 14.75 5.49219C14.9115 5.09636 14.9922 4.6823 14.9922 4.25C14.9922 3.79688 14.9089 3.37501 14.7422 2.98438C14.5755 2.59376 14.3464 2.25261 14.0547 1.96094C13.763 1.66928 13.4193 1.44271 13.0234 1.28126C12.6276 1.1198 12.2031 1.03646 11.75 1.03126C11.3125 1.03126 10.9245 1.09896 10.5859 1.23438C10.2474 1.3698 9.9349 1.55209 9.64844 1.78126C9.36198 2.01042 9.08854 2.26303 8.82812 2.53907C8.56771 2.81511 8.29167 3.10417 8 3.40626C7.71875 3.12501 7.44531 2.84115 7.17969 2.55469C6.91406 2.26823 6.63802 2.01042 6.35156 1.78126C6.0651 1.55209 5.75 1.36459 5.40625 1.21876C5.0625 1.07292 4.67708 1.00001 4.25 1.00001C3.80208 1.00001 3.38021 1.08334 2.98438 1.25001C2.58854 1.41667 2.24479 1.64844 1.95312 1.94532C1.66146 2.24219 1.42969 2.58855 1.25781 2.98438C1.08594 3.38021 1 3.80209 1 4.25C1 4.6823 1.08073 5.09636 1.24219 5.49219C1.40365 5.88803 1.64062 6.23959 1.95312 6.54688L8 12.5938L14.0469 6.54688Z" fill="#9E9C9F"></path></svg><u class="icon-count post-like-icon-count">${likeInfo.likeCount}</u></span><span><svg class="sty_svg-icons" width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0 0H16V11H5.71094L2 14.7109V11H0V0ZM15 10V1H1V10H3V12.2891L5.28906 10H15Z" fill="#9E9C9F"></path></svg><u class="icon-count">${data.countComments}</u></span></div><div class="cflex"><div id="discussion-edit-btn" class="sharebtn cpointer" onclick="editDiscussion(${discussionID})"><span id="edit-btn-txt">EDIT</span></div><div id="discussion-delete-btn" class="sharebtn cpointer" onclick="deleteDiscussion(${discussionID})"><svg width="20" height="18" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M4.99967 0.666992H8.99967C9.73605 0.666992 10.333 1.26395 10.333 2.00033V2.66699H12.333C13.0694 2.66699 13.6663 3.26395 13.6663 4.00033V5.33366C13.6663 6.07004 13.0694 6.66699 12.333 6.66699H12.2796L11.6663 14.0003C11.6663 14.7367 11.0694 15.3337 10.333 15.3337H3.66634C2.92996 15.3337 2.33301 14.7367 2.33531 14.0557L1.71959 6.66699H1.66634C0.929961 6.66699 0.333008 6.07004 0.333008 5.33366V4.00033C0.333008 3.26395 0.929961 2.66699 1.66634 2.66699H3.66634V2.00033C3.66634 1.26395 4.26329 0.666992 4.99967 0.666992ZM1.66634 4.00033L3.66634 4.00033H10.333L12.333 4.00033V5.33366H1.66634V4.00033ZM3.05733 6.66699H10.9418L10.3353 13.945L10.333 14.0003H3.66634L3.05733 6.66699ZM8.99967 2.00033V2.66699H4.99967V2.00033H8.99967Z" fill="#00B899"></path></svg><span id="delete-btn-txt">DELETE</span></div><div id="discussion-share-btn" class="sharebtn cpointer" onclick="copyLinkToClipBoard()"><svg class="sty_svg-icons" width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13.75 15L15 13.75V17.5H0V5H1.25V16.25H13.75V15ZM12.5 10C11.6667 10 10.8529 10.0814 10.0586 10.2441C9.26432 10.4069 8.49284 10.651 7.74414 10.9766C6.99544 11.3021 6.28906 11.696 5.625 12.1582C4.96094 12.6204 4.33594 13.151 3.75 13.75V12.5C3.75 11.6927 3.85417 10.918 4.0625 10.1758C4.27083 9.43359 4.5638 8.73698 4.94141 8.08594C5.31901 7.4349 5.77474 6.8457 6.30859 6.31836C6.84245 5.79102 7.43164 5.33529 8.07617 4.95117C8.7207 4.56706 9.41732 4.27083 10.166 4.0625C10.9147 3.85417 11.6927 3.75 12.5 3.75V0L19.375 6.875L12.5 13.75V10ZM13.4863 5C13.1217 5 12.7799 5.00326 12.4609 5.00977C12.1419 5.01628 11.8262 5.03581 11.5137 5.06836C11.2012 5.10091 10.8887 5.15951 10.5762 5.24414C10.2637 5.32878 9.93164 5.44271 9.58008 5.58594C9.01367 5.82031 8.48958 6.11979 8.00781 6.48438C7.52604 6.84896 7.0931 7.26237 6.70898 7.72461C6.32487 8.18685 6.0026 8.69141 5.74219 9.23828C5.48177 9.78516 5.28646 10.3581 5.15625 10.957C6.25 10.2279 7.41862 9.67773 8.66211 9.30664C9.9056 8.93555 11.1849 8.75 12.5 8.75H13.75V10.7324L17.6074 6.875L13.75 3.01758V5H13.4863Z" fill="#00B899"></path></svg><span>SHARE</span></div></div></div><div class="hline"></div><div class="total-comments-heading" id="totCmnt">${data.countComments} Comments</div><div id="top-comments"><div id="sty_comment-container"></div><div id="sty_feed-more-comments" onclick="loadTopComments()">SEE MORE COMMENTS</div></div><div id="user-input-container" class="each-post--container">
${addCommentHtml}</div>`;
document.getElementById('loadingDiscussion').innerHTML = newPostHtml; if (data.type === 'poll') await loadPollOptions(data.discussionID);
document.getElementById('sty_room-feed--container').style.display = 'flex'; await loadTopComments(data.discussionID);
document.getElementById('top-comments').style.display = 'block';
document.getElementById('user-input-container').style.display = 'flex';
showAdminButtons(); document.getElementById('sty_discussion-loader').style.display = 'none';
        }
        const copyToClipboard = (text) => {
            var input = document.createElement('input'); input.setAttribute('value', text); document.body.appendChild(input);
            input.select(); var result = document.execCommand('copy'); document.body.removeChild(input);
            return result;
        }
        const originalShareHTML = `
<svg class="sty_svg-icons" width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13.75 15L15 13.75V17.5H0V5H1.25V16.25H13.75V15ZM12.5 10C11.6667 10 10.8529 10.0814 10.0586 10.2441C9.26432 10.4069 8.49284 10.651 7.74414 10.9766C6.99544 11.3021 6.28906 11.696 5.625 12.1582C4.96094 12.6204 4.33594 13.151 3.75 13.75V12.5C3.75 11.6927 3.85417 10.918 4.0625 10.1758C4.27083 9.43359 4.5638 8.73698 4.94141 8.08594C5.31901 7.4349 5.77474 6.8457 6.30859 6.31836C6.84245 5.79102 7.43164 5.33529 8.07617 4.95117C8.7207 4.56706 9.41732 4.27083 10.166 4.0625C10.9147 3.85417 11.6927 3.75 12.5 3.75V0L19.375 6.875L12.5 13.75V10ZM13.4863 5C13.1217 5 12.7799 5.00326 12.4609 5.00977C12.1419 5.01628 11.8262 5.03581 11.5137 5.06836C11.2012 5.10091 10.8887 5.15951 10.5762 5.24414C10.2637 5.32878 9.93164 5.44271 9.58008 5.58594C9.01367 5.82031 8.48958 6.11979 8.00781 6.48438C7.52604 6.84896 7.0931 7.26237 6.70898 7.72461C6.32487 8.18685 6.0026 8.69141 5.74219 9.23828C5.48177 9.78516 5.28646 10.3581 5.15625 10.957C6.25 10.2279 7.41862 9.67773 8.66211 9.30664C9.9056 8.93555 11.1849 8.75 12.5 8.75H13.75V10.7324L17.6074 6.875L13.75 3.01758V5H13.4863Z" fill="#00B899"></path></svg>
SHARE
`; let copyBtnClicked = false; const copyLinkToClipBoard = () => {
            if (copyBtnClicked) return; copyBtnClicked = true; const shareBtnDOM = document.getElementById('discussion-share-btn'); shareBtnDOM.classList.remove('cpointer');
            const curInnerHtml = shareBtnDOM.innerHTML; shareBtnDOM.innerHTML = 'COPIED LINK!'; const curDiscussionLink = window.location.href; copyToClipboard(curDiscussionLink);
            setTimeout(() => { shareBtnDOM.classList.add('cpointer'); shareBtnDOM.innerHTML = originalShareHTML; copyBtnClicked = false; }, 3000);
        };
    </script>
    <script defer>
        const loadBulletin = async () => {
            const categories = await fetch(
                `${sty_getBaseUrl()}/api/v2/categories?maxDepth=2&featured=true&page=1&limit=10`,
                sty_getHttpOptions()).then((response) => response.json()); const discussions = await fetch(
                    `${sty_getBaseUrl()}/api/v2/discussions?discussionID=${featuredDiscussions}&followed=false&pinOrder=first&page=1&limit=10`,
                    sty_getHttpOptions()).then((response) => response.json()); const bulletinData = [
                        { ...discussions[0], link: `/discussion/${discussions[0].discussionID}` },
                        { ...categories[1], link: `/categories/${categories[1].categoryID}` },
                        { ...categories[2], link: `/categories/${categories[2].categoryID}` },
                        { ...discussions[1], link: `/discussion/${discussions[1].discussionID}` },
                        { ...discussions[2], link: `/discussion/${discussions[2].discussionID}` },
                        { ...discussions[3], link: `/discussion/${discussions[3].discussionID}` },
                        { ...discussions[4], link: `/discussion/${discussions[4].discussionID}` },
                        { ...categories[3], link: `/categories/${categories[3].categoryID}` },
                        { ...categories[4], link: `/categories/${categories[4].categoryID}` },
                        { ...categories[0], link: `/categories/${categories[0].categoryID}` },
                        { ...categories[5], link: `/categories/${categories[5].categoryID}` },
                        { ...categories[0], link: `/categories/${categories[0].categoryID}` },
                    ]; bulletinData.map((res, index) => { const card = document.querySelector(`.bulletin--grid--item-${index + 1}`); card.addEventListener("click", () => window.open(res.link, '_self'), false); })
            bulletinData.map((res, index) => { if (index === 0 || index === 6) { const card = document.querySelector(`.bulletin--grid--item-${index + 1} .item--content`); card.innerHTML = `<div class="content--top">${res.name}</div><div class="content--bottom">${getFormattedTime(res.dateInserted)} ago</div>` } else if (index === 1 || index === 2 || index === 7 || index === 8) { const card = document.querySelector(`.bulletin--grid--item-${index + 1}`); card.innerHTML = `<div class="content--top">${res.name}</div><div class="content--bottom">${getFormattedTime(res.dateInserted)} ago</div>` } else if (index === 3 || index === 9) { const card = document.querySelector(`.bulletin--grid--item-${index + 1}`); card.innerHTML = `<div class="content--top">${res.name}</div><div class="content--bottom">${getFormattedTime(res.dateInserted)} ago</div>` } else { const card = document.querySelector(`.bulletin--grid--item-${index + 1}`); card.innerHTML = `<div class="content--top">${res.name}</div><div class="content--bottom">${getFormattedTime(res.dateInserted)} ago</div>` } });
        }
    </script>
    <script defer>
        let myURL = window.location.href; let splitMyURL = myURL.split("/");
        if (splitMyURL.length > 3) {
            if (splitMyURL[3] === "discussion") {
                if (splitMyURL.length > 5) {
                    window.location.href = `/discussion/${splitMyURL[4]}`;
                } else {
                    document.getElementById("cover-story").style.display = "block"; ssetProfileImg();
                    loadDiscussion(splitMyURL[4]); loadBulletin();
                }
            }
        }
    </script>
</div>