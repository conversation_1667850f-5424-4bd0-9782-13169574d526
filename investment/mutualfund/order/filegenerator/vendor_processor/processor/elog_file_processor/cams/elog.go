package cams

/*
Model of the CAMS Elog file. The order in which the data members are defined is in the same order in which
CAMS expect the data members in the file. So, no changes should be made to the order in which the data members are
defined unless there is a change in CAMS model itself.
*/
type Elog struct {
	ARNCode              string
	PANNumber            string
	InvestorName         string
	DocumentType         string
	ImageReferenceNumber string
	ElectronicLog        string
}

func NewElogFileModel() *Elog {
	return &Elog{
		DocumentType: "AOF",
	}
}
