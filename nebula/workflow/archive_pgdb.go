package workflow

import (
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/log"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/emptypb"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	nebulaActivity "github.com/epifi/gamma/api/nebula/activity"
	nebulaWorkflow "github.com/epifi/gamma/api/nebula/workflow"
	"github.com/epifi/gamma/nebula/activity/archivetable"
	"github.com/epifi/be-common/pkg/epifitemporal"

	"github.com/epifi/be-common/pkg/datetime"

	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	nebulaNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/nebula"

	lg "github.com/epifi/be-common/pkg/logger"
)

func ArchivePGDB(ctx workflow.Context, request *nebulaWorkflow.ArchivePgdbRequest) (
	*nebulaWorkflow.ArchivePgdbResponse, error) {

	logger := workflow.GetLogger(ctx)

	if request.GetArchivalConstraint() == nebulaWorkflow.ArchivalConstraint_UNDEFINED {
		// TODO: uncomment this once we have moved to the new version
		// logger.Error("oldest updated at fetch stage failed.", archivetable.LogWithIdentifier(request)...)
		// return nil, epifierrors.ErrInvalidArgument

		// for backward compatibility
		request.ArchivalConstraint = nebulaWorkflow.ArchivalConstraint_UPDATED_AT
	}

	logger.Info("archive postgres table workflow started", archivetable.LogWithIdentifier(request)...)
	var oldestArchivalEligibleTime time.Time
	var err error

	curVersion := workflow.GetVersion(ctx, "ArchivePGDB", workflow.DefaultVersion, 2)

	switch curVersion {
	case workflow.DefaultVersion:
		oldestArchivalEligibleTime, err = getOldestUpdatedAt(ctx, request)
	case 1:
		oldestArchivalEligibleTime, err = getOldestDeletedAt(ctx, request)
	case 2:
		oldestArchivalEligibleTime, err = getOldestArchivalEligibleTime(ctx, request)
	default:
		oldestArchivalEligibleTime, err = getOldestArchivalEligibleTime(ctx, request)
	}

	if err != nil {
		logger.Error("oldest time fetch stage failed.", archivetable.LogWithIdentifier(request)...)
		return nil, err
	}
	logger.Info("oldest time in table fetched", archivetable.LogWithIdentifier(request,
		zap.Time(lg.OLDEST_TIME, oldestArchivalEligibleTime))...)

	// ideally we should not have data before epoch
	// in some cases due to software but while data insertion, the oldest archival is set to 01-01-0001. Catching up from there to current
	// timestamp would take years by this workflow. This check will fail early in that case.
	preventPreEpochVersion := workflow.GetVersion(ctx, "PreventPreEpochVersion", workflow.DefaultVersion, 1)
	if preventPreEpochVersion == 1 {
		if oldestArchivalEligibleTime.Compare(time.Unix(0, 0)) <= 0 {
			errStr := "got oldest archival time before epoch, failing archival. Fix oldest archival date before trying again"
			logger.Error(errStr, archivetable.LogWithIdentifier(request))
			return nil, epifitemporal.NewPermanentError(fmt.Errorf(errStr))
		}
	}

	tillTime, err := archiveTable(ctx, request, oldestArchivalEligibleTime, logger)
	if err != nil {
		logger.Error("archive table stage failed.", archivetable.LogWithIdentifier(request)...)
		return nil, err
	}
	logger.Info("archive table workflow completed", archivetable.LogWithIdentifier(request, zap.Time(lg.END_DATE, tillTime))...)

	response := nebulaWorkflow.ArchivePgdbResponse{
		ResponseHeader: nil,
	}

	return &response, nil
}

func getOldestArchivalEligibleTime(ctx workflow.Context, request *nebulaWorkflow.ArchivePgdbRequest) (time.Time, error) {
	switch request.GetArchivalConstraint() {
	case nebulaWorkflow.ArchivalConstraint_DELETED_AT:
		return getOldestDeletedAt(ctx, request)
	case nebulaWorkflow.ArchivalConstraint_UPDATED_AT, nebulaWorkflow.ArchivalConstraint_UNDEFINED:
		return getOldestUpdatedAt(ctx, request)
	case nebulaWorkflow.ArchivalConstraint_DELETED_AT_UNIX:
		return getOldestDeletedAtUnix(ctx, request)
	default:
		return time.Time{}, fmt.Errorf("no oldest archival eligibile time getter implemented for archival constraint: %s", request.GetArchivalConstraint())
	}
}

// Fetches the oldest updatedAt from the table.
func getOldestUpdatedAt(ctx workflow.Context, request *nebulaWorkflow.ArchivePgdbRequest) (time.Time, error) {

	firstUpdatedAtActivityRequest := nebulaActivity.GetFirstUpdatedAtActivityRequest{
		DbConfig:            request.DbConfig,
		TableName:           request.TableName,
		UpdatedAtColumnName: request.UpdatedAtColumnName,
	}

	var firstUpdatedAtActivityResponse nebulaActivity.GetFirstUpdatedAtActivityResponse
	err := activityPkg.Execute(ctx, nebulaNs.FirstUpdatedAt, &firstUpdatedAtActivityResponse, &firstUpdatedAtActivityRequest)
	if err != nil {
		return time.Time{}, err
	}

	updatedAt := firstUpdatedAtActivityResponse.FirstUpdatedAt.AsTime()

	return updatedAt.In(datetime.IST), nil
}

// Fetches the oldest deletedAt from the table.
func getOldestDeletedAt(ctx workflow.Context, request *nebulaWorkflow.ArchivePgdbRequest) (time.Time, error) {
	oldestDeletedAtActivityRequest := nebulaActivity.GetOldestDeletedAtActivityRequest{
		DbConfig:            request.GetDbConfig(),
		TableName:           request.GetTableName(),
		DeletedAtColumnName: request.GetDeletedAtColumnName(),
	}

	var oldestDeletedAtActivityResponse nebulaActivity.GetOldestDeletedAtActivityResponse
	err := activityPkg.Execute(ctx, nebulaNs.OldestDeletedAt, &oldestDeletedAtActivityResponse, &oldestDeletedAtActivityRequest)
	if err != nil {
		return time.Time{}, err
	}

	deletedAt := oldestDeletedAtActivityResponse.GetOldestDeletedAt().AsTime()

	return deletedAt.In(datetime.IST), nil
}

// Fetches the oldest deleted at unix from the table
func getOldestDeletedAtUnix(ctx workflow.Context, request *nebulaWorkflow.ArchivePgdbRequest) (time.Time, error) {
	oldestDeletedAtUnixActivityRequest := nebulaActivity.GetOldestDeletedAtUnixActivityRequest{
		DbConfig:                request.GetDbConfig(),
		TableName:               request.GetTableName(),
		DeletedAtUnixColumnName: request.GetDeletedAtUnixColumnName(),
	}

	var oldestDeletedAtUnixActivityResponse nebulaActivity.GetOldestDeletedAtUnixActivityResponse
	err := activityPkg.Execute(ctx, nebulaNs.OldestDeletedAtUnix, &oldestDeletedAtUnixActivityResponse, &oldestDeletedAtUnixActivityRequest)
	if err != nil {
		return time.Time{}, err
	}

	deletedAtUnix := oldestDeletedAtUnixActivityResponse.GetOldestDeletedAtUnix().AsTime()
	return deletedAtUnix.In(datetime.IST), nil
}

//nolint:funlen
func archiveTable(ctx workflow.Context, request *nebulaWorkflow.ArchivePgdbRequest, oldestTime time.Time,
	logger log.Logger) (time.Time, error) {

	startDate := datetime.GetTimeAtStartOfTheDay(oldestTime)
	tillDate := datetime.GetTimeAtStartOfTheDay(
		workflow.Now(ctx).In(datetime.IST).
			Add(-1 * request.RetentionTime.AsDuration()),
	)

	termSignal := epifitemporal.NewSignalChannel(ctx, nebulaNs.TerminatePGDBArchivalSignal, &emptypb.Empty{})

	logger.Info("going to archive data between",
		archivetable.LogWithIdentifier(request, zap.Time(lg.START_DATE, startDate), zap.Time(lg.END_DATE, tillDate))...)

	for date := startDate; date.Before(tillDate); date = date.Add(24 * time.Hour) {

		curVersion := workflow.GetVersion(ctx, "wf-continue-as-new-support", workflow.DefaultVersion, 1)

		if curVersion == 1 {
			// In case history length is exceeded. Continue as a new workflow
			wfInfo := workflow.GetInfo(ctx)
			if wfInfo.GetCurrentHistoryLength() > 8000 { // Temporal warns at 10000, kept 8000 to be safe side
				logger.Info("Continuing as a new workflow..", archivetable.LogWithIdentifier(request, zap.Time(lg.DATE, date))...)

				// no need to modify request as wf will again calculate the oldest archival eligible time
				return time.Time{}, workflow.NewContinueAsNewError(ctx, nebulaNs.BackupTable, request)
			}
		}

		curVersion = workflow.GetVersion(ctx, "incorrect-batch-delay-fix", workflow.DefaultVersion, 1)
		archiveTableRequest := nebulaActivity.BackupDataActivityRequest{
			DbConfig:                request.GetDbConfig(),
			TableName:               request.GetTableName(),
			UpdatedAtColumnName:     request.GetUpdatedAtColumnName(),
			DeletedAtColumnName:     request.GetDeletedAtColumnName(),
			DeletedAtUnixColumnName: request.GetDeletedAtUnixColumnName(),
			BatchDuration:           request.GetBatchDuration(),
			BatchDelay:              request.GetBatchDuration(),
			BackupDate:              timestamp.New(date),
			ArchivalConstraint:      request.GetArchivalConstraint(),
		}

		if curVersion >= 1 {
			archiveTableRequest.BatchDelay = request.GetBatchDelay()
		}

		termReceived := termSignal.Unwrap().ReceiveAsync(&emptypb.Empty{})
		if termReceived {
			return date, nil
		}

		var archiveTableResponse nebulaActivity.BackupDataActivityResponse
		err := activityPkg.Execute(ctx, nebulaNs.BackupTable, &archiveTableResponse, &archiveTableRequest)
		if err != nil {
			logger.Error("backup table task failed.",
				archivetable.LogWithIdentifier(request, zap.Time(lg.DATE, date), zap.Error(err))...)

			return time.Time{}, errors.Wrap(err, "archive activity failed.")
		}

		termReceived = termSignal.Unwrap().ReceiveAsync(&emptypb.Empty{})
		if termReceived {
			return date, nil
		}

		purgeTableRequest := nebulaActivity.PurgeDataActivityRequest{
			DbConfig:                request.GetDbConfig(),
			TableName:               request.GetTableName(),
			UpdatedAtColumnName:     request.GetUpdatedAtColumnName(),
			DeletedAtColumnName:     request.GetDeletedAtColumnName(),
			DeletedAtUnixColumnName: request.GetDeletedAtUnixColumnName(),
			BatchDuration:           request.GetBatchDuration(),
			BatchDelay:              request.GetBatchDelay(),
			PurgeDate:               timestamp.New(date),
			ArchivalConstraint:      request.GetArchivalConstraint(),
		}

		var purgeTableResponse nebulaActivity.PurgeDataActivityResponse
		err = activityPkg.Execute(ctx, nebulaNs.PurgeTable, &purgeTableResponse, &purgeTableRequest)
		if err != nil {
			logger.Error("purge table task failed.",
				archivetable.LogWithIdentifier(request, zap.Time(lg.DATE, date))...)

			return time.Time{}, errors.Wrap(err, "purge activity failed.")
		}
	}

	return tillDate, nil
}
