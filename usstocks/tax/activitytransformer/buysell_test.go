package activitytransformer

import (
	"context"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/shopspring/decimal"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	ussTaxPb "github.com/epifi/gamma/api/usstocks/tax"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"
)

func TestBuySellProcessor_ProcessActivity(t *testing.T) {
	partialFillNegActivity := &vgStocksPb.AccountActivity{
		Activity: &vgStocksPb.AccountActivity_TradeActivity{
			TradeActivity: &vgStocksPb.TradeActivity{
				Id:                   "id1",
				AccountId:            "acct1",
				TransactionTime:      timestampPb.New(time.Date(2023, 05, 06, 11, 56, 27, 0, datetime.EST5EDT)),
				TradeActivitySubType: vgStocksPb.TradeActivitySubType_TRADE_ACTIVITY_SUB_TYPE_PARTIAL_FILL,
				PerSharePrice:        &moneyPb.Money{CurrencyCode: "USD", Units: 52},
				QtyExecuted:          -0.25,
				Side:                 vgStocksPb.Side_SIDE_BUY,
				Symbol:               "APPL",
				QtyRemaining:         2,
				OrderId:              "order1",
				QtyCumulative:        2.5,
				OrderStatus:          vgStocksPb.OrderStatus_ORDER_STATUS_FILLED_IN_PROGRESS,
				TradeActivityType:    vgStocksPb.TradeActivityType_TRADE_ACTIVITY_TYPE_FILL,
			},
		},
	}
	tests := []struct {
		name    string
		req     *ProcessActivityRequest
		want    func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse
		wantErr bool
	}{
		{
			name: "Successfully process buy order Activity",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_TradeActivity{
						TradeActivity: &vgStocksPb.TradeActivity{
							Id:                   "id1",
							AccountId:            "acct1",
							TransactionTime:      timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
							TradeActivitySubType: vgStocksPb.TradeActivitySubType_TRADE_ACTIVITY_SUB_TYPE_PARTIAL_FILL,
							PerSharePrice:        &moneyPb.Money{CurrencyCode: "USD", Units: 54, Nanos: *********},
							QtyExecuted:          2.5,
							Side:                 vgStocksPb.Side_SIDE_BUY,
							Symbol:               "APPL",
							QtyRemaining:         2,
							OrderId:              "order1",
							QtyCumulative:        2.5,
							OrderStatus:          vgStocksPb.OrderStatus_ORDER_STATUS_FILLED_IN_PROGRESS,
							TradeActivityType:    vgStocksPb.TradeActivityType_TRADE_ACTIVITY_TYPE_FILL,
						},
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				qty := decimal.NewFromFloat(2.5)
				return &ProcessActivityResponse{
					Transactions: []*ussTaxPb.TransactionWrapper{
						{
							Transaction: &ussTaxPb.Transaction{
								Symbol:           "APPL",
								Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 135, Nanos: *********},
								ExecutedAt:       timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
								TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
								ParentActivities: []*vgStocksPb.AccountActivity{activity},
								Expense:          money.ZeroUSD(),
							},
							Quantity: qty,
						},
					},
				}
			},
		},
		{
			name: "Successfully process buy order partial fill activity merging with existing order",
			req: &ProcessActivityRequest{
				Activity: partialFillNegActivity,
				Transactions: []*ussTaxPb.TransactionWrapper{
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol:          "APPL",
							Amount:          &moneyPb.Money{CurrencyCode: "USD", Units: 135, Nanos: *********},
							ExecutedAt:      timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
							TransactionType: ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
							ParentActivities: []*vgStocksPb.AccountActivity{{
								Activity: &vgStocksPb.AccountActivity_TradeActivity{
									TradeActivity: &vgStocksPb.TradeActivity{
										Id:                   "id1",
										AccountId:            "acct1",
										TransactionTime:      timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
										TradeActivitySubType: vgStocksPb.TradeActivitySubType_TRADE_ACTIVITY_SUB_TYPE_FILL,
										PerSharePrice:        &moneyPb.Money{CurrencyCode: "USD", Units: 54, Nanos: *********},
										QtyExecuted:          2.5,
										Side:                 vgStocksPb.Side_SIDE_BUY,
										Symbol:               "APPL",
										QtyRemaining:         2,
										OrderId:              "order1",
										QtyCumulative:        2.5,
										OrderStatus:          vgStocksPb.OrderStatus_ORDER_STATUS_FILLED_IN_PROGRESS,
										TradeActivityType:    vgStocksPb.TradeActivityType_TRADE_ACTIVITY_TYPE_FILL,
									},
								},
							}},
							Expense: money.ZeroUSD(),
						},
						Quantity: decimal.NewFromFloat(2.5),
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				qty := decimal.NewFromFloat(2.25)
				return &ProcessActivityResponse{
					Transactions: []*ussTaxPb.TransactionWrapper{
						{
							Transaction: &ussTaxPb.Transaction{
								Symbol:          "APPL",
								Amount:          &moneyPb.Money{CurrencyCode: "USD", Units: 122, Nanos: *********},
								ExecutedAt:      timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
								TransactionType: ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
								ParentActivities: []*vgStocksPb.AccountActivity{{
									Activity: &vgStocksPb.AccountActivity_TradeActivity{
										TradeActivity: &vgStocksPb.TradeActivity{
											Id:                   "id1",
											AccountId:            "acct1",
											TransactionTime:      timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
											TradeActivitySubType: vgStocksPb.TradeActivitySubType_TRADE_ACTIVITY_SUB_TYPE_FILL,
											PerSharePrice:        &moneyPb.Money{CurrencyCode: "USD", Units: 54, Nanos: *********},
											QtyExecuted:          2.5,
											Side:                 vgStocksPb.Side_SIDE_BUY,
											Symbol:               "APPL",
											QtyRemaining:         2,
											OrderId:              "order1",
											QtyCumulative:        2.5,
											OrderStatus:          vgStocksPb.OrderStatus_ORDER_STATUS_FILLED_IN_PROGRESS,
											TradeActivityType:    vgStocksPb.TradeActivityType_TRADE_ACTIVITY_TYPE_FILL,
										},
									},
								}, activity},
								Expense: money.ZeroUSD(),
							},
							Quantity: qty,
						},
					},
				}
			},
		},
		{
			name: "No parent activities found in past txn",
			req: &ProcessActivityRequest{
				Activity: partialFillNegActivity,
				Transactions: []*ussTaxPb.TransactionWrapper{
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol:           "APPL",
							Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 135, Nanos: *********},
							ExecutedAt:       timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
							TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
							ParentActivities: nil,
							Expense:          money.ZeroUSD(),
						},
						Quantity: decimal.NewFromFloat(2.5),
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				return nil
			},
			wantErr: true,
		},
		{
			name: "No order id found in past buy txn",
			req: &ProcessActivityRequest{
				Activity: partialFillNegActivity,
				Transactions: []*ussTaxPb.TransactionWrapper{
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol:           "APPL",
							Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 135, Nanos: *********},
							ExecutedAt:       timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
							TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
							ParentActivities: []*vgStocksPb.AccountActivity{{}},
							Expense:          money.ZeroUSD(),
						},
						Quantity: decimal.NewFromFloat(2.5),
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				return nil
			},
			wantErr: true,
		},
		{
			name: "No past order found should lead to new txn creation",
			req: &ProcessActivityRequest{
				Activity: partialFillNegActivity,
				Transactions: []*ussTaxPb.TransactionWrapper{
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol:          "APPL",
							Amount:          &moneyPb.Money{CurrencyCode: "USD", Units: 135, Nanos: *********},
							ExecutedAt:      timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
							TransactionType: ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
							ParentActivities: []*vgStocksPb.AccountActivity{{
								Activity: &vgStocksPb.AccountActivity_TradeActivity{
									TradeActivity: &vgStocksPb.TradeActivity{
										OrderId: "order2",
									},
								},
							}},
							Expense: money.ZeroUSD(),
						},
						Quantity: decimal.NewFromFloat(2.5),
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				return &ProcessActivityResponse{
					Transactions: []*ussTaxPb.TransactionWrapper{
						{
							Transaction: &ussTaxPb.Transaction{
								Symbol:          "APPL",
								Amount:          &moneyPb.Money{CurrencyCode: "USD", Units: 135, Nanos: *********},
								ExecutedAt:      timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
								TransactionType: ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
								ParentActivities: []*vgStocksPb.AccountActivity{{
									Activity: &vgStocksPb.AccountActivity_TradeActivity{
										TradeActivity: &vgStocksPb.TradeActivity{
											OrderId: "order2",
										},
									},
								}},
								Expense: money.ZeroUSD(),
							},
							Quantity: decimal.NewFromFloat(2.5),
						},
						{
							Transaction: &ussTaxPb.Transaction{
								Symbol:           "APPL",
								Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: -13},
								ExecutedAt:       timestampPb.New(time.Date(2023, 05, 06, 11, 56, 27, 0, datetime.EST5EDT)),
								TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
								ParentActivities: []*vgStocksPb.AccountActivity{activity},
								Expense:          money.ZeroUSD(),
							},
							Quantity: decimal.NewFromFloat(-0.25),
						},
					},
				}
			},
		},
		{
			name: "No past order found within 36 hour of current activity should lead to new txn creation",
			req: &ProcessActivityRequest{
				Activity: partialFillNegActivity,
				Transactions: []*ussTaxPb.TransactionWrapper{
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol:          "APPL",
							Amount:          &moneyPb.Money{CurrencyCode: "USD", Units: 135, Nanos: *********},
							ExecutedAt:      timestampPb.New(time.Date(2023, 05, 04, 11, 55, 27, 0, datetime.EST5EDT)),
							TransactionType: ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
							ParentActivities: []*vgStocksPb.AccountActivity{{
								Activity: &vgStocksPb.AccountActivity_TradeActivity{
									TradeActivity: &vgStocksPb.TradeActivity{
										OrderId: "order1",
									},
								},
							}},
							Expense: money.ZeroUSD(),
						},
						Quantity: decimal.NewFromFloat(2.5),
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				return &ProcessActivityResponse{
					Transactions: []*ussTaxPb.TransactionWrapper{
						{
							Transaction: &ussTaxPb.Transaction{
								Symbol:          "APPL",
								Amount:          &moneyPb.Money{CurrencyCode: "USD", Units: 135, Nanos: *********},
								ExecutedAt:      timestampPb.New(time.Date(2023, 05, 04, 11, 55, 27, 0, datetime.EST5EDT)),
								TransactionType: ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
								ParentActivities: []*vgStocksPb.AccountActivity{{
									Activity: &vgStocksPb.AccountActivity_TradeActivity{
										TradeActivity: &vgStocksPb.TradeActivity{
											OrderId: "order1",
										},
									},
								}},
								Expense: money.ZeroUSD(),
							},
							Quantity: decimal.NewFromFloat(2.5),
						},
						{
							Transaction: &ussTaxPb.Transaction{
								Symbol:           "APPL",
								Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: -13},
								ExecutedAt:       timestampPb.New(time.Date(2023, 05, 06, 11, 56, 27, 0, datetime.EST5EDT)),
								TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
								ParentActivities: []*vgStocksPb.AccountActivity{activity},
								Expense:          money.ZeroUSD(),
							},
							Quantity: decimal.NewFromFloat(-0.25),
						},
					},
				}
			},
		},
		{
			name: "Successfully process sell order Activity",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_TradeActivity{
						TradeActivity: &vgStocksPb.TradeActivity{
							Id:                   "id1",
							AccountId:            "acct1",
							TransactionTime:      timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
							TradeActivitySubType: vgStocksPb.TradeActivitySubType_TRADE_ACTIVITY_SUB_TYPE_FILL,
							PerSharePrice:        &moneyPb.Money{CurrencyCode: "USD", Units: 54, Nanos: *********},
							QtyExecuted:          2.5,
							Side:                 vgStocksPb.Side_SIDE_SELL,
							Symbol:               "APPL",
							OrderStatus:          vgStocksPb.OrderStatus_ORDER_STATUS_FILLED,
							TradeActivityType:    vgStocksPb.TradeActivityType_TRADE_ACTIVITY_TYPE_FILL,
						},
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				qty := decimal.NewFromFloat(2.5)
				return &ProcessActivityResponse{
					Transactions: []*ussTaxPb.TransactionWrapper{
						{
							Transaction: &ussTaxPb.Transaction{
								Symbol:           "APPL",
								Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 135, Nanos: *********},
								ExecutedAt:       timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
								TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_SELL,
								ParentActivities: []*vgStocksPb.AccountActivity{activity},
								Expense:          money.ZeroUSD(),
							},
							Quantity: qty,
						},
					},
				}
			},
		},
		{
			name: "Inconsequential order should lead to a no op transaction",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_TradeActivity{
						TradeActivity: &vgStocksPb.TradeActivity{
							Id:                   "id1",
							AccountId:            "acct1",
							TransactionTime:      timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
							TradeActivitySubType: vgStocksPb.TradeActivitySubType_TRADE_ACTIVITY_SUB_TYPE_UNSPECIFIED,
							PerSharePrice:        &moneyPb.Money{CurrencyCode: "USD", Units: 54, Nanos: *********},
							QtyExecuted:          2.5,
							Side:                 vgStocksPb.Side_SIDE_SELL,
							Symbol:               "APPL",
							OrderStatus:          vgStocksPb.OrderStatus_ORDER_STATUS_CANCELED,
							TradeActivityType:    vgStocksPb.TradeActivityType_TRADE_ACTIVITY_TYPE_UNSPECIFIED,
						},
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				qty := decimal.NewFromFloat(2.5)
				return &ProcessActivityResponse{
					Transactions: []*ussTaxPb.TransactionWrapper{
						{
							Transaction: &ussTaxPb.Transaction{
								Symbol:           "APPL",
								Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 135, Nanos: *********},
								ExecutedAt:       timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
								TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_NO_OP,
								ParentActivities: []*vgStocksPb.AccountActivity{activity},
								Expense:          money.ZeroUSD(),
							},
							Quantity: qty,
						},
					},
				}
			},
		},
		{
			name: "Negative quantity should lead to error",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_TradeActivity{
						TradeActivity: &vgStocksPb.TradeActivity{
							Id:                   "id1",
							AccountId:            "acct1",
							TransactionTime:      timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
							TradeActivitySubType: vgStocksPb.TradeActivitySubType_TRADE_ACTIVITY_SUB_TYPE_FILL,
							PerSharePrice:        &moneyPb.Money{CurrencyCode: "USD", Units: 54, Nanos: *********},
							QtyExecuted:          -1,
							Side:                 vgStocksPb.Side_SIDE_SELL,
							Symbol:               "APPL",
							OrderStatus:          vgStocksPb.OrderStatus_ORDER_STATUS_FILLED,
							TradeActivityType:    vgStocksPb.TradeActivityType_TRADE_ACTIVITY_TYPE_FILL,
						},
					},
				},
			},
			want:    func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse { return nil },
			wantErr: true,
		},
		{
			name: "Negative price should lead to error",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_TradeActivity{
						TradeActivity: &vgStocksPb.TradeActivity{
							Id:                   "id1",
							AccountId:            "acct1",
							TransactionTime:      timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
							TradeActivitySubType: vgStocksPb.TradeActivitySubType_TRADE_ACTIVITY_SUB_TYPE_FILL,
							PerSharePrice:        &moneyPb.Money{CurrencyCode: "USD", Units: -50},
							QtyExecuted:          1,
							Side:                 vgStocksPb.Side_SIDE_SELL,
							Symbol:               "APPL",
							OrderStatus:          vgStocksPb.OrderStatus_ORDER_STATUS_FILLED,
							TradeActivityType:    vgStocksPb.TradeActivityType_TRADE_ACTIVITY_TYPE_FILL,
						},
					},
				},
			},
			want:    func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse { return nil },
			wantErr: true,
		},
		{
			name: "Unhandled trade Activity sub type should lead to error",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_TradeActivity{
						TradeActivity: &vgStocksPb.TradeActivity{
							Id:                   "id1",
							AccountId:            "acct1",
							TransactionTime:      timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
							TradeActivitySubType: vgStocksPb.TradeActivitySubType_TRADE_ACTIVITY_SUB_TYPE_UNSPECIFIED,
							PerSharePrice:        &moneyPb.Money{CurrencyCode: "USD", Units: 54, Nanos: *********},
							QtyExecuted:          2.5,
							Side:                 vgStocksPb.Side_SIDE_BUY,
							Symbol:               "APPL",
							OrderStatus:          vgStocksPb.OrderStatus_ORDER_STATUS_FILLED_IN_PROGRESS,
							TradeActivityType:    vgStocksPb.TradeActivityType_TRADE_ACTIVITY_TYPE_FILL,
						},
					},
				},
			},
			want:    func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse { return nil },
			wantErr: true,
		},
		{
			name: "Unhandled order side should lead to error",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_TradeActivity{
						TradeActivity: &vgStocksPb.TradeActivity{
							Id:                   "id1",
							AccountId:            "acct1",
							TransactionTime:      timestampPb.New(time.Date(2023, 05, 06, 11, 55, 27, 0, datetime.EST5EDT)),
							TradeActivitySubType: vgStocksPb.TradeActivitySubType_TRADE_ACTIVITY_SUB_TYPE_FILL,
							PerSharePrice:        &moneyPb.Money{CurrencyCode: "USD", Units: 54, Nanos: *********},
							QtyExecuted:          2.5,
							Side:                 vgStocksPb.Side_SIDE_UNSPECIFIED,
							Symbol:               "APPL",
							OrderStatus:          vgStocksPb.OrderStatus_ORDER_STATUS_FILLED_IN_PROGRESS,
							TradeActivityType:    vgStocksPb.TradeActivityType_TRADE_ACTIVITY_TYPE_FILL,
						},
					},
				},
			},
			want:    func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse { return nil },
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			b := NewBuySellProcessor()
			got, err := b.ProcessActivity(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessActivity() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want(tt.req.Activity), protocmp.Transform()); diff != "" {
				t.Errorf("ProcessActivity() diff=%v", diff)
			}
		})
	}
}
