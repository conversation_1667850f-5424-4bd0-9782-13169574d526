package activity_test

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	datePb "google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/testing/protocmp"

	activityPb "github.com/epifi/gamma/api/creditreportv2/activity"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/be-common/pkg/epifitemporal"
	creditreportNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/creditreport"
)

func TestProcessor_GetUserPanDobStatus(t *testing.T) {
	tests := []struct {
		name      string
		req       *activityPb.GetUserPanDobStatusRequest
		wantMocks func(mocks *mockedDependencies)
		wantRes   *activityPb.GetUserPanDobStatusResponse
		assertErr func(err error) bool
		wantErr   bool
	}{
		{
			name: "permanent failure, actor_id is empty in request",
			req: &activityPb.GetUserPanDobStatusRequest{
				ActorId: "",
			},
			wantMocks: func(mocks *mockedDependencies) {},
			wantRes:   nil,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			wantErr: true,
		},
		{
			name: "success pan-dob found for actor",
			req: &activityPb.GetUserPanDobStatusRequest{
				ActorId: "actor-1",
			},
			wantMocks: func(mocks *mockedDependencies) {
				mocks.userProcessor.EXPECT().GetUserByActorId(gomock.Any(), "actor-1").Return(&user.User{
					Profile: &user.Profile{
						DateOfBirth: &datePb.Date{
							Year:  2010,
							Month: 1,
							Day:   1,
						},
						PAN: "**********",
					},
				}, nil)
			},
			wantRes: &activityPb.GetUserPanDobStatusResponse{
				IsPanDobPresent: true,
			},
			assertErr: nil,
			wantErr:   false,
		},
		{
			name: "success, pan-dob not found for actor",
			req: &activityPb.GetUserPanDobStatusRequest{
				ActorId: "actor-1",
			},
			wantMocks: func(mocks *mockedDependencies) {
				mocks.userProcessor.EXPECT().GetUserByActorId(gomock.Any(), "actor-1").Return(&user.User{
					Profile: &user.Profile{
						PAN: "",
					},
				}, nil)
			},
			wantRes: &activityPb.GetUserPanDobStatusResponse{
				IsPanDobPresent: false,
			},
			assertErr: nil,
			wantErr:   false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			act, md, assertTest := newProcessorWithMocks(t)
			defer assertTest()
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(act)

			tt.wantMocks(md)
			got, err := env.ExecuteActivity(creditreportNs.GetUserPanDobStatus, tt.req)

			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserPanDobStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && !tt.assertErr(err) {
				t.Errorf("GetUserPanDobStatus() error = %v, assertion failed", err)
				return
			}
			var gotRes *activityPb.GetUserPanDobStatusResponse
			if got != nil {
				if err1 := got.Get(&gotRes); err1 != nil {
					t.Errorf("GetUserPanDobStatus() error extracting response : %v", err1)
					return
				}
			}
			if diff := cmp.Diff(gotRes, tt.wantRes, protocmp.Transform()); (gotRes != nil || tt.wantRes != nil) && diff != "" {
				t.Errorf("GetUserPanDobStatus() \nwantRes=%v \ngotRes=%v \n diff=%s", tt.wantRes, gotRes, diff)
				return
			}
		})
	}
}

func TestProcessor_CheckPanDobPresent(t *testing.T) {
	tests := []struct {
		name      string
		req       *activityPb.CheckPanDobPresentRequest
		wantMocks func(mocks *mockedDependencies)
		wantRes   *activityPb.CheckPanDobPresentResponse
		assertErr func(err error) bool
		wantErr   bool
	}{
		{
			name: "permanent failure, actor_id is empty in request",
			req: &activityPb.CheckPanDobPresentRequest{
				ActorId: "",
			},
			wantMocks: func(mocks *mockedDependencies) {},
			wantRes:   nil,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
			wantErr: true,
		},
		{
			name: "success pan-dob found for actor",
			req: &activityPb.CheckPanDobPresentRequest{
				ActorId: "actor-1",
			},
			wantMocks: func(mocks *mockedDependencies) {
				mocks.userProcessor.EXPECT().GetUserByActorId(gomock.Any(), "actor-1").Return(&user.User{
					Profile: &user.Profile{
						DateOfBirth: &datePb.Date{
							Year:  2010,
							Month: 1,
							Day:   1,
						},
						PAN: "**********",
					},
				}, nil)
			},
			wantRes:   &activityPb.CheckPanDobPresentResponse{},
			assertErr: nil,
			wantErr:   false,
		},
		{
			name: "transient failure, pan-dob not found for actor",
			req: &activityPb.CheckPanDobPresentRequest{
				ActorId: "actor-1",
			},
			wantMocks: func(mocks *mockedDependencies) {
				mocks.userProcessor.EXPECT().GetUserByActorId(gomock.Any(), "actor-1").Return(&user.User{
					Profile: &user.Profile{
						PAN: "",
					},
				}, nil)
			},
			wantRes:   nil,
			assertErr: epifitemporal.IsRetryableError,
			wantErr:   true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			act, md, assertTest := newProcessorWithMocks(t)
			defer assertTest()
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(act)

			tt.wantMocks(md)
			got, err := env.ExecuteActivity(creditreportNs.CheckPanDobPresent, tt.req)

			if (err != nil) != tt.wantErr {
				t.Errorf("CheckPanDobPresent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && !tt.assertErr(err) {
				t.Errorf("CheckPanDobPresent() error = %v, assertion failed", err)
				return
			}
			var gotRes *activityPb.CheckPanDobPresentResponse
			if got != nil {
				if err1 := got.Get(&gotRes); err1 != nil {
					t.Errorf("CheckPanDobPresent() error extracting response : %v", err1)
					return
				}
			}
			if diff := cmp.Diff(gotRes, tt.wantRes, protocmp.Transform()); (gotRes != nil || tt.wantRes != nil) && diff != "" {
				t.Errorf("CheckPanDobPresent() \nwantRes=%v \ngotRes=%v \n diff=%s", tt.wantRes, gotRes, diff)
				return
			}
		})
	}
}
