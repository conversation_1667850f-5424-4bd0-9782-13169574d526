package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/cfg"
)

const (
	RudderWriteKey = "RudderWriteKey"
	dbCredentials  = "PgdbCredentials"
)

var (
	once       sync.Once
	config     *Config
	err        error
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to load config")
	}

	return config, nil
}

func loadConfig() (*Config, error) {
	conf := &Config{}
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()

	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, cfg.CREDIT_REPORT_SERVICE)

	if err != nil {
		return nil, fmt.Errorf("failed to load dynamic config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to refresh dymanic config: %w", err)
	}

	otherSecrets := conf.Secrets.Ids
	secretsWithEpifiCerts := cfg.AddCrdbSslCertSecretIds(conf.EpifiDb, otherSecrets)
	secretsWithFeatureEngineeringCerts := cfg.AddPgdbSslCertSecretIds(conf.FeatureEngineeringDb, secretsWithEpifiCerts)
	keyToSecret, keyToSecretErr := cfg.LoadSecrets(secretsWithFeatureEngineeringCerts, conf.Application.Environment, conf.AWS.Region)
	if keyToSecretErr != nil {
		return nil, fmt.Errorf("failed to load secrets %w", keyToSecretErr)
	}

	if configUpdateErr := updateDefaultConfig(conf, keyToSecret); configUpdateErr != nil {
		return nil, configUpdateErr
	}
	return conf, nil

}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	// add crdb endpoint
	crdbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c.EpifiDb, crdbServerEndpoint)

	// add pg endpoint
	pgdbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_RDS)
	cfg.UpdateDbEndpointInConfig(c.FeatureEngineeringDb, pgdbServerEndpoint)

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	cfg.UpdateSecretValues(c.EpifiDb, c.Secrets, keyToSecret)

	cfg.UpdatePGDBSecretValuesV2(c.FeatureEngineeringDb, keyToSecret)
	// update db username and password in db config
	// if env is development keys are not fetched from SM hence update from yml file itself
	if c.Application.Environment == cfg.TestEnv || c.Application.Environment == cfg.DevelopmentEnv {
		cfg.UpdateDbUsernamePasswordInConfig(c.FeatureEngineeringDb, c.Secrets.Ids[dbCredentials])
		return nil
	}
	if _, ok := keyToSecret[dbCredentials]; !ok {
		return fmt.Errorf("db username password not fetched from secrets manager")
	}
	cfg.UpdateDbUsernamePasswordInConfig(c.FeatureEngineeringDb, keyToSecret[dbCredentials])

	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcPort = intVal
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		c.EpifiDb.Host = val
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

// TODO(kunal): Add validate tags later and verify config struct post unmarshalling
// Constraints with respect to dynamic config generate tool:
// 1. Struct names have to start with uppercase letters
// 2. Struct variables need to be pointers

//go:generate conf_gen github.com/epifi/gamma/creditreportv2/config Config
type Config struct {
	Application                             *Application
	Server                                  *Server
	EpifiDb                                 *cfg.DB
	FeatureEngineeringDb                    *cfg.DB
	AWS                                     *Aws
	RudderStack                             *cfg.RudderStackBroker
	Secrets                                 *cfg.Secrets
	Flags                                   *Flags
	CreditReportPresencePublisher           *cfg.SqsPublisher
	CreditReportPresenceSubscriber          *cfg.SqsSubscriber `dynamic:"true"`
	CreditReportVerificationPublisher       *cfg.SqsPublisher
	CreditReportVerificationSubscriber      *cfg.SqsSubscriber `dynamic:"true"`
	Screening                               *Screening
	ExperianDataStorageLimitInHrs           int
	Tracing                                 *cfg.Tracing
	CreditReportConfig                      *CreditReportConfig `dynamic:"true"`
	SignalWorkflowPublisher                 *cfg.SqsPublisher
	CreditReportDerivedAttributesPublisher  *cfg.SqsPublisher
	CreditReportDerivedAttributesSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	CibilConsentExpiryInHours               time.Duration
	CibilDataStorageLimitInHrs              time.Duration
	CreditReportFlatteningSubscriber        *cfg.SqsSubscriber `dynamic:"true"`
	CreditReportDownloadEventsPublisher     *cfg.SnsPublisher
	DownTimeConfig                          map[string]*DownTimeConfig `dynamic:"true"`
}

type DownTimeConfig struct {
	// start and end time in format "DD-MM-YYYYTHH:MM:SS"
	Start string `dynamic:"true"`
	End   string `dynamic:"true"`
}

type Application struct {
	Environment   string
	Name          string
	IsSecureRedis bool
}

type Server struct {
	Ports        *cfg.ServerPorts
	EnablePoller bool
}

type Aws struct {
	Region string
	S3     *S3
}

type S3 struct {
	CreditReportsBucketName string `iam:"s3-readwrite"`
}

type Screening struct {
	CreditReportPresenceCheck *CreditReportPresenceCheck
	CreditReportVerification  *CreditReportVerification
}

type CreditReportPresenceCheck struct {
	// denotes max duration allowed for user to be processed in credit report presence check step
	CreditReportPresenceCheckMaxDuration time.Duration
}

type CreditReportVerification struct {
	CreditScoreThreshold int
	// denotes max duration allowed for user to be processed in credit report verification step
	CreditReportVerificationMaxDuration time.Duration
}

type CreditReportConfig struct {
	ExperianConsentConfig *ExperianConsentConfig `dynamic:"true"`
	// CreditReportPresenceEnabled enforces credit report presence before collecting consent to verify credit report
	// in screener. If flag is off, we show consent to all the users.
	// This config complements the flag in OnboardingConfig
	CreditReportPresenceEnabled bool `dynamic:"true"`
	MangleRawCreditReportData   bool
}

type ExperianConsentConfig struct {
	ConsentExtension time.Duration `dynamic:"true"`
	ConsentExpiry    time.Duration `dynamic:"true"`
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
}
