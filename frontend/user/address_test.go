package user

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/latlng"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	bankcustPb "github.com/epifi/gamma/api/bankcust"
	bcMocks "github.com/epifi/gamma/api/bankcust/mocks"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	feuserpb "github.com/epifi/gamma/api/frontend/user"
	kycPb "github.com/epifi/gamma/api/kyc"
	types "github.com/epifi/gamma/api/typesv2"
	onbScreenTypes "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	userLocationPb "github.com/epifi/gamma/api/user/location"
	userLocationMocks "github.com/epifi/gamma/api/user/location/mocks"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

func TestService_GetAddressEntryScreenFromLocation(t *testing.T) {
	logger.Init("test")
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()

	conf, _ = config.Load()
	gconf, _ := genconf.Load()

	const (
		locationToken      = "location-token"
		postalCode         = "111111"
		administrativeArea = "KA"
		locality           = "BLR"
		subLocality        = "BLR_00"
	)

	mockBcClient := bcMocks.NewMockBankCustomerServiceClient(ctr)
	mockUserLocationClient := userLocationMocks.NewMockLocationClient(ctr)

	type args struct {
		ctx   context.Context
		req   *feuserpb.GetAddressEntryScreenFromLocationRequest
		mocks []interface{}
	}

	tests := []struct {
		name    string
		args    args
		want    *feuserpb.GetAddressEntryScreenFromLocationResponse
		wantErr bool
	}{
		{
			name: "successfully fetched address screen",
			args: args{
				ctx: context.Background(),
				req: &feuserpb.GetAddressEntryScreenFromLocationRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actorId,
							Device: &commontypes.Device{
								LocationToken: locationToken,
							},
						},
					},
					LatLng: &latlng.LatLng{
						Latitude:  100,
						Longitude: 100,
					},
					Type: types.AddressType_CREDIT_CARD_SHIPPING,
				},
				mocks: []interface{}{
					mockUserLocationClient.EXPECT().FetchAndStoreAddressForIdentifier(context.Background(), &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
						IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_LOCATION,
						IdentifierValue: locationToken,
					}).Return(&userLocationPb.FetchAndStoreAddressForIdentifierResponse{
						Status: rpc.StatusOk(),
						Address: &types.PostalAddress{
							PostalCode:         postalCode,
							AdministrativeArea: administrativeArea,
							Locality:           locality,
							Sublocality:        subLocality,
						},
					}, nil),
					mockBcClient.EXPECT().GetBankCustomer(context.Background(), &bankcustPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bankcustPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
					}).Return(&bankcustPb.GetBankCustomerResponse{
						Status: rpc.StatusOk(),
						BankCustomer: &bankcustPb.BankCustomer{
							KycInfo: &bankcustPb.KYCInfo{KycLevel: kycPb.KYCLevel_FULL_KYC},
						},
					}, nil),
				},
			},
			want: &feuserpb.GetAddressEntryScreenFromLocationResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
				NextAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_ADD_NEW_ADDRESS_DETAILS_SCREEN,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&onbScreenTypes.AddNewAddressDetailsScreenOption{
						Title: &commontypes.Text{
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "Add address details",
							},
						},
						Subtitle: &commontypes.Text{
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "Your Fi-Federal Co-branded Credit Card will \nbe delivered to this address",
							},
						},
						Address: &types.PostalAddress{
							PostalCode:         postalCode,
							Locality:           locality,
							AdministrativeArea: administrativeArea,
							Sublocality:        subLocality,
						},
						IsPincodeEditable: true,
						IsCityEditable:    true,
						IsStateEditable:   true,
						IsCountryEditable: true,
						ProceedCta: &deeplinkPb.Cta{
							Text: "Proceed",
						},
						AddressType:   types.AddressType_CREDIT_CARD_SHIPPING,
						KycLevel:      types.KYCLevel_FULL_KYC,
						LocationToken: locationToken,
					}),
				},
			},
		},
		{
			name: "subtitle text empty for unknown address type",
			args: args{
				ctx: context.Background(),
				req: &feuserpb.GetAddressEntryScreenFromLocationRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actorId,
							Device: &commontypes.Device{
								LocationToken: locationToken,
							},
						},
					},
					LatLng: &latlng.LatLng{
						Latitude:  100,
						Longitude: 100,
					},
					Type: types.AddressType_PERMANENT,
				},
				mocks: []interface{}{
					mockUserLocationClient.EXPECT().FetchAndStoreAddressForIdentifier(context.Background(), &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
						IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_LOCATION,
						IdentifierValue: locationToken,
					}).Return(&userLocationPb.FetchAndStoreAddressForIdentifierResponse{
						Status: rpc.StatusOk(),
						Address: &types.PostalAddress{
							PostalCode:         postalCode,
							AdministrativeArea: administrativeArea,
							Locality:           locality,
							Sublocality:        subLocality,
						},
					}, nil),
					mockBcClient.EXPECT().GetBankCustomer(context.Background(), &bankcustPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bankcustPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
					}).Return(&bankcustPb.GetBankCustomerResponse{
						Status: rpc.StatusOk(),
						BankCustomer: &bankcustPb.BankCustomer{
							KycInfo: &bankcustPb.KYCInfo{KycLevel: kycPb.KYCLevel_FULL_KYC},
						},
					}, nil),
				},
			},
			want: &feuserpb.GetAddressEntryScreenFromLocationResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
				NextAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_ADD_NEW_ADDRESS_DETAILS_SCREEN,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&onbScreenTypes.AddNewAddressDetailsScreenOption{
						Title: &commontypes.Text{
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "Add address details",
							},
						},
						Subtitle: &commontypes.Text{
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "",
							},
						},
						Address: &types.PostalAddress{
							PostalCode:         postalCode,
							Locality:           locality,
							AdministrativeArea: administrativeArea,
							Sublocality:        subLocality,
						},
						IsPincodeEditable: true,
						IsCityEditable:    true,
						IsStateEditable:   true,
						IsCountryEditable: true,
						ProceedCta: &deeplinkPb.Cta{
							Text: "Proceed",
						},
						AddressType:   types.AddressType_PERMANENT,
						KycLevel:      types.KYCLevel_FULL_KYC,
						LocationToken: locationToken,
					}),
				},
			},
		},
		{
			name: "throw error for empty address type",
			args: args{
				ctx: context.Background(),
				req: &feuserpb.GetAddressEntryScreenFromLocationRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actorId,
							Device: &commontypes.Device{
								LocationToken: locationToken,
							},
						},
					},
					LatLng: &latlng.LatLng{
						Latitude:  100,
						Longitude: 100,
					},
				},
				mocks: []interface{}{},
			},
			want: &feuserpb.GetAddressEntryScreenFromLocationResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInvalidArgumentWithDebugMsg("address type cannot be unspecified")},
			},
		},
		{
			name: "error getting address details from location token",
			args: args{
				ctx: context.Background(),
				req: &feuserpb.GetAddressEntryScreenFromLocationRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actorId,
							Device: &commontypes.Device{
								LocationToken: locationToken,
							},
						},
					},
					LatLng: &latlng.LatLng{
						Latitude:  100,
						Longitude: 100,
					},
					Type: types.AddressType_CREDIT_CARD_SHIPPING,
				},
				mocks: []interface{}{
					mockUserLocationClient.EXPECT().FetchAndStoreAddressForIdentifier(context.Background(), &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
						IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_LOCATION,
						IdentifierValue: locationToken,
					}).Return(&userLocationPb.FetchAndStoreAddressForIdentifierResponse{
						Status: rpc.StatusInternal(),
					}, nil),
				},
			},
			want: &feuserpb.GetAddressEntryScreenFromLocationResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
			},
		},
		{
			name: "error getting bank customer",
			args: args{
				ctx: context.Background(),
				req: &feuserpb.GetAddressEntryScreenFromLocationRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actorId,
							Device: &commontypes.Device{
								LocationToken: locationToken,
							},
						},
					},
					LatLng: &latlng.LatLng{
						Latitude:  100,
						Longitude: 100,
					},
					Type: types.AddressType_CREDIT_CARD_SHIPPING,
				},
				mocks: []interface{}{
					mockUserLocationClient.EXPECT().FetchAndStoreAddressForIdentifier(context.Background(), &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
						IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_LOCATION,
						IdentifierValue: locationToken,
					}).Return(&userLocationPb.FetchAndStoreAddressForIdentifierResponse{
						Status: rpc.StatusOk(),
						Address: &types.PostalAddress{
							PostalCode:         postalCode,
							AdministrativeArea: administrativeArea,
							Locality:           locality,
							Sublocality:        subLocality,
						},
					}, nil),
					mockBcClient.EXPECT().GetBankCustomer(context.Background(), &bankcustPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bankcustPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
					}).Return(&bankcustPb.GetBankCustomerResponse{
						Status: rpc.StatusInternal(),
					}, nil),
				},
			},
			want: &feuserpb.GetAddressEntryScreenFromLocationResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewUserService(conf, nil, nil, nil,
				nil, nil, nil, nil, nil, nil, nil,
				nil, nil, nil, nil, nil, nil, nil,
				nil, nil, nil, nil, nil,
				nil, gconf, nil, nil, nil, nil,
				nil, mockBcClient, nil, nil,
				nil, nil, nil, nil, mockUserLocationClient,
				nil, nil, nil, nil, nil, nil, nil, nil, nil)
			got, err := s.GetAddressEntryScreenFromLocation(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAddressEntryScreenFromLocation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAddressEntryScreenFromLocation() got = %v, want %v", got, tt.want)
			}
		})
	}
}
