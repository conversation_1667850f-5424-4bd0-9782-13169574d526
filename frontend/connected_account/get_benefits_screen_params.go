package connected_account

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	typesUiWidgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	goUtils "github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/logger"

	beCaEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	consentPb "github.com/epifi/gamma/api/consent"
	feConnectedAccPb "github.com/epifi/gamma/api/frontend/connected_account"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	analyserTypesPb "github.com/epifi/gamma/api/typesv2/analyser"
	typesUiPb "github.com/epifi/gamma/api/typesv2/ui"
)

const (
	FiLiteTnC             = "You understand this info will be available for a <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/T&C\">limited time</a>."
	SpendInsightIcon      = "https://epifi-icons.pointz.in/analyser/consent/spend_insight.png"
	BellIcon              = "https://epifi-icons.pointz.in/analyser/consent/bell.png"
	ConnectLinkIcon       = "https://epifi-icons.pointz.in/analyser/consent/connect_link.png"
	PieChart3DIcon        = "https://epifi-icons.pointz.in/analyser/consent/3d_pie_chart.png"
	ConnectAccountCtaText = "Connect my bank accounts"
)

// GetBenefitsScreenParams returns params to build the connected accounts benefits screen
func (s *Service) GetBenefitsScreenParams(ctx context.Context, req *feConnectedAccPb.GetBenefitsScreenParamsRequest) (*feConnectedAccPb.GetBenefitsScreenParamsResponse, error) {
	caFlowName := goUtils.Enum(req.GetCaFlowName(), beCaEnumPb.CAFlowName_value, beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT)
	switch caFlowName {
	case beCaEnumPb.CAFlowName_CA_FLOW_NAME_PEOPLE_MERCHANT_ANALYSER,
		beCaEnumPb.CAFlowName_CA_FLOW_NAME_TIME_SPENDS_ANALYSER,
		beCaEnumPb.CAFlowName_CA_FLOW_NAME_CATEGORY_ANALYSER,
		beCaEnumPb.CAFlowName_CA_FLOW_NAME_SECRET_INSIGHTS,
		// fix this to reduce unnecessary screen
		beCaEnumPb.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION:
		consentScreen, redirectDl, err := s.createAnalyserBenefitsScreen(ctx, req.GetReq(), caFlowName, req.GetCaFlowId())
		if err != nil {
			logger.Error(ctx, "failed to create connected account benefits screen", zap.Error(err))
			return &feConnectedAccPb.GetBenefitsScreenParamsResponse{
				RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusInternalWithDebugMsg(fmt.Sprintf("failed to create connected account benefits screen: %v", err))},
			}, nil
		}
		res := &feConnectedAccPb.GetBenefitsScreenParamsResponse{
			RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
		}
		if redirectDl != nil {
			res.RedirectDeeplink = redirectDl
		} else {
			res.BenefitsScreenType = feConnectedAccPb.GetBenefitsScreenParamsResponse_BENEFITS_SCREEN_TYPE_ANALYSER_CONSENT
			res.Params = &feConnectedAccPb.GetBenefitsScreenParamsResponse_AnalyserConsent{AnalyserConsent: consentScreen}
		}
		return res, nil
	default:
		logger.Error(ctx, fmt.Sprintf("unhandled connected accounts flow while preparing benefits screen %v", caFlowName))
		return &feConnectedAccPb.GetBenefitsScreenParamsResponse{
			RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusInternalWithDebugMsg(fmt.Sprintf("unhandled connected accounts flow while preparing benefits screen %v", caFlowName))},
		}, nil
	}
}

//nolint:funlen
func (s *Service) createAnalyserBenefitsScreen(
	ctx context.Context,
	requestHeader *headerPb.RequestHeader,
	caFlowName beCaEnumPb.CAFlowName,
	caFlowId string,
) (*analyserTypesPb.ConsentScreen, *deeplinkPb.Deeplink, error) {
	data, err := s.gatherDataForCaDeeplink(ctx, requestHeader, caFlowName.String(), "")
	if err != nil {
		return nil, nil, fmt.Errorf("failed to gather data for CA deeplink: %w", err)
	}

	actorId := requestHeader.GetAuth().GetActorId()
	requiredConsents, err := s.getRequiredFiConsents(data.isFiLiteUser, data.getAaEntityResp.GetAaEntity())
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get required fi consents: %w", err)
	}

	// default benefits screen to initiate connected accounts
	sdkDeeplink, err := s.getDeeplinkToStartSDK(ctx, data.usr, requestHeader, data.getAaEntityResp.GetAaEntity(), caFlowName.String(), caFlowId, nil)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get deeplink to stark CA sdk: %w", err)
	}

	if len(data.reoobeAccounts) > 0 {
		// reoobe accounts will be delinked
		sdkDeeplink, err = s.getDeeplinkForHandleReoobe(data.reoobeAccounts, sdkDeeplink)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to get reoob deeplink: %w", err)
		}
	}

	unavailableConsents, err := s.filterUnavailableFiConsents(ctx, actorId, requiredConsents)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to fetch unavailable consents: %w", err)
	}

	if len(unavailableConsents) == 0 {
		// consents are already available. redirect to sdk
		return nil, sdkDeeplink, nil
	}

	tnc, err := s.connectedAccountsTnC(data)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get connected accounts tnc: %w", err)
	}
	cta := s.connectedAccountSdkCta(sdkDeeplink, data.isFiLiteUser)

	consentScreen := analyserTypesPb.NewDefaultConsentScreen().
		WithDefaultTitle("Spend insights").
		WithPrimaryImage(commontypes.GetVisualElementImageFromUrl(SpendInsightIcon).WithProperties(&commontypes.VisualElementProperties{Width: 150, Height: 150})).
		WithDefaultDescription("Track spends across all your bank accounts").
		WithDefaultBenefitsSection(nil).
		WithDefaultBenefitsLineItem(PieChart3DIcon, "Get insights into your spends").
		WithDefaultBenefitsLineItem(BellIcon, "Set reminders to spend smart").
		WithDefaultBenefitsLineItem(ConnectLinkIcon, "Disconnect accounts anytime")
	consentScreen.TncContent = tnc
	consentScreen.Cta = cta
	return consentScreen, nil, nil
}

// connectedAccountsTnC prepares tnc for Fi core and lite users
func (s *Service) connectedAccountsTnC(data *caDeeplinkData) (*analyserTypesPb.TnC, error) {
	var (
		tncText   string
		wealthTnc string
	)
	switch data.getAaEntityResp.GetAaEntity() {
	case beCaEnumPb.AaEntity_AA_ENTITY_AA_FINVU:
		tncText = s.epifiWealthFinvuTnc()
		wealthTnc = consentPb.ConsentType_CONNECTED_ACCOUNTS_FI_WEALTH_AND_FINVU_TNC.String()
	case beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY:
		tncText = s.epifiWealthOneMoneyTnc()
		wealthTnc = consentPb.ConsentType_CONNECTED_ACCOUNTS_FI_WEALTH_AND_ONEMONEY_TNC.String()
	default:
		return nil, fmt.Errorf("unhandle aa entity tnc: %v", data.getAaEntityResp.GetAaEntity())
	}

	// ToDo: add tnc for finvu an one money tnc
	epifiWealthTncText := commontypes.GetTextFromHtmlStringFontColourFontStyle(
		tncText, colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_XS)

	if data.isFiLiteUser {
		return &analyserTypesPb.TnC{
			TncType: analyserTypesPb.TnCType_TNC_TYPE_CHECKBOX_LIST,
			Content: &analyserTypesPb.TnC_CheckBoxList{
				CheckBoxList: &analyserTypesPb.CheckBoxList{
					CheckBoxes: []*typesUiWidgetPb.CheckboxItem{
						{
							Id:          wealthTnc,
							DisplayText: epifiWealthTncText,
						},
						{
							Id: consentPb.ConsentType_CONNECTED_ACCOUNTS_LIMITED_TIME_TNC.String(),
							DisplayText: commontypes.GetTextFromHtmlStringFontColourFontStyle(
								FiLiteTnC, colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_XS),
						},
					},
				},
			},
		}, nil
	} else {
		return &analyserTypesPb.TnC{
			TncType: analyserTypesPb.TnCType_TNC_TYPE_PARAGRAPH,
			Content: &analyserTypesPb.TnC_Paragraph{
				Paragraph: &analyserTypesPb.ConsentParagraph{
					ConsentId: wealthTnc,
					Text:      epifiWealthTncText,
				},
			},
		}, nil
	}
}

// connectedAccountSdkCta prepares cta button for Fi core and lite users
func (s *Service) connectedAccountSdkCta(sdkDeeplink *deeplinkPb.Deeplink, isFiLiteUser bool) *analyserTypesPb.CTA {
	ctaContent := typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(
		ConnectAccountCtaText, colors.ColorSnow, commontypes.FontStyle_BUTTON_M)).WithContainer(0, 0, 0, colors.ColorLightPrimaryAction).WithDeeplink(sdkDeeplink)

	if isFiLiteUser {
		return &analyserTypesPb.CTA{
			CtaType:           analyserTypesPb.CTAType_CTA_TYPE_BUTTON,
			Content:           ctaContent,
			BottomShadowColor: "#00866F",
		}
	} else {
		return &analyserTypesPb.CTA{
			CtaType: analyserTypesPb.CTAType_CTA_TYPE_SLIDER,
			Content: ctaContent,
		}
	}
}

func (s *Service) epifiWealthOneMoneyTnc() string {
	return "You agree to <a style='color: #00B899; text-decoration: none;' href=\"" + s.conf.LegalDocuments().FiWealthTncUrl + "\">EpiFi Wealth T&C</a>, " +
		"<a style='color: #00B899; text-decoration: none;' href=\"" + s.conf.LegalDocuments().AaOnemoneyTncUrl + "\">OneMoney T&C</a> " +
		"& consent to EpiFi Wealth receiving your spending info from your other bank accounts"
}

func (s *Service) epifiWealthFinvuTnc() string {
	return "You agree to <a style='color: #00B899; text-decoration: none;' href=\"" + s.conf.LegalDocuments().FiWealthTncUrl + "\">EpiFi Wealth T&C</a>, " +
		"<a style='color: #00B899; text-decoration: none;' href=\"" + s.conf.LegalDocuments().AaFinvuTncUrl + "\">Finvu T&C</a> " +
		"& consent to EpiFi Wealth receiving your spending info from your other bank accounts"
}
