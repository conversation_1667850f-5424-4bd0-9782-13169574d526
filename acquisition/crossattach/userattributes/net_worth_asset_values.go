package userattributes

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/acquisition/crossattach"
	networthPb "github.com/epifi/gamma/api/insights/networth"
)

type NetWorthAssetValuesGetter struct {
	networthClient networthPb.NetWorthClient
}

func NewNetWorthAssetValuesGetter(networthClient networthPb.NetWorthClient) *NetWorthAssetValuesGetter {
	return &NetWorthAssetValuesGetter{
		networthClient: networthClient,
	}
}

var _ UserAttributesGetter = (*NetWorthAssetValuesGetter)(nil)

func (s *NetWorthAssetValuesGetter) GetUserAttributesFromSource(ctx context.Context, req *GetUserAttributesFromSourceRequest) (*GetUserAttributesFromSourceResponse, error) {
	// As of now, Connected accounts and Mutual funds are considered as WB assets for crossattach
	getNetWorthValueRes, err := s.networthClient.GetNetWorthValue(ctx, &networthPb.GetNetWorthValueRequest{
		ActorId: req.GetActorId(),
		AssetTypes: []networthPb.AssetType{
			networthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
			networthPb.AssetType_ASSET_TYPE_MUTUAL_FUND,
		},
	})
	if rpcErr := epifigrpc.RPCError(getNetWorthValueRes, err); rpcErr != nil {
		logger.Error(ctx, "error fetching GetNetWorthValue", zap.Error(rpcErr))
		return nil, rpcErr
	}
	return &GetUserAttributesFromSourceResponse{
		UserAttributeValueMap: map[crossattach.UserAttribute]*crossattach.UserAttributeValue{
			crossattach.UserAttribute_USER_ATTRIBUTE_NET_WORTH_ASSET_VALUES_LIST: {
				Value: &crossattach.UserAttributeValue_NetWorthAssetValueList_{
					NetWorthAssetValueList: &crossattach.UserAttributeValue_NetWorthAssetValueList{
						NetWorthAssetValues: getNetWorthValueRes.GetAssetValues(),
					},
				},
			},
		},
	}, nil
}
