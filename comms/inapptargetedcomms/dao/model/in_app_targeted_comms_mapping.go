package model

import (
	"time"

	tcPb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	"github.com/epifi/be-common/pkg/nulltypes"

	"google.golang.org/protobuf/types/known/timestamppb"
)

type InAppTargetedCommsMapping struct {
	Id string `gorm:"type:uuid;default:gen_random_uuid();primary_key"`
	// foreign id from InAppTargetedCommsElements table
	ElementId string
	// the type of mapping for this row
	// an element can be mapped with user, user_tag or screen
	MappingType tcPb.MappingType
	// mapped value is dependent on mapping type
	// it is actor_id in case of user mapping, enum string for user_tag and screen mappings
	MappedValue string
	// mapped value meta is extra info regarding mapped value
	// eg: to identify help category screens (in this case it is categoryID_name)
	MappedValueMeta string
	// visibility state of the banner particular to this mapped element
	MappedVisibilityState tcPb.VisibilityState
	// timestamp at which this row was created in DB
	CreatedAt time.Time
	// timestamp at which this row was last updated
	UpdatedAt time.Time
	// timestamp at which last callback received
	LastCallbackTime nulltypes.NullTime
}

func NewInAppTargetedCommsMappingFromProtoMsg(msg *tcPb.InAppTargetedCommsMapping) *InAppTargetedCommsMapping {
	modelMsg := &InAppTargetedCommsMapping{
		ElementId:             msg.GetElementId(),
		MappingType:           msg.GetMappingDetails().GetMappingType(),
		MappedValue:           msg.GetMappingDetails().GetMappedValue(),
		MappedValueMeta:       msg.GetMappingDetails().GetMappedValueMeta(),
		MappedVisibilityState: msg.GetMappedVisibilityState(),
	}
	if msg.LastCallbackTime != nil {
		modelMsg.LastCallbackTime = nulltypes.NewNullTime(msg.GetLastCallbackTime().AsTime())
	}
	return modelMsg
}

func (t *InAppTargetedCommsMapping) ToProtoMessage() *tcPb.InAppTargetedCommsMapping {
	protoMsg := &tcPb.InAppTargetedCommsMapping{
		Id:        t.Id,
		ElementId: t.ElementId,
		MappingDetails: &tcPb.MappingDetails{
			MappingType:     t.MappingType,
			MappedValue:     t.MappedValue,
			MappedValueMeta: t.MappedValueMeta,
		},
		MappedVisibilityState: t.MappedVisibilityState,
		CreatedAt:             timestamppb.New(t.CreatedAt),
		UpdatedAt:             timestamppb.New(t.UpdatedAt),
	}
	if t.LastCallbackTime.Valid {
		protoMsg.LastCallbackTime = timestamppb.New(t.LastCallbackTime.GetValue())
	}
	return protoMsg
}

func NewInAppTargetedCommsMappingList(mappings []*tcPb.InAppTargetedCommsMapping) []*InAppTargetedCommsMapping {
	var mappingModels []*InAppTargetedCommsMapping
	for _, mappingProto := range mappings {
		mappingModels = append(mappingModels, NewInAppTargetedCommsMappingFromProtoMsg(mappingProto))
	}
	return mappingModels
}

func InAppTargetedCommsMappingListToProtos(mappingModels []*InAppTargetedCommsMapping) []*tcPb.InAppTargetedCommsMapping {
	var mappings []*tcPb.InAppTargetedCommsMapping
	for _, mappingModel := range mappingModels {
		mappings = append(mappings, mappingModel.ToProtoMessage())
	}
	return mappings
}
