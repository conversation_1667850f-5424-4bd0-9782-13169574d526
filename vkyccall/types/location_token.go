package types

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/epifi/gamma/api/vkyccall/types"
)

type LocationToken struct {
	LocationToken string
	RecordedAt    time.Time
}

func NewLocationToken(loc *pb.LocationToken) *LocationToken {
	if loc == nil {
		return nil
	}
	return &LocationToken{
		LocationToken: loc.GetLocationToken(),
		RecordedAt:    loc.GetRecordedAt().AsTime(),
	}
}

func (l *LocationToken) ToProto() *pb.LocationToken {
	if l == nil {
		return nil
	}
	return &pb.LocationToken{
		RecordedAt:    timestamppb.New(l.RecordedAt),
		LocationToken: l.LocationToken,
	}
}
