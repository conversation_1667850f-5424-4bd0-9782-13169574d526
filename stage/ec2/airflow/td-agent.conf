<source>
  @type forward
  port 24224
  @id input_forward
</source>

<source>
  @type tail
  path /home/<USER>/airflow/logs/*/*/*/*
  tag dev.airflow
  read_from_head true
  <parse>
    @type json
  </parse>
  <format>
    @type json
  </format>
</source>
<match dev.airflow>
  @type forward
  <server>
    tag_key @log_name
    host data-dev-aggregator.epifi.vpc
    port 24224
  </server>
  <buffer>
    flush_interval 5s
  </buffer>
</match>
<match dev.airflow>
  @type s3
  s3_bucket epifi-dev-dp-resources
  path logs/airflow/
  append true
  time_slice_format %Y-%m-%d/%H/
  time_slice_wait 1h
</match>