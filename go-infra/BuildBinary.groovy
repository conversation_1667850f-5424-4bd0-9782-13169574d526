pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
	go 'Go stable'
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '10'))
    }
    parameters {
        string(name: 'Branch', defaultValue: 'master', description: 'branch of the go-infra repo to be used for the build')
        string(name: 'Target', description: 'target to build binary for')
        choice(name: 'FixType', choices: ['PATCH', 'MINOR', 'MAJOR'], description: 'Fix type for binary')
        booleanParam(name: 'REFRESH', defaultValue: false, description: '<PERSON><PERSON>, ignore this variable! This is for DevOps to refresh <PERSON><PERSON>le.')
        choice(name: 'Type', choices: ['cmd', 'script'], description: 'Type for binary')
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true}
            }
            steps {
                script {    
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-REFRESH:${params.REFRESH}"
                    currentBuild.description = "#${BUILD_NUMBER}-REFRESH:${params.REFRESH}"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Repo clone') {
            steps {
                dir('app') {
                    git credentialsId: 'github-app-epifi', poll: false, url: "https://github.com/epiFi/go-infra.git", branch: "${params.Branch}"
                }
            }
        }
        stage('Build Binary') {
            steps {
                dir('app') {
                    script {
                        if (Type == 'cmd'){
                        sh "make build target=${params.Target}"
                        }
                        else
                        {
                        sh "make build-script target=${params.Target}"
                        }
                    }
                }
            }
        }
        stage('Push Binary') {
            steps {
                dir('app') {
                    script {
                        sh "aws s3 cp s3://epifi-go-infra-bin/publisher/linux/amd64/1.0.1 ./publisher"
                        sh "chmod 700 publisher"
                        sh "./publisher  --bucket epifi-go-infra-bin --target=${params.Target} --fix-type=${params.FixType} --target-path=./bin/${params.Target}"
                    }
                }
            }
        }
    }
}
