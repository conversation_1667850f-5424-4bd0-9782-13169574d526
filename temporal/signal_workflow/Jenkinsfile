@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '25', artifactNumToKeepStr: '25'))
    }
    parameters {
        choice(name: 'env', choices: ['staging', 'qa', 'demo', 'uat'], description: 'Select the env to run the script in')
        string(name: 'VERSION', defaultValue: 'stable', description: 'Binary Version to Execute')
        string(name: 'branch', defaultValue: 'master', description: 'Enter gamma branch')
        string(name: 'workflowId', defaultValue: '', description: 'Identifier of the workflow to which signal should be sent')
        string(name: 'namespace', defaultValue: '', description: 'Temporal namespace in which workflow is executing')
        string(name: 'signalName', defaultValue: '', description: 'Name of the signal to be sent')
        string(name: 'signalPayload', defaultValue: '', description: 'Marshalled signal payload (eg: "{\\"status\\":\\"POOL_ACCOUNT_TRANSFER_FAILED\\",\\"txn_id\\":\\"RANDOM\\"}")')
        string(name: 'reason', defaultValue: '', description: 'Reason for sending signal')
    }
    environment {
        ENV = "${params.env}"
    }
    stages {
        stage('Get Gamma Repo') {
            steps {
                script {
                    dir('gamma') {
                        // Wipe the workspace so we are building completely clean
                        deleteDir()
                        git credentialsId: 'github-app-epifi', poll: false, url: 'https://github.com/epiFi/gamma.git', branch: "${params.branch}"
                    }
                }
            }
        }
        stage('Initialise aws tokens') {
            steps {
                script {
                    dir('gamma') {
                        ROLE_ARN = sh(script: "set +x && echo \${${"${ENV}".toUpperCase()}_ACCOUNT_ROLE}", returnStdout: true).trim()
                        echo "Role ARN to be used for ${ENV} is: ${ROLE_ARN}"
                        aws_credentials_json = sh(script: "set +x && aws sts assume-role --role-arn '${ROLE_ARN}' --role-session-name ${ENV}-${env.TARGET}-deploy --region ap-south-1", returnStdout: true).trim()
                        AWS_ACCESS_KEY_ID = sh(script: "set +x && echo '${aws_credentials_json}' | jq --exit-status --raw-output .Credentials.AccessKeyId", returnStdout: true).trim()
                        AWS_SECRET_ACCESS_KEY = sh(script: "set +x && echo '$aws_credentials_json' | jq --exit-status --raw-output .Credentials.SecretAccessKey", returnStdout: true).trim()
                        AWS_SESSION_TOKEN = sh(script: "set +x && echo '$aws_credentials_json' | jq --exit-status --raw-output .Credentials.SessionToken", returnStdout: true).trim()
                    }
                }
            }
        }
         stage('Fetch binary') {
            steps {
                dir('gamma/output') {
                    script {
                        dir('certs') {
                            assumeRole()
                        }
                    }
                    script {
                        echo "Fetching pre-built binary"
                        sh """
                        set +x
                            . ${WORKSPACE}/.env
                        set -x
                        chmod 777 ${env.WORKSPACE}/scripts/platform/gamma/helper.sh
                        ${env.WORKSPACE}/scripts/platform/gamma/helper.sh fetch_binary epifi-jenkins-job-binaries temporal/signal_workflow ${params.VERSION}
                        """
                    }
                }
            }
         }
        stage('Execute') {
            steps {
                script {
                    dir('gamma') {
                        sh """
                        cd ./output/temporal/signal_workflow &&
                        AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY} AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN} ENVIRONMENT=${ENV} ./temporal/signal_workflow_bin --workflow-id=${params.workflowId} --namespace=${params.namespace} --signal-name=${params.signalName} --reason=${params.reason} --signal-payload=${params.signalPayload}
                        """
                    }
                }
            }
        }
    }
}
