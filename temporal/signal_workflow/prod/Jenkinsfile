@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '25', artifactNumToKeepStr: '25'))
    }
    parameters {
        string(name: 'workflowId', defaultValue: '', description: 'Identifier of the workflow to which signal should be sent')
        string(name: 'VERSION', defaultValue: 'stable', description: 'Binary Version to Execute')
        string(name: 'namespace', defaultValue: '', description: 'Temporal namespace in which workflow is executing')
        string(name: 'signalName', defaultValue: '', description: 'Name of the signal to be sent')
        string(name: 'signalPayload', defaultValue: '', description: 'Marshalled signal payload (eg: "{\\"status\\":\\"POOL_ACCOUNT_TRANSFER_FAILED\\",\\"txn_id\\":\\"RANDOM\\"}")')
        string(name: 'reason', defaultValue: '', description: 'Reason for sending signal')
    }
    environment {
        ENV = "prod"
    }
    stages {
        stage('Get Gamma Repo') {
            steps {
                script {
                    dir('gamma') {
                        // Wipe the workspace so we are building completely clean
                        deleteDir()
                        git credentialsId: 'github-app-epifi', poll: false, url: 'https://github.com/epiFi/gamma.git', branch: "master"
                    }
                }
            }
        }
        stage('Fetch binary') {
            steps {
                dir('gamma/output') {
                    script {
                        echo "Fetching pre-built binary"
                        sh """
                        chmod 777 ${env.WORKSPACE}/scripts/platform/gamma/helper.sh
                        ${env.WORKSPACE}/scripts/platform/gamma/helper.sh fetch_binary epifi-gamma-binaries-prod temporal/signal_workflow ${params.VERSION}
                        """
                    }
                }
            }
        }
        stage('Execute') {
            steps {
                script {
                    dir('gamma') {
                        sh """
                        cd ./output/temporal/signal_workflow &&
                        ENVIRONMENT=${ENV} ./temporal/signal_workflow_bin --workflow-id=${params.workflowId} --namespace=${params.namespace} --signal-name=${params.signalName} --reason=${params.reason} --signal-payload=${params.signalPayload}
                        """
                    }
                }
            }
        }
    }
}
