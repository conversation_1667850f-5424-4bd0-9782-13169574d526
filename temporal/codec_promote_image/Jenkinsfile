pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    options {
        disableConcurrentBuilds()
    }
    parameters {
        choice(name: 'ENV', choices: ['deploy', 'data-dev'], description: 'Environment')
        choice(name: 'IMAGE', choices: ['temporal-codec-server'], description: '')
        string(name: 'TAG', defaultValue: '', description: '')
        string(name: 'Share_Key', defaultValue: '', description: 'optional')

    }
    environment {
        FROM_ACCOUNT = "************" // deploy aws account
        TO_ACCOUNT = "************"   // prod aws account
        ENVIRONMENT = "${params.ENV}"
    }
    stages {
        stage('Execute ') {
            steps {
                script {
                     if (params.ENV == "data-dev") {
                        FROM_ACCOUNT = "************" // data-dev aws account
                        TO_ACCOUNT = "************"   // data-prod aws account
                     }
                    sh """
                        aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ${FROM_ACCOUNT}.dkr.ecr.ap-south-1.amazonaws.com
                        docker pull ${FROM_ACCOUNT}.dkr.ecr.ap-south-1.amazonaws.com/${params.IMAGE}:${params.TAG}
                        aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ${TO_ACCOUNT}.dkr.ecr.ap-south-1.amazonaws.com
                        docker tag ${FROM_ACCOUNT}.dkr.ecr.ap-south-1.amazonaws.com/${params.IMAGE}:${params.TAG} ${TO_ACCOUNT}.dkr.ecr.ap-south-1.amazonaws.com/${params.IMAGE}:${params.TAG}
                        docker push ${TO_ACCOUNT}.dkr.ecr.ap-south-1.amazonaws.com/${params.IMAGE}:${params.TAG}
                        if [ "${params.Share_Key}" != "" ]; then
                          echo "Uploading share key"
                          json='{"app": "'${IMAGE}'", "imageId": "'${TAG}'", "cluster": "deploy", "shareKey": "'${Share_Key}'"}'
                          echo \$json | aws s3 cp - s3://epifi-jarvis-k8s/${Share_Key}/${IMAGE}.txt
                          echo "Uploaded share key"
                        fi
                    """
                }
            }
        }
    }
}
