@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    options {
        disableConcurrentBuilds()
    }
    parameters {
        choice(name: 'ENV', choices: ['deploy'], description: 'Environment')
        string(name: 'VERSION', defaultValue: 'stable', description: 'Binary Version to Execute')
        choice(name: 'APP_BRANCH', choices: ['master'], description: 'gamma branch')
        choice(name: 'ES_MAPPING_BRANCH', choices: ['master'], description: 'es mapping branch')
        choice(name: 'MAPPING_JSON_PATH', choices: ['es-mapping/deploy/field_mapping/temporal/mapping.json'],  description: 'path from which search attributes are being read')
    }
    environment {
        APP_BRANCH = "${params.APP_BRANCH}"
        ES_MAPPING_BRANCH = "${params.ES_MAPPING_BRANCH}"
        // there is only a single es cluster for non-prod in deploy. Hence using qa environment as deploy
        ENVIRONMENT = "qa"
        MAPPING_JSON_PATH = "../../../../${params.MAPPING_JSON_PATH}"
        DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP=true
    }
    stages {
        stage('Repo clone') {
            steps {
                dir('gamma') {
                    // Wipe the workspace so we are building completely clean
                    deleteDir()
                    git credentialsId: 'github-app-epifi', poll: false, url: 'https://github.com/epiFi/gamma.git/', branch: "${APP_BRANCH}"
                }
                dir('es-mapping') {
                    // Wipe the workspace so we are building completely clean
                    deleteDir()
                    git credentialsId: 'github-app-epifi', poll: false, url: 'https://github.com/epiFi/es-mapping.git', branch: "${ES_MAPPING_BRANCH}"
                }
            }
        }
        stage('Fetch binary') {
            steps {
                dir('gamma/output') {
                    script {
                        dir('certs') {
                            assumeRole()
                        }
                    }
                    script {
                        echo "Fetching pre-built binary"
                        sh """
                        set +x
                            . ${WORKSPACE}/.env
                        set -x
                        chmod 777 ${env.WORKSPACE}/scripts/platform/gamma/helper.sh
                        ${env.WORKSPACE}/scripts/platform/gamma/helper.sh fetch_binary epifi-jenkins-job-binaries temporal/create_search_attributes ${params.VERSION}
                        """
                    }
                }
            }
        }
        stage('Execute script') {
            steps {
                script {
                    dir('gamma/output/temporal/create_search_attributes') {
                        sh """
                            ./temporal/create_search_attributes_bin -attribute-file-path=${MAPPING_JSON_PATH}
                        """
                    }
                }
            }
        }
    }
}
