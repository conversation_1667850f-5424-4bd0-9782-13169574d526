@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    options {
        disableConcurrentBuilds()
    }
    parameters {
        choice(name: 'ENV', choices: ['prod'], description: 'Environment')
        string(name: 'VERSION', defaultValue: 'stable', description: 'Binary Version to Execute')
        choice(name: 'APP_BRANCH', choices: ['master'], description: 'gamma branch')
        choice(name: 'ES_MAPPING_BRANCH', choices: ['master'], description: 'es mapping branch')
        choice(name: 'MAPPING_JSON_PATH', choices: ['es-mapping/prod/field_mapping/temporal/mapping.json'],  description: 'path from which search attributes are being read')
    }
    environment {
        APP_BRANCH = "${params.APP_BRANCH}"
        ES_MAPPING_BRANCH = "${params.ES_MAPPING_BRANCH}"
        ENVIRONMENT = "${params.ENV}"
        MAPPING_JSON_PATH = "../../../../${params.MAPPING_JSON_PATH}"
        DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP=true
    }
    stages {
        stage('Repo clone') {
            steps {
                dir('gamma') {
                    // Wipe the workspace so we are building completely clean
                    deleteDir()
                    git credentialsId: 'github-app-epifi', poll: false, url: 'https://github.com/epiFi/gamma.git/', branch: "${APP_BRANCH}"
                }
                dir('es-mapping') {
                    // Wipe the workspace so we are building completely clean
                    deleteDir()
                    git credentialsId: 'github-app-epifi', poll: false, url: 'https://github.com/epiFi/es-mapping.git', branch: "${ES_MAPPING_BRANCH}"
                }
            }
        }
        stage('Fetch binary') {
            steps {
                dir('gamma/output') {
                    script {
                        echo "Fetching pre-built binary"
                        sh """
                        chmod 777 ${env.WORKSPACE}/scripts/platform/gamma/helper.sh
                        ${env.WORKSPACE}/scripts/platform/gamma/helper.sh fetch_binary epifi-gamma-binaries-prod temporal/create_search_attributes ${params.VERSION}
                        """
                    }
                }
            }
        }
        stage('Execute script') {
            steps {
                script {
                    dir('gamma') {
                        sh """
                            cd ./output/temporal/create_search_attributes
                            ./temporal/create_search_attributes_bin -attribute-file-path=${MAPPING_JSON_PATH}
                        """
                    }
                }
            }
        }
    }
}
