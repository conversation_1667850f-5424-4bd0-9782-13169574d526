//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"
	"gorm.io/gorm"

	pkgGenCfg "github.com/epifi/be-common/pkg/cfg/genconf"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/ratelimiter"
	ratelimiterWire "github.com/epifi/be-common/pkg/ratelimiter/wire"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth/location"
	"github.com/epifi/gamma/api/creditreportv2"
	employmentPb "github.com/epifi/gamma/api/employment"
	inAppReferralPb "github.com/epifi/gamma/api/inappreferral"
	"github.com/epifi/gamma/api/preapprovedloan/lendability"
	"github.com/epifi/gamma/api/user"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	employmentVg "github.com/epifi/gamma/api/vendorgateway/employment"
	fennelVgPb "github.com/epifi/gamma/api/vendorgateway/fennel"
	"github.com/epifi/gamma/api/vendorgateway/incomeestimator"
	vgItr "github.com/epifi/gamma/api/vendorgateway/itr"
	"github.com/epifi/gamma/api/vendorgateway/phonenetwork"
	profileValidationPb "github.com/epifi/gamma/api/vendorgateway/profilevalidation"
	vgScienapticPb "github.com/epifi/gamma/api/vendorgateway/scienaptic"
	"github.com/epifi/gamma/api/vendorgateway/userseg"
	"github.com/epifi/gamma/featurestore"
	"github.com/epifi/gamma/featurestore/fennel"
	"github.com/epifi/gamma/featurestore/scienaptic"
	"github.com/epifi/gamma/pkg/vendorstore"
	"github.com/epifi/gamma/userintel"
	"github.com/epifi/gamma/userintel/config/genconf"
	"github.com/epifi/gamma/userintel/dao"
	"github.com/epifi/gamma/userintel/inteltypeparam"
	"github.com/epifi/gamma/userintel/wire/provider"
)

func RateLimiterConfigProvider(conf *genconf.Config) *pkgGenCfg.RateLimitConfig {
	return conf.RLConfig()
}

func DummyRateLimiterOptionsProvider() []ratelimiter.Option {
	return nil
}

func InitialiseUserIntelService(db types.EpifiCRDB, userClient user.UsersClient, actorClient actor.ActorClient,
	userSegClient userseg.UserSegmentationClient, phoneNetworkClient phonenetwork.PhoneNetworkClient,
	employmentClient employmentVg.EmploymentClient, profileValidationClient profileValidationPb.ProfileValidationClient,
	userRedisStore types.UserRedisStore, onbClient onboardingPb.OnboardingClient, vgIncomeEstimator incomeestimator.IncomeEstimatorClient,
	inAppReferralClient inAppReferralPb.InAppReferralClient,
	empClient employmentPb.EmploymentClient, locClient location.LocationClient, fennelClient fennelVgPb.FennelFeatureStoreClient, scienapticClient vgScienapticPb.ScienapticClient,
	vgItrClient vgItr.ITRClient, s3Client provider.ITRIntimationS3Client, genConf *genconf.Config,
	crV2VClient creditreportv2.CreditReportManagerClient, lendabilityClient lendability.LendabilityClient, eventBroker events.Broker) (*userintel.Service, error) {
	wire.Build(
		dao.NewUserIntelCrdb,
		userintel.NewService,
		featurestore.FactoryWireSet,
		fennel.NewFennelClient,
		scienaptic.NewScienapticFeatureStoreClient,
		gormProvider,
		vendorstore.NewVendorStore,
		vendorstore.VendorStoreDAOWireSet,
		inteltypeparam.NewDevicePremiumnessProc,
		inteltypeparam.NewPhoneBillingTypeProc,
		inteltypeparam.NewUANPresenceProc,
		inteltypeparam.NewPayUAffluenceProc,
		inteltypeparam.NewHunterProc,
		inteltypeparam.NewUANByPanProc,
		inteltypeparam.NewIncomeEstimateProc,
		inteltypeparam.NewLendabilityProc,
		inteltypeparam.NewUserAffluenceProc,
		inteltypeparam.NewITRIntimationProc,
		inteltypeparam.NewAAIncomeEstimateProc,
		inteltypeparam.NewSMSParserProc,
		idgen.WireSet,
		idgen.NewClock,
		RateLimiterConfigProvider,
		DummyRateLimiterOptionsProvider,
		types.UserRedisStoreRedisClientProvider,
		ratelimiterWire.WireSet,
		provider.ITRIntimationS3ClientProvider,
	)
	return &userintel.Service{}, nil
}

func gormProvider(db types.EpifiCRDB) *gorm.DB {
	return db
}
