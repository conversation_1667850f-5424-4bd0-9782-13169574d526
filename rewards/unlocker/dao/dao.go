package dao

import (
	"context"

	"github.com/google/wire"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
)

var RewardsUnlockerDaoWireSet = wire.NewSet(NewPGDBUnlockerDao, wire.Bind(new(RewardsUnlockerDao), new(*PGDBUnlockerDao)))

type RewardsUnlockerDao interface {
	// GetRewardOffersForLockedRewards returns the reward offers that have rewards in
	// locked state for the actor, having given eventType as unlockEvent type and can
	// be potentially unlocked
	GetRewardOffersForLockedRewards(ctx context.Context, actorId string, eventType rewardsPb.CollectedDataType, rewardSubStatus rewardsPb.SubStatus) ([]*rewardOffersPb.RewardOffer, error)
	GetUnlockableRewardOffers(ctx context.Context, unlockEvent rewardsPb.CollectedDataType) ([]*rewardOffersPb.RewardOffer, error)
	RemoveExplicitLocksForActorAndOffer(ctx context.Context, actorId, offerId string, updatedStatus rewardsPb.RewardStatus, updatedSubStatus rewardsPb.SubStatus, eventType rewardsPb.CollectedDataType, eventRefId string) error
	RemoveImplicitLocksForActor(ctx context.Context, actorId, offerId string) error
}
