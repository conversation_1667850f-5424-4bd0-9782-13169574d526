#!/bin/bash
echo "Required input parameters are: target, environment, GIT_COMMIT, GIT_BRANCH"
echo "target=$target, environment=$environment, GIT_COMMIT=$GIT_COMMIT, GIT_BRANCH=$GIT_BRANCH"
true > env.properties
target=$target
environment=$environment
GIT_COMMIT=$GIT_COMMIT
GIT_BRANCH=$GIT_BRANCH
export source_directory_path="/var/lib/jenkins/artifacts/$environment/$target/"
cd packer || exit
packer build -debug -machine-readable -var "source_directory_path=$source_directory_path" -var "target=$target" -var "environment=$environment" -var "GIT_COMMIT=$GIT_COMMIT" -var "GIT_BRANCH=$GIT_BRANCH" golang.json |tee ./"$target"_packer.log
export IMAGE_ID=$(cat ./"$target"_packer.log | awk 'match($0, /ami-.*/) { print substr($0, RSTART, RLENGTH) }' | tail -n 1)
cd ..
{
echo "IMAGE_ID=$IMAGE_ID"
echo "target=$target"
echo "environment=$environment"
}  >> env.properties
