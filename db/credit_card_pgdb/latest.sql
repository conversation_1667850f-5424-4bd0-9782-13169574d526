CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TABLE public.card_audits (
    id character varying NOT NULL,
    card_id character varying NOT NULL,
    change_type character varying NOT NULL,
    details jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.card_audits IS 'table used for maintaining history for changes in cards table.';
COMMENT ON COLUMN public.card_audits.change_type IS 'Enum denoting type of flow';
COMMENT ON COLUMN public.card_audits.details IS 'column containing information related to each request type, example : in case of limit update we will store the current limits';
CREATE TABLE public.card_request_stages (
    id character varying NOT NULL,
    card_request_id character varying NOT NULL,
    external_request_id character varying NOT NULL,
    orch_id character varying NOT NULL,
    stage character varying NOT NULL,
    stage_execution_details jsonb NOT NULL,
    status character varying NOT NULL,
    sub_status character varying NOT NULL,
    staled_at timestamp with time zone,
    completed_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.card_request_stages IS 'table to store specific stage related data for card_requests';
COMMENT ON COLUMN public.card_request_stages.stage_execution_details IS 'metadata for the given stage';
COMMENT ON COLUMN public.card_request_stages.status IS 'high level status of the request stage';
COMMENT ON COLUMN public.card_request_stages.sub_status IS 'granular level status of the request stage';
COMMENT ON COLUMN public.card_request_stages.staled_at IS 'time to make step stale so that re-execution of the step can be done';
COMMENT ON COLUMN public.card_request_stages.completed_at IS 'time at which the request stage is completed';
CREATE TABLE public.card_requests (
    id character varying NOT NULL,
    card_id character varying NOT NULL,
    actor_id character varying NOT NULL,
    orch_id character varying NOT NULL,
    vendor character varying NOT NULL,
    request_details jsonb NOT NULL,
    next_action jsonb,
    workflow character varying NOT NULL,
    status character varying NOT NULL,
    provenance character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone,
    external_vendor_id character varying
);
COMMENT ON TABLE public.card_requests IS 'entity where we will keep track of all card requests and will store the state machine for each request';
COMMENT ON COLUMN public.card_requests.orch_id IS 'id for orchestrating the request at celestial/orchestrator';
COMMENT ON COLUMN public.card_requests.request_details IS 'Metadata for a given request. This can be user provided information or information collected internally for initiating the flow';
COMMENT ON COLUMN public.card_requests.next_action IS 'deeplink of the next screen to be shown to the user when client polls';
COMMENT ON COLUMN public.card_requests.status IS 'high level status of the request';
COMMENT ON COLUMN public.card_requests.provenance IS 'enum denoting entry point for the request, APP/SHERLOCK etc';
COMMENT ON COLUMN public.card_requests.external_vendor_id IS 'identifier used by external vendors for a given card request';
CREATE TABLE public.cc_offer_eligibility_criteria (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    vendor character varying NOT NULL,
    status character varying NOT NULL,
    vendor_response jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone,
    provenance character varying
);
COMMENT ON TABLE public.cc_offer_eligibility_criteria IS 'table to store all the actors who are eligible for cc from BA side';
COMMENT ON COLUMN public.cc_offer_eligibility_criteria.actor_id IS 'actor who is eligible for cc from BA';
COMMENT ON COLUMN public.cc_offer_eligibility_criteria.vendor IS '{"proto_type":"creditcard.Vendor", "comment":"vendor to whom cc offer is requested"}';
COMMENT ON COLUMN public.cc_offer_eligibility_criteria.status IS '{"proto_type":"creditcard.CreditCardOfferEligibilityCriteriaStatus", "comment":"status of the eligibility"}';
COMMENT ON COLUMN public.cc_offer_eligibility_criteria.vendor_response IS '{"proto_type":"creditcard.VendorResponse", "comment":"response timestamp from vendor"}';
CREATE TABLE public.cc_offers (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    vendor_offer_id character varying NOT NULL,
    vendor character varying NOT NULL,
    offer_constraints jsonb NOT NULL,
    valid_since timestamp with time zone NOT NULL,
    valid_till timestamp with time zone NOT NULL,
    deactivated_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone,
    cc_offer_eligibility_criteria_id character varying,
    card_program character varying,
    offer_details jsonb
);
COMMENT ON TABLE public.cc_offers IS 'table to store all the cc offers received from vendor';
COMMENT ON COLUMN public.cc_offers.actor_id IS 'actor for which cc offer is available';
COMMENT ON COLUMN public.cc_offers.vendor_offer_id IS 'id for the offer provided by vendor';
COMMENT ON COLUMN public.cc_offers.vendor IS '{"proto_type":"cc.Vendor", "comment":"vendor who has offered cc"}';
COMMENT ON COLUMN public.cc_offers.offer_constraints IS '{"proto_type":"ccPb.OfferConstraints", "comment":"cc offer constraints like max cc amount, max EMI amount, max cc tenure"}';
COMMENT ON COLUMN public.cc_offers.valid_since IS 'cc offer validity start time';
COMMENT ON COLUMN public.cc_offers.valid_till IS 'cc offer validity end time';
COMMENT ON COLUMN public.cc_offers.deactivated_at IS 'cc offer deactivation time';
COMMENT ON COLUMN public.cc_offers.created_at IS 'cc offer creation time';
COMMENT ON COLUMN public.cc_offers.updated_at IS 'cc offer latest update time';
COMMENT ON COLUMN public.cc_offers.card_program IS 'stores the details associated with card program under which an offer is created in arn format';
COMMENT ON COLUMN public.cc_offers.offer_details IS 'stores the details regarding the offer like the parent offer id in case of secured cards';
CREATE TABLE public.cc_recommendation_info (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    recommendation_details jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone,
    evaluated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);
CREATE TABLE public.credit_accounts (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    reference_id character varying NOT NULL,
    billed_amount double precision NOT NULL,
    unbilled_amount double precision NOT NULL,
    total_outstanding double precision NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone,
    total_limit double precision DEFAULT (0.0)::double precision NOT NULL,
    last_synced_at timestamp with time zone,
    card_program character varying,
    collateral_details jsonb,
    due_info jsonb
);
COMMENT ON TABLE public.credit_accounts IS 'table to store data relevant to credit card account';
COMMENT ON COLUMN public.credit_accounts.reference_id IS 'this may vary for vendor to vendor for the current vendor this will be entity id passed by us to vendor by the vendor for a card';
COMMENT ON COLUMN public.credit_accounts.card_program IS 'stores the details associated with card program under which an account is created in arn format';
COMMENT ON COLUMN public.credit_accounts.collateral_details IS 'stores the collateral information against which the credit line is issued';
COMMENT ON COLUMN public.credit_accounts.due_info IS 'stores the due information for the user';
CREATE TABLE public.credit_card_bills (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    account_id character varying NOT NULL,
    last_statement_balance jsonb,
    current_statement_amount jsonb,
    total_credit jsonb,
    total_debit jsonb,
    cash jsonb,
    purchase jsonb,
    min_due jsonb,
    total_due jsonb,
    statement_date timestamp with time zone NOT NULL,
    soft_due_date timestamp with time zone NOT NULL,
    hard_due_date timestamp with time zone NOT NULL,
    rewards_info jsonb,
    analytics_info jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone,
    statement_summary jsonb,
    available_limit jsonb,
    s3_path character varying,
    reward_id character varying
);
CREATE TABLE public.credit_card_payment_info (
    id character varying NOT NULL,
    bill_info_id character varying,
    external_txn_id character varying,
    order_id character varying,
    vendor_txn_ref_no character varying NOT NULL,
    payment_date timestamp with time zone NOT NULL,
    amount jsonb,
    payment_status character varying NOT NULL,
    payment_sub_status character varying,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone,
    account_id character varying,
    repayment_type character varying
);
CREATE TABLE public.credit_card_sku_overrides (
    actor_id character varying NOT NULL,
    card_sku_type character varying NOT NULL,
    feature_override_info jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.credit_card_sku_overrides IS 'table to store the special provisions for an actor like more number of card provisions, etc.';
COMMENT ON COLUMN public.credit_card_sku_overrides.feature_override_info IS 'blob containing the information regarding the override information specific to the actor';
CREATE TABLE public.credit_card_skus (
    sku_type character varying NOT NULL,
    type character varying NOT NULL,
    feature_info jsonb NOT NULL,
    vendor_card_sku jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.credit_card_skus IS 'table to define the card variants offered by Epifi';
COMMENT ON COLUMN public.credit_card_skus.type IS 'type of card as in DEBIT/CREDIT';
COMMENT ON COLUMN public.credit_card_skus.feature_info IS 'card variant information like number of free cards, etc';
COMMENT ON COLUMN public.credit_card_skus.vendor_card_sku IS 'Vendor specific information related to the above features';
CREATE TABLE public.credit_card_transactions (
    id character varying NOT NULL,
    account_id character varying NOT NULL,
    card_id character varying NOT NULL,
    amount jsonb NOT NULL,
    balance jsonb NOT NULL,
    txn_time timestamp with time zone NOT NULL,
    txn_status character varying NOT NULL,
    txn_category character varying NOT NULL,
    txn_origin character varying NOT NULL,
    txn_type character varying NOT NULL,
    beneficiary_info jsonb,
    conversion_info jsonb,
    description character varying,
    dispute_info jsonb,
    external_txn_id character varying,
    bill_ref_no character varying,
    bank_txn_id character varying,
    auth_code character varying,
    acquirer_id character varying,
    retrieval_reference_no character varying,
    sor_txn_id character varying,
    txn_reference_no character varying,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone,
    failure_info jsonb,
    vendor_ext_txn_id character varying,
    parent_transaction_id character varying,
    child_transaction_ids jsonb,
    dedupe_id character varying,
    transaction_authorization_status character varying
);
COMMENT ON COLUMN public.credit_card_transactions.dedupe_id IS '{"proto_type":"firefly.accounting.internal.cardTransaction.dedupe_id", "comment": "Unique derived id to uniquely identify transaction from provided vendor data"}';
CREATE TABLE public.credit_cards (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    account_id character varying NOT NULL,
    form character varying NOT NULL,
    network_type character varying NOT NULL,
    vendor_identifier character varying NOT NULL,
    vendor character varying NOT NULL,
    controls_data jsonb NOT NULL,
    issuance_fee jsonb NOT NULL,
    basic_info jsonb NOT NULL,
    state character varying NOT NULL,
    limits jsonb NOT NULL,
    card_sku_type character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.credit_cards IS 'table to store data relevant to credit card';
COMMENT ON COLUMN public.credit_cards.form IS 'whether the card is in physical or digital form';
COMMENT ON COLUMN public.credit_cards.vendor_identifier IS 'the unique id for the card on the vendors end';
COMMENT ON COLUMN public.credit_cards.controls_data IS 'Blob containing information regarding the card controls i.e. if a given control is enabled or disabled';
COMMENT ON COLUMN public.credit_cards.basic_info IS 'to store the tokenized card number, expiry, emboss name, masked_card_number';
COMMENT ON COLUMN public.credit_cards.limits IS 'card limit wrt region and control i.e domestic, international, etc.';
CREATE TABLE public.disputed_transactions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    transaction_id character varying NOT NULL,
    actor_id character varying NOT NULL,
    account_id character varying NOT NULL,
    ext_dispute_ref character varying NOT NULL,
    dispute_state character varying NOT NULL,
    disputed_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone,
    dispute_details jsonb
);
COMMENT ON TABLE public.disputed_transactions IS 'entity to keep track of disputed transactions';
COMMENT ON COLUMN public.disputed_transactions.transaction_id IS 'id of transaction from credit_card_transactions table';
COMMENT ON COLUMN public.disputed_transactions.ext_dispute_ref IS 'external dispute reference id from vendor';
COMMENT ON COLUMN public.disputed_transactions.dispute_state IS 'current state of dispute';
COMMENT ON COLUMN public.disputed_transactions.disputed_at IS 'timestamp at which dispute was raised';
CREATE TABLE public.do_once_tasks (
    task_name character varying NOT NULL,
    deleted_at_unix bigint DEFAULT (0)::bigint NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);
COMMENT ON TABLE public.do_once_tasks IS 'to identify tasks by their unique names that should be done exactly once';
COMMENT ON COLUMN public.do_once_tasks.task_name IS 'unique identifier for a task';
COMMENT ON COLUMN public.do_once_tasks.deleted_at_unix IS 'non-zero for soft-deleted tasks so that another task with same name can be created again once previous task is soft-deleted';
CREATE TABLE public.loan_account (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    transaction_loan_offers_id character varying,
    vendor_loan_id character varying,
    tenure_in_months bigint,
    amount_info jsonb,
    interest_info jsonb,
    fee_info jsonb,
    repayment_info jsonb,
    summary jsonb,
    loan_schedule jsonb,
    status character varying,
    disbursed_date date,
    loan_end_date date,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
CREATE TABLE public.transaction_additional_infos (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    transaction_id character varying NOT NULL,
    pi_to character varying NOT NULL,
    pi_from character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone,
    actor_from character varying,
    actor_to character varying,
    txn_time timestamp with time zone NOT NULL,
    enriched_beneficiary_info jsonb,
    bill_ref_id character varying
);
CREATE TABLE public.transaction_loan_offers (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    transaction_id character varying,
    tenure_in_months bigint,
    vendor_loan_request_id character varying,
    amount_info jsonb,
    interest_info jsonb,
    processing_fee_info jsonb,
    summary jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);
ALTER TABLE ONLY public.card_audits
    ADD CONSTRAINT card_audits_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.card_request_stages
    ADD CONSTRAINT card_request_stages_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.card_requests
    ADD CONSTRAINT card_requests_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.cc_offer_eligibility_criteria
    ADD CONSTRAINT cc_offer_eligibility_criteria_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.cc_offers
    ADD CONSTRAINT cc_offers_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.cc_recommendation_info
    ADD CONSTRAINT cc_recommendation_info_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.credit_accounts
    ADD CONSTRAINT credit_accounts_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.credit_card_bills
    ADD CONSTRAINT credit_card_bills_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.credit_card_payment_info
    ADD CONSTRAINT credit_card_payment_info_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.credit_card_sku_overrides
    ADD CONSTRAINT credit_card_sku_overrides_pkey PRIMARY KEY (actor_id, card_sku_type);
ALTER TABLE ONLY public.credit_card_skus
    ADD CONSTRAINT credit_card_skus_pkey PRIMARY KEY (sku_type, type);
ALTER TABLE ONLY public.credit_card_transactions
    ADD CONSTRAINT credit_card_transactions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.credit_cards
    ADD CONSTRAINT credit_cards_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.disputed_transactions
    ADD CONSTRAINT disputed_transactions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.do_once_tasks
    ADD CONSTRAINT do_once_tasks_pkey PRIMARY KEY (task_name, deleted_at_unix);
ALTER TABLE ONLY public.loan_account
    ADD CONSTRAINT loan_account_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
ALTER TABLE ONLY public.transaction_additional_infos
    ADD CONSTRAINT transaction_additional_infos_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.transaction_loan_offers
    ADD CONSTRAINT transaction_loan_offers_pkey PRIMARY KEY (id);
CREATE INDEX card_audits_updated_at_idx ON public.card_audits USING btree (updated_at);
CREATE INDEX card_request_stage_card_request_id_stage_idx ON public.card_request_stages USING btree (card_request_id, stage);
CREATE INDEX card_request_stage_orch_id ON public.card_request_stages USING btree (orch_id);
CREATE INDEX card_request_stages_external_request_id ON public.card_request_stages USING btree (external_request_id);
CREATE INDEX card_request_stages_updated_at ON public.card_request_stages USING btree (updated_at);
CREATE INDEX card_requests_actor_id ON public.card_requests USING btree (actor_id);
CREATE INDEX card_requests_card_id_idx ON public.card_requests USING btree (card_id);
CREATE INDEX card_requests_ext_vendor_id_idx ON public.card_requests USING btree (external_vendor_id);
CREATE INDEX card_requests_orch_id_idx ON public.card_requests USING btree (orch_id);
CREATE INDEX card_requests_updated_at ON public.card_requests USING btree (updated_at);
CREATE INDEX cc_offer_eligibility_criteria_actor_id_idx ON public.cc_offer_eligibility_criteria USING btree (actor_id);
CREATE INDEX cc_offer_eligibility_criteria_updated_at_idx ON public.cc_offer_eligibility_criteria USING btree (updated_at DESC);
CREATE INDEX cc_offers_actor_id_idx ON public.cc_offers USING btree (actor_id);
CREATE INDEX cc_offers_updated_at_idx ON public.cc_offers USING btree (updated_at);
CREATE INDEX cc_recommendation_info_actor_id_lookup_idx ON public.cc_recommendation_info USING btree (actor_id);
CREATE INDEX cc_recommendation_info_updated_at_idx ON public.cc_recommendation_info USING btree (updated_at);
CREATE INDEX credit_accounts_actor_id_idx ON public.credit_accounts USING btree (actor_id);
CREATE INDEX credit_accounts_reference_id_idx ON public.credit_accounts USING btree (reference_id);
CREATE INDEX credit_accounts_updated_at_idx ON public.credit_accounts USING btree (updated_at);
CREATE INDEX credit_card_bills_account_id_idx ON public.credit_card_bills USING btree (account_id);
CREATE INDEX credit_card_bills_actor_id_idx ON public.credit_card_bills USING btree (actor_id);
CREATE INDEX credit_card_bills_statement_date_idx ON public.credit_card_bills USING btree (statement_date DESC);
CREATE INDEX credit_card_bills_updated_at_idx ON public.credit_card_bills USING btree (updated_at DESC);
CREATE INDEX credit_card_loan_account_actor_id_and_status_idx ON public.loan_account USING btree (actor_id, status);
CREATE INDEX credit_card_loan_account_updated_at_idx ON public.loan_account USING btree (updated_at);
CREATE INDEX credit_card_loan_account_vendor_loan_id_idx ON public.loan_account USING btree (vendor_loan_id);
CREATE INDEX credit_card_payment_info_account_id ON public.credit_card_payment_info USING btree (account_id);
CREATE INDEX credit_card_payment_info_bill_info_idx ON public.credit_card_payment_info USING btree (bill_info_id);
CREATE INDEX credit_card_payment_info_order_id_idx ON public.credit_card_payment_info USING btree (order_id);
CREATE INDEX credit_card_payment_info_transaction_id_idx ON public.credit_card_payment_info USING btree (external_txn_id);
CREATE INDEX credit_card_payment_info_updated_at_idx ON public.credit_card_payment_info USING btree (updated_at DESC);
CREATE INDEX credit_card_payment_info_vendor_txn_ref_no ON public.credit_card_payment_info USING btree (vendor_txn_ref_no);
CREATE INDEX credit_card_sku_overrides_updated_at_idx ON public.credit_card_sku_overrides USING btree (updated_at);
CREATE INDEX credit_card_skus_updated_at_idx ON public.credit_card_skus USING btree (updated_at);
CREATE INDEX credit_card_transaction_loan_offers_actor_id_txn_id_tenure_idx ON public.transaction_loan_offers USING btree (actor_id, transaction_id, tenure_in_months);
CREATE INDEX credit_card_transaction_loan_offers_updated_at_idx ON public.transaction_loan_offers USING btree (updated_at);
CREATE INDEX credit_card_transaction_loan_offers_vendor_loan_request_id_idx ON public.transaction_loan_offers USING btree (vendor_loan_request_id);
CREATE INDEX credit_card_transactions_account_id_idx ON public.credit_card_transactions USING btree (account_id);
CREATE INDEX credit_card_transactions_card_id_idx ON public.credit_card_transactions USING btree (card_id);
CREATE INDEX credit_card_transactions_external_txn_id_idx ON public.credit_card_transactions USING btree (external_txn_id);
CREATE INDEX credit_card_transactions_retrieval_reference_no_idx ON public.credit_card_transactions USING btree (retrieval_reference_no);
CREATE UNIQUE INDEX credit_card_transactions_unique_dedupe_deleted_at_key ON public.credit_card_transactions USING btree (dedupe_id, deleted_at);
CREATE INDEX credit_card_transactions_updated_at_idx ON public.credit_card_transactions USING btree (updated_at);
CREATE INDEX credit_card_transactions_vendor_ext_txn_id_idx ON public.credit_card_transactions USING btree (vendor_ext_txn_id);
CREATE INDEX credit_cards_account_id_idx ON public.credit_cards USING btree (account_id);
CREATE INDEX credit_cards_actor_id_idx ON public.credit_cards USING btree (actor_id);
CREATE INDEX credit_cards_updated_at_idx ON public.credit_cards USING btree (updated_at);
CREATE INDEX credit_cards_vendor_identifier ON public.credit_cards USING btree (vendor_identifier);
CREATE UNIQUE INDEX disputed_transactions_transaction_id_actor_id_key ON public.disputed_transactions USING btree (transaction_id, actor_id);
CREATE INDEX disputed_transactions_updated_at_idx ON public.disputed_transactions USING btree (updated_at);
CREATE INDEX do_once_tasks_updated_at_idx ON public.do_once_tasks USING btree (updated_at);
CREATE INDEX transaction_additional_infos_actor_id_to_and_from ON public.transaction_additional_infos USING btree (actor_from, actor_to);
CREATE INDEX transaction_additional_infos_bill_ref_id ON public.transaction_additional_infos USING btree (bill_ref_id);
CREATE INDEX transaction_additional_infos_transaction_id ON public.transaction_additional_infos USING btree (transaction_id);
CREATE INDEX transaction_additional_infos_updated_at_idx ON public.transaction_additional_infos USING btree (updated_at);
