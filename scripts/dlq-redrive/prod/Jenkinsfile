@Library("epifi-jenkins-libraries") _
pipeline {
    agent {
        label "${params.TENANT}-worker"
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        timestamps()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '10'))
    }
    parameters {
        string(name: 'QUEUE', defaultValue: '', description: 'original queue to redrive (Ommit dlq and env Ex for prod-dlq-rewards-data-collector-queue enter rewards-data-collector-queue)')
        choice(name: 'TENANT',choices: ['epifi', 'stockguardian'], description: 'Select the tenant')
        choice(name: 'ENV',choices: ['prod'], description: 'Production environment')
        booleanParam(name: 'REFRESH', defaultValue: false, description: 'Dev<PERSON>, ignore this variable! This is for DevOps to refresh Jenkinsfile.')
    }
    environment {
        SOURCE_QUEUE = "${params.ENV}-dlq-${params.QUEUE}"
        DEST_QUEUE = "${params.ENV}-${params.QUEUE}"
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true}
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-${ENV}-REFRESH:${params.REFRESH}"
                    currentBuild.description = "#${BUILD_NUMBER}-${ENV}-REFRESH:${params.REFRESH}"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }

        stage('Start DLQ Redrive') {
            steps {
                script {
                    sh """
                        SOURCE_QUEUE_URL=\$(aws sqs get-queue-url --queue-name ${env.SOURCE_QUEUE} --output text)
                        SOURCE_QUEUE_ARN=\$(aws sqs get-queue-attributes --queue-url \$SOURCE_QUEUE_URL --attribute-names QueueArn --output text --query 'Attributes.QueueArn')

                        DEST_QUEUE_URL=\$(aws sqs get-queue-url --queue-name ${env.DEST_QUEUE} --output text)
                        DEST_QUEUE_ARN=\$(aws sqs get-queue-attributes --queue-url \$DEST_QUEUE_URL --attribute-names QueueArn --output text --query 'Attributes.QueueArn')

                        aws sqs start-message-move-task --source-arn \$SOURCE_QUEUE_ARN --destination-arn \$DEST_QUEUE_ARN
                    """
                }
            }
        }
    }
}