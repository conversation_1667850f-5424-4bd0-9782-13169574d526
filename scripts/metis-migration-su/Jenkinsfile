pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '20'))
    }
    parameters {
        choice(name: 'ENV', choices: ['staging'], description: 'Select the environment where you want to run database migration ?')
        string(name:"BRANCH", defaultValue: "", description:'Gamma branch name')
        choice(name: 'DATABASE_NAME', choices: ['connected_account', 'comms'], description: 'Database to run migration for ?')
        choice(name: 'TYPE', choices:['up','down'], description: 'Type of migration to run.')
        string(name: 'N', defaultValue: '0', description: 'Specify the number of migration count to run ?')
        booleanParam(name: 'REFRESH', defaultValue: false, description: 'Devs, ignore this variable! This is for DevOps to refresh Jenkinsfile.')
    }
    environment {
        DATABASE_NAME = "${params.DATABASE_NAME}"
        ENV = "${params.ENV}"
        TYPE = "${params.TYPE}"
        N = "${params.N}".toInteger()
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true }
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-Refresh"
                    currentBuild.description = "#${BUILD_NUMBER}-Refresh"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage("Run Migration") {
            steps {
              dir("gamma"){
                script {
                  sh '''#!/bin/bash
                  
                    echo ${DATABASE_NAME}
                    
                    if [ "$DATABASE_NAME" == "connected_account" ]
                    then
                      SECRET_ID='prod/rds/epifimetis/connected-account'
                      USERNAME="\$(aws secretsmanager get-secret-value --secret-id \${SECRET_ID} --query SecretString --output text --region ap-south-1 | jq -r .username)"
                      PASSWORD="\$(aws secretsmanager get-secret-value --secret-id \${SECRET_ID} --query SecretString --output text --region ap-south-1 | jq -r .password)"
                      POSTGRES_URL="postgresql://\$USERNAME:\$<EMAIL>:5432/$DATABASE_NAME?sslmode=require"
                    fi

                    if [ "$DATABASE_NAME" == "comms" ]
                    then
                      SECRET_ID='prod/rds/epifiminerva/comms'
                      USERNAME="\$(aws secretsmanager get-secret-value --secret-id \${SECRET_ID} --query SecretString --output text --region ap-south-1 | jq -r .username)"
                      PASSWORD="\$(aws secretsmanager get-secret-value --secret-id \${SECRET_ID} --query SecretString --output text --region ap-south-1 | jq -r .password)"
                      POSTGRES_URL="postgresql://\$USERNAME:\$<EMAIL>:5432/$DATABASE_NAME?sslmode=require"
                    fi

                    echo "Running Migration for target: $DATABASE_NAME to latest sql"
                    if [ $N != 0 ] 
                    then
                      docker run -v ${WORKSPACE}/gamma/db/${DATABASE_NAME}/migrations:/migrations --rm migrate/migrate:v4.9.1 -database \${POSTGRES_URL} -path /migrations/ ${TYPE} ${N}
                    else
                      echo "Migration with N=0 not supported."
                      exit 1
                    fi
                  '''
                }
              }
            }
        }
    }
}
