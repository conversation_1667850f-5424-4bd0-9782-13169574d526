#!/bin/bash
set -e
set -o pipefail

make docker-build || { echo 'docker-build step failed' ; exit 1; }
docker create -it --name copy_artifact_entity-matcher entity-matcher:latest || { echo 'docker-create step failed' ; exit 1; }
mkdir -p ./output/entity_matcher
## copy code
docker cp copy_artifact_entity-matcher:/home/<USER>/entity-matcher/ ./output/
## remove docker image
docker rm -f copy_artifact_entity-matcher
## cleanup dangling images, stopped containers
docker system prune -f --filter "until=1h"