#!/bin/bash
set -e 
set -o pipefail
## 2-step docker build. performs
## 1. creates venev
## 2. installs requirements
## 3. in 2nd step - copies all code including model files and venv files
make docker-build || { echo 'docker-build step failed' ; exit 1; }
docker create -it --name copy_artifact_emailparser emailparser:latest || { echo 'docker-create step failed' ; exit 1; }
mkdir -p ./output/EmailParser
## copy code
docker cp copy_artifact_emailparser:/home/<USER>/EmailParser/ ./output/
## remove docker image
docker rm -f copy_artifact_emailparser
## cleanup dangling images, stopped containers
docker system prune -f --filter "until=1h" 
