@Library('epifi-jenkins-libraries') _

pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools{
        go 'Go stable'
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '10', artifactNumToKeepStr: '10'))
        timestamps()
    }
    parameters {
        choice(name: 'ENV', choices: ['demo', 'staging', 'qa', 'uat', 'deploy'], description: 'Environment where you want to build binary')
        string(name: 'BRANCH', defaultValue:"master", description: 'Branch name for epifi repo')
        string(name: 'SCRIPT_FOLDER', defaultValue:"", description: 'Path of the script folder in <gamma_repo_root>/scripts/')
        booleanParam(name: 'REFRESH', defaultValue: false, description: '<PERSON><PERSON>, ignore this variable! This is for DevOps to refresh Jenkinsfile.')
    }
    environment {
        ENV = "${params.ENV}"
        BRANCH = "${params.BRANCH}"
        SCRIPT_FOLDER="${params.SCRIPT_FOLDER}"
        GOMODCACHE = "/var/cache/go/master/mod"
        GOCACHE = "/var/cache/go/master/go-build"
        GOPROXY = "https://goproxy.pointz.in"
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true }
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-Refresh"
                    currentBuild.description = "#${BUILD_NUMBER}-Refresh"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage("Get Gamma Repo") {
            steps {
                gitCheckout(repoName: "gamma", gitBranch: "${BRANCH}", githubOrg: "epifi")
                dir("certs"){
                    sh "touch ${WORKSPACE}/.env"
                }
            }
        }
        stage('Assume Role') {
            steps {
                script {
                    dir('certs') {
                        deleteDir()
                        ROLE_ARN = sh(script: "set +x && echo \${${"${ENV}".toUpperCase()}_ACCOUNT_ROLE}", returnStdout: true).trim()
                        echo "Role ARN to be used for ${ENV} is: ${ROLE_ARN}"
                        aws_credentials_json = sh(script: "set +x && aws sts assume-role --role-arn '${ROLE_ARN}' --role-session-name ${ENV}-${env.TARGET}-deploy --region ap-south-1", returnStdout: true).trim()
                        AWS_ACCESS_KEY_ID = sh(script: "set +x && echo '${aws_credentials_json}' | jq --exit-status --raw-output .Credentials.AccessKeyId", returnStdout: true).trim()
                        AWS_SECRET_ACCESS_KEY = sh(script: "set +x && echo '$aws_credentials_json' | jq --exit-status --raw-output .Credentials.SecretAccessKey", returnStdout: true).trim()
                        AWS_SESSION_TOKEN = sh(script: "set +x && echo '$aws_credentials_json' | jq --exit-status --raw-output .Credentials.SessionToken", returnStdout: true).trim()
                        sh "set +x && echo \"export AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}; export AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}; export AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN}\" > ${WORKSPACE}/.env"
                    }
                }
            }
        }
        stage("Build and Upload Binary") {
            steps {
                dir("gamma") {
                    script {
                        sh """
                            set +x
                            
                            . ${WORKSPACE}/.env

                            make build-script target=$SCRIPT_FOLDER
                            cd ./output/$SCRIPT_FOLDER

                            aws s3 sync . "s3://epifi-${ENV}-jenkins-job-binaries/scripts/$SCRIPT_FOLDER" --delete
                            echo "Pushed Jenkins binary to s3://epifi-${ENV}-jenkins-job-binaries/scripts/$SCRIPT_FOLDER"
                            set -x
                        """
                    }
                }
            }
        }
    }
    post {
        always{
            script {
               dir('certs') {
                    try {
                        deleteDir()
                    } catch(Exception e){
                        echo "${e.getMessage()}"
                    }
               }
            }
        }
    }
}
