@Library('epifi-jenkins-libraries') _

pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools{
        go 'Go stable'
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '10', artifactNumToKeepStr: '10'))
        timestamps()
    }
    parameters {
        string(name: 'SCRIPT_FOLDER', defaultValue:"", description: 'Name of the script in <gamma_repo_root>/scripts/')
        booleanParam(name: 'REFRESH', defaultValue: false, description: '<PERSON><PERSON>, ignore this variable! This is for DevOps to refresh Jenkinsfile.')
    }
    environment {
        SCRIPT_FOLDER="${params.SCRIPT_FOLDER}"
        GOPROXY = "https://goproxy.epifi.in"
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true }
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-Refresh"
                    currentBuild.description = "#${BUILD_NUMBER}-Refresh"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage("Get Gamma Repo") {
            steps {
                gitCheckout(repoName: "gamma", gitBranch: "master", githubOrg: "epifi")
                dir("certs"){
                    sh "touch ${WORKSPACE}/.env"
                }
            }
        }
        stage("Build and Upload Binary") {
            steps {
                dir("gamma") {
                    script {
                        sh """
                            set +x

                            . ${WORKSPACE}/.env

                            make build-script target=$SCRIPT_FOLDER
                            cd ./output/$SCRIPT_FOLDER

                            aws s3 sync . "s3://epifi-prod-jenkins-job-binaries/scripts/$SCRIPT_FOLDER" --delete
                            echo "Pushed Jenkins binary to s3://epifi-prod-jenkins-job-binaries/scripts/$SCRIPT_FOLDER"
                            set -x
                        """
                    }
                }
            }
        }
    }
    post {
        always{
            script {
               dir('certs') {
                    try {
                        deleteDir()
                    } catch(Exception e){
                        echo "${e.getMessage()}"
                    }
               }
            }
        }
    }
}
