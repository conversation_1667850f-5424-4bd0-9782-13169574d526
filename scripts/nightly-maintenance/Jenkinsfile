@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
    }

    parameters {
        //choice(name: 'ENV', choices: ['cx-uat'], description: 'Environment')
        choice(name: 'ACTION', choices: ['startup','shutdown'], description: 'Action to be performed')
        //choice(name: 'BINARY_VERSION', choices: ['0.0.10'], description: 'Go Binary Version')
        //booleanParam(name: 'UPDATE_ASG', defaultValue: false, description: 'If true, then selected action will be taken on ASG')
        //booleanParam(name: 'UPDATE_RDS', defaultValue: false, description: 'If true, then selected action will be taken on RDS')
        //booleanParam(name: 'UPDATE_ALL_INSTANCES', defaultValue: false, description: 'If true, then selected action will be taken on all the instances')
        booleanParam(name: 'REFRESH', defaultValue: false, description: 'This is for DevOps to refresh Jenkins<PERSON>le.')
    }
    
    environment {
        ENV = "${params.ENV}"
    }

    stages {
        stage("Refresh") {
            when {
                expression { return params.REFRESH == true }
            }
            steps {
                script {
                    if (params.REFRESH == true) {
                        echo "Jenkins file was loaded....... finish build now"
                        currentBuild.displayName = "#${BUILD_NUMBER}-Refresh"
                        currentBuild.description = "#${BUILD_NUMBER}-Refresh"
                        currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                        sleep(5)
                    }
                }
            }
        }
        stage('Download script') {
            steps {
                script {
                    sh "aws s3 cp s3://epifi-go-infra-bin/nightly_maintenance/linux/amd64/0.0.19 nightly_maintenance"
                }
            }
        }
        stage('Make script executable') {
            steps {
                sh 'chmod a+x nightly_maintenance'
            }
        }
        stage('Update ASG') {
            when {
                expression { return params.UPDATE_ASG == true }
            }
            steps {
                script {
                    sh(script:"set +x && ./nightly_maintenance -env ${params.ENV} -action ${params.ACTION} -update-asg ${params.UPDATE_ASG}")
                }
            }
        }
        stage('Update RDS') {
            when {
                expression { return params.UPDATE_RDS == true }
            }
            steps {
                script {
                    sh(script:"set +x && ./nightly_maintenance -env ${params.ENV} -action ${params.ACTION} -update-rds ${params.UPDATE_RDS}")
                }
            }
        }
        stage('Update all instances') {
            when {
                expression { return params.UPDATE_ALL_INSTANCES == true }
            }
            steps {
                script {
                    sh(script:"set +x && ./nightly_maintenance -env ${params.ENV} -action ${params.ACTION} -update-all-instances ${params.UPDATE_ALL_INSTANCES}")
                }
            }
        }

    }
}