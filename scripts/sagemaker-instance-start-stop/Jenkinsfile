@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'epifi-data-prod-jenkins-cloud-nodes'}
    options {
            ansiColor('xterm')
            disableConcurrentBuilds()
            buildDiscarder(logRotator(numToKeepStr: '5', artifactNumToKeepStr: '5'))
        }
    triggers {
        parameterizedCron('''
            TZ=Asia/Kolkata\n 00 17 * * * %ENV=data-prod;InstanceState=start;InstanceName=Analytics-Jupyter-Notebook
        ''')
    }
    parameters {
        choice(name: 'ENV', choices: ['data-prod'], description: 'Environment in which to execute the script')
        choice(name: 'InstanceState', choices: ['start', 'stop'], description: 'Whether to start or stop the instance')
        choice(name: 'InstanceName', choices: ['Analytics-Jupyter-Notebook'], description: 'Notebook instance name')
        booleanParam(name: 'REFRESH', defaultValue: false, description: '<PERSON><PERSON>, ignore this variable! This is for DevOps to refresh <PERSON><PERSON>le.')
    }
    environment {
        ENV = "${params.ENV}"
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true}
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-REFRESH:${params.REFRESH}"
                    currentBuild.description = "#${BUILD_NUMBER}-REFRESH:${params.REFRESH}"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Execute') {
            steps {
                script {

                sh """#!/bin/bash
                    set -x
                    echo "Environment to run Instance ${env} - ${InstanceName}"

                    if [[ "$InstanceState" == "start" ]]; then
                        aws  sagemaker   start-notebook-instance --notebook-instance-name ${InstanceName}
                    elif [[ "$InstanceState" == "stop" ]]; then
                        aws  sagemaker   stop-notebook-instance --notebook-instance-name ${InstanceName}
                    fi
                    set +x
                """
                }
            }
        }
    }
}