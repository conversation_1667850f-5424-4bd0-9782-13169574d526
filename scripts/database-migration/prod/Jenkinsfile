pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '20'))
    }
    parameters {
        choice(name: 'ENV', choices: ['prod'], description: 'Select the environment where you want to run database migration ?')
        choice(name: 'DATABASE_NAME', choices: ['epifi', 'epifi_wealth', 'credit_card', 'federal', 'p2pinvestment_liquiloans', 'usstocks_alpaca', 'frm', 'pl_liquiloans', 'pl_idfc', 'loans_fiftyfin_crdb'], description: 'Database to run migration for ?')
        string(name: 'N', defaultValue: '0', description: 'Specify the number of migration count to run ?')
        booleanParam(name: 'REFRESH', defaultValue: false, description: 'Devs, ignore this variable! This is for DevOps to refresh Jenkinsfile.')
    }
    environment {
        DATABASE_NAME = "${params.DATABASE_NAME}"
        ENV = "${params.ENV}"
        N = "${params.N}".toInteger()
    }
    stages {
        stage('GET CRDB CERTS') {
            steps {
                script {
                    if (params.REFRESH == true) {
                        echo "Jenkins file was loaded....... finish build now"
                        currentBuild.displayName = "#${BUILD_NUMBER}-Refresh"
                        currentBuild.description = "#${BUILD_NUMBER}-Refresh"
                        currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                        sleep(5)
                    }
                    dir('certs') {
                        deleteDir()
                        sh(script: "aws secretsmanager get-secret-value --secret-id ${ENV}/cockroach/ca.crt --region ap-south-1 | jq -r '.SecretString' > ca.crt")
                        sh(script: "aws secretsmanager get-secret-value --secret-id ${ENV}/cockroach/client.root.crt --region ap-south-1 | jq -r '.SecretString' > client.root.crt")
                        sh(script: "aws secretsmanager get-secret-value --secret-id ${ENV}/cockroach/client.root.key --region ap-south-1 | jq -r '.SecretString | fromjson | .\"client.root.key\"' | tr ' ' '\\n' | sed -z 's/BEGIN\\nRSA\\nPRIVATE\\nKEY/BEGIN RSA PRIVATE KEY/g' | sed -z 's/END\\nRSA\\nPRIVATE\\nKEY/END RSA PRIVATE KEY/g' > client.root.key")
                        sh(script: "chmod 0600 client.root.key")
                    }
                }
            }
        }
        stage('Get Gamma Repo') {
            steps {
                dir('gamma') {
                    deleteDir()
                    checkout([
                        $class: 'GitSCM',
                        branches: [[name: "master"]],
                        extensions: [[
                            $class: 'CloneOption',
                            shallow: true,
                            depth:   1,
                            timeout: 30
                        ]],
                        userRemoteConfigs: [[
                            url:           'https://github.com/epiFi/gamma.git',
                            credentialsId: 'github-app-epifi'
                        ]]
                    ])
                }
            }
        }
        stage("Run Migration") {
            steps {
                script {
                    sh "chmod 777 ${env.WORKSPACE}/Jenkins/scripts/db-migration.sh && ${env.WORKSPACE}/Jenkins/scripts/db-migration.sh ${ENV} ${DATABASE_NAME} ${N}"
                }
            }
        }
    }
}
