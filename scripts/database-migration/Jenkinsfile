pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '20'))
    }
    parameters {
        choice(name: 'ENV', choices: ['demo', 'staging', 'qa', 'uat'], description: 'Select the environment where you want to run database migration ?')
        choice(name: 'DATABASE_NAME', choices: ['epifi', 'epifi_wealth', 'simulator', 'credit_card', 'federal', 'p2pinvestment_liquiloans', 'usstocks_alpaca', 'frm', 'pl_liquiloans', 'pl_idfc', 'loans_fiftyfin_crdb'], description: 'Database to run migration for ?')
        string(name: 'N', defaultValue: '0', description: 'Specify the number of migration count to run ?')
        string(name: 'GAMMA_BRANCH', defaultValue: 'master', description: 'Specify gamma branch')
        booleanParam(name: 'REFRESH', defaultValue: false, description: '<PERSON><PERSON>, ignore this variable! This is for DevOps to refresh Jenkins<PERSON>le.')
    }
    environment {
        DATABASE_NAME = "${params.DATABASE_NAME}"
        ENV = "${params.ENV}"
        N = "${params.N}".toInteger()
        GAMMA_BRANCH = "${params.GAMMA_BRANCH}"
    }
    stages {
        stage('GET CRDB CERTS') {
            steps {
                script {
                    if (params.REFRESH == true) {
                        echo "Jenkins file was loaded....... finish build now"
                        currentBuild.displayName = "#${BUILD_NUMBER}-Refresh"
                        currentBuild.description = "#${BUILD_NUMBER}-Refresh"
                        currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                        sleep(5)
                    }
                    dir('certs') {
                        deleteDir()
                        ROLE_ARN = sh(script: "set +x && echo \${${"${ENV}".toUpperCase()}_ACCOUNT_ROLE}", returnStdout: true).trim()
                        echo "Role ARN to be used for ${ENV} is: ${ROLE_ARN}"
                        aws_credentials_json = sh(script: "set +x && aws sts assume-role --role-arn '${ROLE_ARN}' --role-session-name ${ENV}-${env.TARGET}-deploy --region ap-south-1", returnStdout: true).trim()
                        AWS_ACCESS_KEY_ID = sh(script: "set +x && echo '${aws_credentials_json}' | jq --exit-status --raw-output .Credentials.AccessKeyId", returnStdout: true).trim()
                        AWS_SECRET_ACCESS_KEY = sh(script: "set +x && echo '$aws_credentials_json' | jq --exit-status --raw-output .Credentials.SecretAccessKey", returnStdout: true).trim()
                        AWS_SESSION_TOKEN = sh(script: "set +x && echo '$aws_credentials_json' | jq --exit-status --raw-output .Credentials.SessionToken", returnStdout: true).trim()
                        sh(script: "set +x && AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}  AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN} aws secretsmanager get-secret-value --secret-id ${ENV}/cockroach/ca.crt --region ap-south-1 | jq -r '.SecretString' > ca.crt")
                        sh(script: "set +x && AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}  AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN} aws secretsmanager get-secret-value --secret-id ${ENV}/cockroach/client.root.crt | jq -r '.SecretString' > client.root.crt")
                        sh(script: "set +x && AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}  AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN}  aws secretsmanager get-secret-value --secret-id ${ENV}/cockroach/client.root.key | jq -r '.SecretString | fromjson | .\"client.root.key\"' | tr ' ' '\\n' | sed -z 's/BEGIN\\nRSA\\nPRIVATE\\nKEY/BEGIN RSA PRIVATE KEY/g' | sed -z 's/END\\nRSA\\nPRIVATE\\nKEY/END RSA PRIVATE KEY/g' > client.root.key")
                        sh(script: "chmod 0600 client.root.key")
                    }
                }
            }
        }
        stage('Get Gamma Repo') {
            steps {
                dir('gamma') {
                    deleteDir()
                    checkout([
                        $class: 'GitSCM',
                        branches: [[name: "master"]],
                        extensions: [[
                            $class: 'CloneOption',
                            shallow: true,
                            depth:   1,
                            timeout: 30
                        ]],
                        userRemoteConfigs: [[
                            url:           'https://github.com/epiFi/gamma.git',
                            credentialsId: 'github-app-epifi'
                        ]]
                    ])
                }
            }
        }
        stage("Run Migration") {
            steps {
                 script {
                    withCredentials([usernamePassword(credentialsId: 'epifi-docker-deploy', passwordVariable: 'pass', usernameVariable: 'user')]) {
                            sh "docker login -u "+ user +" -p "+ '${pass}'
                    }
                }
                script {
                    sh "chmod 777 ${env.WORKSPACE}/Jenkins/scripts/db-migration.sh && ${env.WORKSPACE}/Jenkins/scripts/db-migration.sh ${ENV} ${DATABASE_NAME} ${N}"
                }
            }
        }
    }
}
