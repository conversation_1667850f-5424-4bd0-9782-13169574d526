pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '10'))
        timeout(time: 10, unit: 'MINUTES')
    }
    parameters {
        string(name: 'CLUSTER_NAME', defaultValue: 'epifi-deploy-runners', description: 'EKS cluster name')
        string(name: 'NODEGROUP_NAME', defaultValue: 'epifi-managed-dedicated-runner-ng-v4', description: 'EKS nodegroup name')
        string(name: 'POD_PREFIX', defaultValue: 'gamma-4xlarge-runner', description: 'Pod name prefix to check')
        choice(name: 'SCALE_ACTION', choices: ['SCALE_UP', 'SCALE_DOWN'], description: 'Action to perform')
    }
    
    environment {
        CLUSTER_NAME = "${params.CLUSTER_NAME}"
        NODEGROUP_NAME = "${params.NODEGROUP_NAME}"
        POD_PREFIX = "${params.POD_PREFIX}"
        MIN_NODES = '1'
        DESIRED_NODES = '1'
    }
    
    stages {
        stage('Verify Dependencies') {
            steps {
                script {
                    // Verify kubectl and aws CLI are available
                    def kubectlCheck = sh(script: "command -v kubectl || echo 'not found'", returnStdout: true).trim()
                    def awsCheck = sh(script: "command -v aws || echo 'not found'", returnStdout: true).trim()
                    
                    if (kubectlCheck == 'not found') {
                        error "kubectl is not installed or not in PATH"
                    }
                    
                    if (awsCheck == 'not found') {
                        error "aws CLI is not installed or not in PATH"
                    }
                    
                    echo "Dependencies verified successfully"
                }
            }
        }
        
        stage('Configure kubectl') {
            steps {
                script {
                    try {
                        sh "aws eks update-kubeconfig --name ${CLUSTER_NAME} --region ap-south-1"
                        echo "kubectl configured successfully for cluster ${CLUSTER_NAME}"
                    } catch (Exception e) {
                        error "Failed to configure kubectl: ${e.message}"
                    }
                }
            }
        }
        
        stage('Check and Scale') {
            steps {
                script {
                    try {
                        // Check if pods exist using label selector instead of grep
                        echo "Checking for pods with label runner-deployment-name=${POD_PREFIX}"
                        def podList = sh(script: "kubectl get pods -A -l runner-deployment-name=${POD_PREFIX} --no-headers || true", returnStdout: true).trim()
                        echo "Pod list: ${podList}"
                        
                        def podCount = 0
                        if (podList) {
                            podCount = sh(script: "echo '${podList}' | wc -l", returnStdout: true).trim().toInteger()
                        }
                        
                        echo "Found ${podCount} pods with prefix '${POD_PREFIX}'"
                        def podsExist = podCount > 0 ? 'true' : 'false'
                        echo "Pods with prefix '${POD_PREFIX}' exist: ${podsExist}"
                        
                        // Handle scaling based on SCALE_ACTION parameter
                        if (params.SCALE_ACTION == 'SCALE_UP') {
                            echo "Scaling up nodegroup ${NODEGROUP_NAME} to ${DESIRED_NODES} nodes"
                            sh "aws eks update-nodegroup-config --cluster-name ${CLUSTER_NAME} --nodegroup-name ${NODEGROUP_NAME} --scaling-config desiredSize=${DESIRED_NODES},minSize=${MIN_NODES} --region ap-south-1"
                            echo "Successfully scaled up nodegroup ${NODEGROUP_NAME}"
                        } else if (params.SCALE_ACTION == 'SCALE_DOWN' && podsExist == 'false') {
                            echo "Scaling down nodegroup ${NODEGROUP_NAME} to 0 nodes"
                            sh "aws eks update-nodegroup-config --cluster-name ${CLUSTER_NAME} --nodegroup-name ${NODEGROUP_NAME} --scaling-config desiredSize=0,minSize=0 --region ap-south-1"
                            echo "Successfully scaled down nodegroup ${NODEGROUP_NAME}"
                        } else if (params.SCALE_ACTION == 'SCALE_DOWN' && podsExist == 'true') {
                            echo "Cannot scale down: Pods with prefix '${POD_PREFIX}' are still running"
                        }
                    } catch (Exception e) {
                        error "Failed during scaling operation: ${e.message}"
                    }
                }
            }
        }
    }
    
    post {
        always {
            cleanWs()
        }
        success {
            echo "Successfully completed nodegroup scaling operation"
        }
        failure {
            echo "Failed to complete nodegroup scaling operation"
        }
    }
} 