### Running
nlu-engine is run via mlflow, a python server. 
This folder defines 
* jenkinsfile - pipeline for jenkins
* packer - packer script

#### Jenkinsfile
jenkins pipeline has 4 stages 
* build-binary
    1. `git pull {branch:-master}`
    2. runs docker build, defined in repo
    3. copies all the required artifacts, model files in output/$target/
* packer
    builds image
* deployment 
    runs terraform apply on packer built image
     