#!/bin/bash
set -e 
set -o pipefail
echo "Required input parameters are: target"
echo "target=$TARGET"
target=$TARGET

# Start - This is to get the commit id from backend repo
cd ${WORKSPACE}/$REPO_NAME/
backend_git_commit=`git rev-parse HEAD`
backend_git_branch=`git rev-parse --abbrev-ref HEAD`
cd -
# End

PACKER_LOG="${WORKSPACE}/askfi-nlu-engine/output/${TARGET}_packer.log"
export source_directory_path="${WORKSPACE}/askfi-nlu-engine/output/${TARGET}/askfi-nlu-engine/"
## needed variables for packetr to copy model files ~ 2gb
export AWS_POLL_DELAY_SECONDS=5
export AWS_MAX_ATTEMPTS=400
cd packer/nlu-engine || exit
if [ -n "$target" ]
then
    packer build -debug -machine-readable -var "source_directory_path=$source_directory_path" -var "target=$target" -var "GIT_COMMIT=$backend_git_commit" -var "GIT_BRANCH=$backend_git_branch" nlu-engine.json |tee "${PACKER_LOG}" || { echo 'packer build step failed' ; exit 1; }
    IMAGE_ID=$(cat "${PACKER_LOG}" | awk 'match($0, /ami-.*/) { print substr($0, RSTART, RLENGTH) }' | tail -n 1 | tr -d '\\n')
    echo "$IMAGE_ID"
    echo "$IMAGE_ID" >> "${WORKSPACE}/askfi-nlu-engine/output/image_id.txt"
fi
