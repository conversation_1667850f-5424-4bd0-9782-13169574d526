#!/bin/bash
set -e 
set -o pipefail
echo "Required input parameters are: target"
echo "target=$TARGET"
env
target=$TARGET
## 2-step docker build. performs
## 1. creates venev
## 2. installs requirements
## 3. in 2nd step - copies all code including model files and venv files
make docker-build-nlu || { echo 'docker-build step failed' ; exit 1; }
docker create -it --name copy_artifact_"$target" "$target":latest || { echo 'docker-create step failed' ; exit 1; }
mkdir -p ./output/"$target"/askfi-nlu-engine/nlu_engine/data
mkdir -p ./output/"$target"/askfi-nlu-engine/
## download model files
make download-inference-build basepath=./output/"$target"/askfi-nlu-engine/nlu_engine 
## copy code
docker cp copy_artifact_"$target":/home/<USER>/askfi-nlu-engine/ ./output/"$target"/
## remove docker image
docker rm -f copy_artifact_"$target"

## cleanup dangling images, stopped containers
docker system prune -f --filter "until=1h" 
