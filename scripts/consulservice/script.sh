#!/bin/bash
set -e
set -o pipefail

make docker-build || { echo 'docker-build step failed' ; exit 1; }
docker create -it --name copy_artifact_consulservice consulservice:latest || { echo 'docker-create step failed' ; exit 1; }
mkdir -p ./output/consulservice
## copy code
docker cp copy_artifact_consulservice:/home/<USER>/consulservice/ ./output/
## remove docker image
docker rm -f copy_artifact_consulservice
## cleanup dangling images, stopped containers
docker system prune -f --filter "until=1h"