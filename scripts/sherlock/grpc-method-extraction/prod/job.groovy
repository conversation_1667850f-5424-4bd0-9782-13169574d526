@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '10', artifactNumToKeepStr: '10'))
        timestamps()
    }
    environment {
        PROTO_BRANCH = "${params.PROTO_BRANCH}"
        SERVICE_DIRECTORY = "${params.SERVICE_DIRECTORY}"
        IMAGE_NAME = 'epifi/protos'
        ENV = "${params.ENV}"
    }
    
    stages {
        stage('Clone Git repository') {
            steps {
                dir('protos') {
                    deleteDir()
                    git credentialsId: 'github-app-epifi', poll: false, url: 'https://github.com/epiFi/protos.git', branch: "${PROTO_BRANCH}"
                }
            }
        }
        stage('Build Docker Image') {
            steps {
                script {
                    // Set the Docker build context to the root of the repository
                    dir('protos') {
                        sh "docker build --no-cache --build-arg SERVICE_DIRECTORY=${SERVICE_DIRECTORY} -t ${IMAGE_NAME} -f scripts/proto_json_compiler/Dockerfile ."
                    }
                }
            }
        }
        stage('Extract Files from Docker Image') {
            steps {
                script {
                    // Create a container from the built image
                    containerId = sh(script: "docker create ${IMAGE_NAME}", returnStdout: true).trim()

                    // Copy files from the container to the Jenkins workspace
                    sh "docker cp ${containerId}:/epifi/protos/grpc-schema.json ./protos/grpc-schema.json"
                    sh "docker cp ${containerId}:/epifi/protos/grpc-methods ./protos/grpc-methods"

                    // Remove the container after copying the files
                    sh "docker rm ${containerId}"
                }
            }
        }
        stage('Upload to S3') {
            steps {
                dir('protos'){
                    script {
                        sh "aws s3 cp grpc-schema.json s3://epifi-${ENV}-strapi/protos/${SERVICE_DIRECTORY}/grpc-schema.json"
                        sh "aws s3 cp grpc-methods s3://epifi-${ENV}-strapi/protos/${SERVICE_DIRECTORY}/grpc-methods --recursive"
                    }
                }
            }
        }
    }
}
