#!/bin/bash
set -e 
set -o pipefail
echo "Required input parameters are: env"
echo "env=$ENV"
env

docker build -t sherlock:latest --build-arg environment="${ENV}" . || { echo 'docker-build step failed' ; exit 1; }

docker create -it --name copy_artifact_sherlock sherlock:latest|| { echo 'docker-create step failed' ; exit 1; }

mkdir -p "$PWD"/output/

docker cp copy_artifact_sherlock:/server "$PWD"/output/
docker cp copy_artifact_sherlock:/strapi "$PWD"/output/
docker cp copy_artifact_sherlock:/build "$PWD"/output/
docker rm -f copy_artifact_sherlock

## cleanup dangling images, stopped containers
docker system prune -f --filter "until=1h"
