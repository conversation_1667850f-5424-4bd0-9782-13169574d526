#!/bin/bash
set -e 
set -o pipefail
echo "Required input parameters are: MANIFEST_FILE_NAME, TARGETAPP, AMI_NAME, BASE_AMI, \
SERVICE_TYPE, DEST_DIR_PATH, SOURCE_DIR_PATH, SYSTEMD_SERVICE_SOURCE, SYSTEMD_SERVICE_DEST, IMAGE_ID_FILE \
SOURCE_SERVER_PATH, SOURCE_PROTO_PATH, SOURCE_BUILD_PATH"

echo "MANIFEST_FILE_NAME=$MANIFEST_FILE_NAME, TARGETAPP=$TARGETAPP, AMI_NAME=$AMI_NAME, \
BASE_AMI=$BASE_AMI, SERVICE_TYPE=$SERVICE_TYPE, SERVICE_NAME=$SERVICE_NAME, DEST_DIR_PATH=$DEST_DIR_PATH, \
SOURCE_DIR_PATH=$SOURCE_DIR_PATH, SYSTEMD_SERVICE_SOURCE=$SYSTEMD_SERVICE_SOURCE, SOURCE_SERVER_PATH=$SOURCE_SERVER_PATH, \
SOURCE_PROTO_PATH=$SOURCE_PROTO_PATH, SOURCE_BUILD_PATH=$SOURCE_BUILD_PATH, SYSTEMD_SERVICE_DEST=$SYSTEMD_SERVICE_DEST, \
IMAGE_ID_FILE=$IMAGE_ID_FILE"

target=$TARGET
PACKER_DIR="${WORKSPACE}/infra/packer"
PACKER_MANIFEST="${PACKER_DIR}/${MANIFEST_FILE_NAME}.json"

# Start - This is to get the commit id from backend repo
cd ${WORKSPACE}/sherlock/
backend_git_commit=`git rev-parse HEAD`
backend_git_branch=`git rev-parse --abbrev-ref HEAD`
cd -
# End

select_golden_ami_type() {
    # by default use 'release' (or stable) type
    GOLDEN_AMI_TYPE='release'
    # check branch name for release branch name pattern: eg. epifi/m153-rc1-May-14
    RELEASE_BRANCH_PATTERN='^epifi\/m([0-9]{1,})-rc([0-9]{1,})-([a-zA-Z]{3})-([0-9]{1,})$'
    if [[ "${APP_BRANCH}" =~ ${RELEASE_BRANCH_PATTERN} ]]; then
        GOLDEN_AMI_TYPE='release'
    else
        # allow use of 'pre-release' type ami for non-release type branches
        GOLDEN_AMI_TYPE='pre-release'
    fi
    echo "[INFO] selected golden AMI type: ${GOLDEN_AMI_TYPE}"
}

cd ${PACKER_DIR} || exit
if [ -n "$target" ]
then
    select_golden_ami_type

    packer build -timestamp-ui \
      -var "MANIFEST_FILE_NAME=$MANIFEST_FILE_NAME"\
      -var "TARGETAPP=$TARGETAPP" \
      -var "AMI_NAME=$AMI_NAME" \
      -var "SOURCE_DIR_PATH=$SOURCE_DIR_PATH" \
      -var "GIT_COMMIT=$backend_git_commit" \
      -var "GIT_BRANCH=$backend_git_branch" \
      -var "BASE_AMI=$BASE_AMI" \
      -var "SERVICE_TYPE=$SERVICE_TYPE" \
      -var "DEST_DIR_PATH=$DEST_DIR_PATH" \
      -var "SERVICE_NAME=$SERVICE_NAME" \
      -var "SYSTEMD_SERVICE_SOURCE=$SYSTEMD_SERVICE_SOURCE" \
      -var "SYSTEMD_SERVICE_DEST=$SYSTEMD_SERVICE_DEST" \
      -var "SOURCE_BUILD_PATH=$SOURCE_BUILD_PATH" \
      -var "SOURCE_SERVER_PATH=$SOURCE_SERVER_PATH" \
      -var "SOURCE_PROTO_PATH=$SOURCE_PROTO_PATH" \
      -var golden_ami_type=${GOLDEN_AMI_TYPE} \
      nodejs-18-packer-config.json || { echo '[ERROR] packer build step failed'; exit 1; }

    # use packer manifest to get ami id
    jq '.last_run_uuid as $id
      | .builds[]
      | select(.packer_run_uuid==$id)
      | .artifact_id' ${PACKER_MANIFEST} | \
      grep -oE "ami-(\w+)" | \
      tee "${IMAGE_ID_FILE}"
fi
