package main

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	queuePb "github.com/epifi/be-common/api/queue"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"

	actorPb "github.com/epifi/gamma/api/actor"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"

	emailParserPb "github.com/epifi/gamma/api/insights/emailparser"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/scripts/gmail_spends_actorid_backfill/config"
	"go.uber.org/zap"
)

var (
	userClient        userPb.UsersClient
	actorClient       actorPb.ActorClient
	emailParserClient emailParserPb.ConsumerServiceClient
)

var phoneNumbers = []int{}

func main() {
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() { _ = logger.Log.Sync() }()

	_, err = config.Load()
	if err != nil {
		logger.Fatal("failed to load config", zap.Error(err))
	}

	insightsConn := epifigrpc.NewConn(getInsightsEndpoint(env))
	defer epifigrpc.CloseConn(insightsConn)
	emailParserClient = emailParserPb.NewConsumerServiceClient(insightsConn)

	actorConn := epifigrpc.NewConn(getActorEndPoint(env))
	defer epifigrpc.CloseConn(actorConn)
	actorClient = actorPb.NewActorClient(actorConn)

	userConn := epifigrpc.NewConn(getUserEndPoint(env))
	defer epifigrpc.CloseConn(userConn)
	userClient = userPb.NewUsersClient(userConn)

	linkWaitListOnboardActorId()
}

func linkWaitListOnboardActorId() {
	var errorUsersResp = map[int]string{}
	var errorActorResp = map[int]string{}
	var errorEpResp = map[int]string{}
	var passUsers = map[int]bool{}
	for idx := range phoneNumbers {
		currPhone := phoneNumbers[idx]
		logger.InfoNoCtx("running for ", zap.Int("phone-number", currPhone))
		userRes, err := userClient.GetUser(context.Background(), &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{
				CountryCode:    91,
				NationalNumber: uint64(currPhone),
			}},
		})
		if te := epifigrpc.RPCError(userRes, err); te != nil {
			if userRes != nil {
				logger.InfoNoCtx("user resp", zap.Any("resp", userRes.GetStatus().GetShortMessage()))
				errorUsersResp[currPhone] = userRes.GetStatus().GetShortMessage()
			} else {
				errorUsersResp[currPhone] = "error"
			}
			continue
		}
		actorResp, err := actorClient.GetActorByEntityId(context.Background(), &actorPb.GetActorByEntityIdRequest{
			Type:     types.Actor_USER,
			EntityId: userRes.GetUser().GetId(),
		})
		if te := epifigrpc.RPCError(actorResp, err); te != nil {
			if actorResp != nil {
				logger.InfoNoCtx("actor resp", zap.Any("resp", actorResp.GetStatus().GetShortMessage()))
				errorActorResp[currPhone] = actorResp.GetStatus().GetShortMessage()
			} else {
				errorActorResp[currPhone] = "error"
			}
			continue
		}
		// step -2
		// this is step 2 of the retrigger indexing part, first step will run as part of jenkins job for now
		// send an event for onboarding for a user
		processOnboardingResp, err := emailParserClient.ProcessOnboardingStageEvent(context.Background(), &onboardingPb.OnboardingStageUpdate{
			ActorId: actorResp.GetActor().GetId(),
			Stage:   onboardingPb.OnboardingStage_ONBOARDING_COMPLETE,
			State:   onboardingPb.OnboardingStageUpdate_SUCCESS,
		})
		switch {
		case err != nil:
			logger.ErrorNoCtx("error in processing onboarding stage", zap.Error(err))
			errorEpResp[currPhone] = "error in processing onboarding stage"
		case processOnboardingResp.GetResponseHeader().GetStatus() == queuePb.MessageConsumptionStatus_PERMANENT_FAILURE:
			logger.ErrorNoCtx("consumer sent permanent failure")
			errorEpResp[currPhone] = "consumer sent permanent failure"
		case processOnboardingResp.GetResponseHeader().GetStatus() == queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE:
			logger.ErrorNoCtx("consumer sent transient failure")
			errorEpResp[currPhone] = "consumer sent transient failure"
		default:
			passUsers[currPhone] = true
			logger.InfoNoCtx("process user onboarding data ok response", zap.String("response", processOnboardingResp.String()))
		}
	}
	logger.InfoNoCtx("error data", zap.Any("user-resp-error", errorUsersResp), zap.Any("actor-resp-error", errorActorResp), zap.Any("emailparser-resp-error", errorEpResp))
	logger.InfoNoCtx("pass users", zap.Any("pass-users", passUsers))
}

func getInsightsEndpoint(env string) string {
	switch env {
	case cfg.DemoEnv:
		return "demo-insights-nlb-1a98a404b9148d1b.elb.ap-south-1.amazonaws.com:8096"
	case cfg.StagingEnv:
		return "staging-insights-nlb-1cb7e58fe9ae70db.elb.ap-south-1.amazonaws.com:8096"
	case cfg.ProductionEnv:
		return "prod-insights-nlb-f51e53bdeb2472ed.elb.ap-south-1.amazonaws.com:8096"
	default:
		return "localhost:8096"
	}
}

func getActorEndPoint(env string) string {
	switch env {
	case cfg.DemoEnv:
		return "demo-actor-nlb-1801ba9a29a85adc.elb.ap-south-1.amazonaws.com:8092"
	case cfg.StagingEnv:
		return "staging-actor-nlb-9a4bfd6f3a2554de.elb.ap-south-1.amazonaws.com:8092"
	case cfg.ProductionEnv:
		return "prod-actor-nlb-c8f1241e10425618.elb.ap-south-1.amazonaws.com:8092"
	default:
		return "localhost:8092"
	}
}

func getUserEndPoint(env string) string {
	switch env {
	case cfg.DemoEnv:
		return "demo-user-nlb-d86dd25aef0aca25.elb.ap-south-1.amazonaws.com:8083"
	case cfg.StagingEnv:
		return "staging-user-nlb-bac0fa010e3bdbcf.elb.ap-south-1.amazonaws.com:8083"
	case cfg.ProductionEnv:
		return "prod-user-nlb-b8f758c43fe0ab5a.elb.ap-south-1.amazonaws.com:8083"
	default:
		return "localhost:8083"
	}
}
