#!/bin/bash
set -e
set -o pipefail
set -x
GAMMA_PATH=$1
cd  $GAMMA_PATH

mkdir -p ./config/server/
cp ./pkg/cfg/config/*  config/
cp -r ./cmd/servers/config/*.yml ./config/server/ 2> /dev/null || :
cp -r ./cmd/servers/$ENV/*/config/*.yml ./config/server/ 2> /dev/null || :

if [ $ENV == "uat" ]; then
    ROLE_ARN=${UAT_ACCOUNT_ROLE}
elif [ $ENV == "demo" ]; then
    ROLE_ARN=${DEMO_ACCOUNT_ROLE}
elif [ $ENV == "staging" ]; then
    ROLE_ARN=${STAGING_ACCOUNT_ROLE}
elif [ $ENV == "qa" ]; then
    ROLE_ARN=${QA_ACCOUNT_ROLE}
fi

set +x
aws_credentials_json=$(aws sts assume-role --role-arn "${ROLE_ARN}" --role-session-name uatSession --region ap-south-1)
export AWS_ACCESS_KEY_ID=$(echo "$aws_credentials_json" | jq --exit-status --raw-output .Credentials.AccessKeyId)
export AWS_SECRET_ACCESS_KEY=$(echo "$aws_credentials_json" | jq --exit-status --raw-output .Credentials.SecretAccessKey)
export AWS_SESSION_TOKEN=$(echo "$aws_credentials_json" | jq --exit-status --raw-output .Credentials.SessionToken)
set -x

aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ************.dkr.ecr.ap-south-1.amazonaws.com
ENVIRONMENT=$ENV TEST_TENANT=$SUFFIX go run -v ./testing/test_tenant/cmd/setup_db/setup_db.go
