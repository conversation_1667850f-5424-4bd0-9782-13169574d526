#!/bin/bash
set -e
set -o pipefail

set -x
aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ************.dkr.ecr.ap-south-1.amazonaws.com
docker pull ************.dkr.ecr.ap-south-1.amazonaws.com/integration-test-tenant:latest

if [ $ENV == "uat" ]; then
    ROLE_ARN=${UAT_ACCOUNT_ROLE}
elif [ $ENV == "demo" ]; then
    ROLE_ARN=${DEMO_ACCOUNT_ROLE}
elif [ $ENV == "staging" ]; then
    ROLE_ARN=${STAGING_ACCOUNT_ROLE}
elif [ $ENV == "qa" ]; then
    ROLE_ARN=${QA_ACCOUNT_ROLE}
fi

#set +x
#aws_credentials_json=$(aws sts assume-role --role-arn "${ROLE_ARN}" --role-session-name uatSession --region ap-south-1)
#export AWS_ACCESS_KEY_ID=$(echo "$aws_credentials_json" | jq --exit-status --raw-output .Credentials.AccessKeyId)
#export AWS_SECRET_ACCESS_KEY=$(echo "$aws_credentials_json" | jq --exit-status --raw-output .Credentials.SecretAccessKey)
#export AWS_SESSION_TOKEN=$(echo "$aws_credentials_json" | jq --exit-status --raw-output .Credentials.SessionToken)

GOMODCACHE="/mnt/efs/data1/go/.cache/go-mod"
GOCACHE="/mnt/efs/data1/go/.cache/go-build"

GAMMA_PATH=$1
cd  $GAMMA_PATH

docker run \
  -v $GAMMA_PATH:/go/src/github.com/epifi/gamma_mount \
  -v $GOCACHE:/root/.cache/go-build \
  -v $GOMODCACHE:/go/pkg/mod \
  -e TEST_PATTERN=$PATTERN \
  -e BU_ARGS=$BU_ARGS \
  -e SCOPE_ARGS=$SCOPE_ARGS \
  -e ENVIRONMENT=$ENV \
  -e TEST_TENANT=$SUFFIX \
  -e CONFIG_DIR=/go/src/github.com/epifi/gamma/testing/integration/output/config \
  -e USE_PREONBOARDED_DATA=$USE_PREONBOARDED_DATA \
  -e ROLE_ARN=$ROLE_ARN \
  -e BRANCH=$BRANCH \
  -e BUILD_NUMBER=$BUILD_NUMBER \
  -e RUN_QUEST_SMOKE_TEST=$RUN_QUEST_SMOKE_TEST \
  -e SLACK_ALERT=$SLACK_ALERT \
  -e IS_SERVER_HEALTH_CHECK=$IS_SERVER_HEALTH_CHECK \
  -e INCLUDE_SERVERS=$INCLUDE_SERVERS \
  -e EXCLUDE_SERVERS=$EXCLUDE_SERVERS \
  ************.dkr.ecr.ap-south-1.amazonaws.com/integration-test-tenant:latest
