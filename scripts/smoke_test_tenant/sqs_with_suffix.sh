#!/bin/bash
set -e
set -o pipefail
set -x
INFRA_PATH=$1
mode=$2
cd  $INFRA_PATH/terraform/sns_sqs_v2
terraform init -backend-config=s3-backend-$ENV.conf -backend-config="key=sns-sqs/v2/$ENV/suffix/-$SUFFIX/terraform.tfstate"
terraform plan --var-file ./../env/$ENV.tf -var="suffix=-$SUFFIX"
if [[ "$mode" == "create" ]]; then
  terraform apply -input=false -auto-approve --var-file ./../env/$ENV.tf -var="suffix=-$SUFFIX"
elif [[ "$mode" == "destroy" ]]; then
  terraform destroy -input=false -auto-approve --var-file ./../env/$ENV.tf -var="suffix=-$SUFFIX"
fi