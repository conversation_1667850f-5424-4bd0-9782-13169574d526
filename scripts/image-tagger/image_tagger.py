import sys
import os

arguments = sys.argv
"""
python script.py ami-id test/all/nodejs
"""

if len(arguments) != 3:
    print("Please provide all required command line argument")
    print("Required-Arguments:\n 1. AWS-AMI-ID \n 2. test | golang | nodejs | python_v1 | python_v2")
    exit()

go_lang_img_tags = [
    "actor",
    "atlas",
    "auth",
    "auror",
    "card",
    "cx",
    "docs",
    "frontend",
    "goblin",
    "lending",
    "nebula",
    "order",
    "paymentinstrument",
    "savings",
    "simulator",
    "timeline",
    "tokenizer",
    "test",
    "universal",
    "userrisk",
    "vendorgateway",
    "vendorgateway-pci",
    "vendormapping",
    "vendornotification",
    "vnotificationgw",
    "central-growth",
    "growth-infra",
    "onboarding",
    "pay",
    "sgapigateway",
    "sgexternalgateway",
    "sgvendorgateway",
    "wealthdmf",
]

nodejs_img_tag_keys = ["web", "sherlock"]

python_v1_img_tags = ["emailparser"]

python_v2_img_tags = ["entity-matcher"]

go_lang_img_tag_key_values = {"team": "devops",
                              "type": "epifi-golden-hardened-image"}

tags = "--tags "

if arguments[2] == "test":
    tags = tags + "Key=test,Value=approved"
    print("Test tag applied successfully")
elif arguments[2] == "golang":
    for key in go_lang_img_tags:
        tags = tags + "Key={},Value=approved ".format(key)
        print("    Tag Key: {} and value: approved".format(key))
    for key, value in go_lang_img_tag_key_values.items():
        tags = tags + "Key={},Value={} ".format(key, value)
        print("    Tag Key: {} and value: {}".format(key, value))
elif arguments[2] == "nodejs":
    for key in nodejs_img_tag_keys:
        tags = tags + "Key={},Value=approved ".format(key)
elif arguments[2] == "python_v1":
    for key in python_v1_img_tags:
        tags = tags + "Key={},Value=approved ".format(key)
elif arguments[2] == "python_v2":
    for key in python_v2_img_tags:
        tags = tags + "Key={},Value=approved ".format(key)
else:
    print("Not Supported Arguments")
    exit()

os.system("aws ec2 create-tags --resources {} {}".format(arguments[1], tags))
