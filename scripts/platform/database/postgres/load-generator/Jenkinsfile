@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    options {
        buildDiscarder(logRotator(numToKeepStr: '25'))
    }
    parameters {
        choice(name: 'ENV', choices: ['demo', 'qa', 'staging', 'uat'], description: 'Environment in which to execute the script')
        choice(name: 'STEP', choices: ['init_tables_and_data', 'generate_load', 'go_load_generator'])
        string(name: 'DB_HOST', defaultValue: 'staging-postgres-14.cet3be1aev95.ap-south-1.rds.amazonaws.com')
        string(name: 'DB_USER', defaultValue: 'staging_admin')
        string(name: 'LOAD_DURATION', defaultValue: '300', description: 'time (in seconds) till which load should be generated')
        booleanParam(name: 'REFRESH', defaultValue: false, description: 'Devs, ignore this variable! This is for DevOps to refresh Jenkinsfile.')
    }
    environment {
        ENV = "${params.ENV}"
        LOAD_DURATION = "${params.LOAD_DURATION}"
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true }
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-Refresh"
                    currentBuild.description = "#${BUILD_NUMBER}-Refresh"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Assume Role') {
            steps {
                script {
                    assumeRole()
                }
            }
        }
        stage('Fetch dependencies') {
            steps {
                script {
                    env.DB_PASS = sh(script: "set +x && aws secretsmanager get-secret-value --region ap-south-1 --secret-id \"${env.ENV}/rds/postgres\" | jq -r '.SecretString | fromjson | .password' && set -x", returnStdout: true).trim()
                }
                script {
                    sh """
                    set +x
                    curl https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add -
                    sudo sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt \$(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list'
                    sudo apt update -y
                    sudo apt-get install postgresql-client-14 postgresql-contrib  -y
                    set -x
                    """
                }
            }
        }
        stage('Get Gamma Repo') {
            steps {
                script {
                    gitCheckout(repoName: "gamma", gitBranch: "master", githubOrg: "epifi", sparseCheckoutPath: "scripts/pgdb/load_generator")
                }
            }
        }
        stage('Execute script') {
            steps {
                script {
                    dir('gamma/scripts/pgdb/load_generator') {
                        sh "ENVIRONMENT=${env.ENV} ./load.sh ${params.STEP}"
                    }
                }
            }
        }
    }
}
