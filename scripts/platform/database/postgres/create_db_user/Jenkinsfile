pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    options {
        buildDiscarder(logRotator(numToKeepStr: '25'))
    }
    parameters {
        choice(name: 'ENV', choices: ['demo', 'qa', 'staging', 'uat', 'deploy'], description: 'Environment in which to execute the script')
        choice(name: 'DB_CLUSTER', choices: ['epifimetis', 'epifiminerva', 'epifiplutus', 'deploy-jarvis'], description: 'Cluster in which to create the DB. Select postgres for demo/staging/uat')
        string(name: 'DB_NAME', defaultValue: '', description: 'Name of the database which is to be created')
        string(name: 'DB_OWNER_SECRET_KEY', defaultValue: '', description: 'Required if DB already exists. AWS SecretManager key for the DB owner user')
        booleanParam(name: 'REFRESH', defaultValue: false, description: 'Devs, ignore this variable! This is for <PERSON>Ops to refresh Jenkins<PERSON>le.')
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true}
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-${ENV}-REFRESH:${params.REFRESH}"
                    currentBuild.description = "#${BUILD_NUMBER}-${ENV}-REFRESH:${params.REFRESH}"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Get Infra Repo') {
            steps {
                dir('infra') {
                    deleteDir()
                    checkout([
                        $class: 'GitSCM',
                        branches: [[name: "master"]],
                        extensions: [[
                            $class: 'CloneOption',
                            shallow: true,
                            depth:   1,
                            timeout: 30
                        ]],
                        userRemoteConfigs: [[
                            url:           'https://github.com/epiFi/infra.git',
                            credentialsId: 'github-app-epifi'
                        ]]
                    ])
                }

            }
        }
        stage('Verify db name') {
            steps {
                script {
                    if (params.DB_NAME == '') {
                        currentBuild.result = "ABORTED"
                        throw new Exception("No db name provided for which to grant access to user.")
                    } else {
                        currentBuild.displayName = "#${BUILD_NUMBER}-${ENV}-${DB_CLUSTER}-${DB_NAME}"
                    }
                }
            }
        }
        stage('Run Create DB User script') {
            steps {
                script {
                    dir('infra/jenkins/scripts') {
                        sh "./new_pgdb_client_secrets.sh ${params.ENV} ${params.DB_CLUSTER} ${params.DB_NAME} ${params.DB_OWNER_SECRET_KEY}"
                    }
                }
            }
        }
    }
    post {
        always {
            dir('infra') {
                deleteDir()
            }
        }
    }
}
