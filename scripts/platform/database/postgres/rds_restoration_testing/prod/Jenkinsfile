pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    options {
        buildDiscarder(logRotator(numToKeepStr: '25'))
    }
    parameters {
        choice(name: 'ENV', choices: ['prod'], description: 'Environment in which to execute the script')
        choice(name: 'DB_CLUSTER', choices: ['epifimetis', 'epifiminerva', 'temporalprod', 'prod-plutus', 'prod-jarvis'], description: 'Cluster in which to create the DB.')
        string(name: 'SNAPSHOT_NAME', defaultValue: '', description: 'Name of the snapshot to restore rds instance from. Will create a new snapshot if this field is empty')
        booleanParam(name: 'REFRESH', defaultValue: false, description: '<PERSON><PERSON>, ignore this variable! This is for DevOps to refresh Jenkinsfile.')
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true}
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-${ENV}-REFRESH:${params.REFRESH}"
                    currentBuild.description = "#${BUILD_NUMBER}-${ENV}-REFRESH:${params.REFRESH}"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Get Infra Repo') {
            steps {
                dir('infra') {
                    deleteDir()
                    checkout([
                        $class: 'GitSCM',
                        branches: [[name: "yash/rds-restoration"]],
                        extensions: [[
                            $class: 'CloneOption',
                            shallow: true,
                            depth:   1,
                            timeout: 30
                        ]],
                        userRemoteConfigs: [[
                            url:           'https://github.com/epiFi/infra.git',
                            credentialsId: 'github-app-epifi'
                        ]]
                    ])
                }
            }
        }
        stage('Run RDS Restoration Testing script') {
            steps {
                script {
                    dir('infra/jenkins/scripts') {
                        sh "set -x && chmod 755 ./rds_restoration_testing.sh && ./rds_restoration_testing.sh ${params.ENV} ${params.DB_CLUSTER} ${params.SNAPSHOT_NAME}"
                    }
                }
            }
        }
    }
    post {
        always {
            dir('infra') {
                deleteDir()
            }
        }
    }
}
