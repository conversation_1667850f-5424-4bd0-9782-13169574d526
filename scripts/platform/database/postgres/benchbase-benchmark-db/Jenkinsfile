@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        buildDiscarder(logRotator(numToKeepStr: '25'))
    }
    parameters {
        choice(name: 'ENV', choices: ['demo', 'qa', 'staging', 'uat'], description: 'Environment in which to execute the script')
        choice(name: 'STEP', choices: ['setup_pgdb', 'setup_crdb', 'benchmark_pgdb', 'benchmark_crdb'])
        booleanParam(name: 'REFRESH', defaultValue: false, description: '<PERSON><PERSON>, ignore this variable! This is for DevOps to refresh Jenkinsfile.')
    }
    environment {
        ENV = "${params.ENV}"
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true }
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-Refresh"
                    currentBuild.description = "#${BUILD_NUMBER}-Refresh"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Assume Role') {
            steps {
                script {
                    assumeRole()
                }
            }
        }
        stage('Get Gamma Repo') {
            steps {
                script {
                    gitCheckout(repoName: "gamma", gitBranch: "bhumij/benchbase", githubOrg: "epifi", sparseCheckoutPath: "scripts/basebench")
                }
            }
        }
        stage('Execute script') {
            steps {
                script {
                    dir('gamma/scripts/basebench') {
                        sh "./trigger.sh ${params.STEP}"
                    }
                }
            }
        }
    }
}
