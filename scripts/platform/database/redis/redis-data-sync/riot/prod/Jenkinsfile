@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
        buildDiscarder(logRotator(numToKeepStr: '10', artifactNumToKeepStr: '10'))
    }
    parameters {
        string(name: 'SOURCE_HOST', defaultValue: "", description: 'Source redis host URL')
        string(name: 'SOURCE_PORT', defaultValue: "", description: 'Data port of the source redis')
        string(name: 'DB', defaultValue: "0", description: 'Source redis DB from which to migrate data')
        string(name: 'TARGET_HOST', defaultValue: "", description: 'Target redis host URL')
        string(name: 'TARGET_PORT', defaultValue: "", description: 'Data port of the target redis')
        string(name: 'REDIS_DB_NAME', defaultValue: "", description: 'Redis DB name in redis cloud for which migration is being done')
        choice(name: 'REPLICATION_MODE', choices: ['LIVE', 'SNAPSHOT', 'LIVEONLY', 'COMPARE'], description: 'Mode of replication to use')
        string(name: 'OPTIONAL_FLAGS', defaultValue: "", description: 'Optional flags for replicate command to tune the replication performance')
        booleanParam(name: 'ENABLE_TYPE_BASED_REPLICATION', defaultValue: false, description: 'Enable replication based on data types. Needed when dump and restore base replication cannot be used')
        booleanParam(name: 'REFRESH', defaultValue: false, description: 'Devs, ignore this variable! This is for DevOps to refresh Jenkinsfile.')
    }
    environment {
        ENV = "prod"
    }
    stages {
        stage('Refresh') {
            steps {
                script {
                    if (params.REFRESH == true) {
                        echo 'Jenkins file was loaded....... finish build now'
                        currentBuild.displayName = "#${BUILD_NUMBER}-Refresh"
                        currentBuild.description = "#${BUILD_NUMBER}-Refresh"
                        currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                        sleep(5)
                    }
                }
            }
        }
        stage('Download RIOT binary') {
            steps {
                dir('redis_migration') {
                    deleteDir()
                    sh "wget https://github.com/redis-developer/riot/releases/download/v3.2.2/riot-standalone-3.2.2-linux-x86_64.zip"
                    sh "unzip -o riot-standalone-3.2.2-linux-x86_64.zip"
                }
            }
        }
        stage('Fetch redis cloud secrets') {
            steps {
                dir('redis_migration/riot-standalone-3.2.2-linux-x86_64/bin') {
                    script {
                        env.REDIS_SECRETS = sh(script: "set +x && echo \$(aws secretsmanager get-secret-value --region ap-south-1 --secret-id \"prod/redis/${params.REDIS_DB_NAME}/prefixaccess\" | jq -r '.SecretString') && set -x", returnStdout: true).trim()
                    }
                }
            }
        }
        stage("Check connectivity to redis clusters") {
            steps {
                dir('redis_migration/riot-standalone-3.2.2-linux-x86_64/bin') {
                    script {
                        sh """
                        #!/bin/bash
                        set +x
                        ./riot -h ${params.SOURCE_HOST} -p ${params.SOURCE_PORT} --db ${params.DB} --tls ping
                        ./riot -h ${params.TARGET_HOST} -p ${params.TARGET_PORT} --user \$(echo '${env.REDIS_SECRETS}' | jq -r '.Username') --pass \$(echo '${env.REDIS_SECRETS}' | jq -r '.Password') --tls ping
                        """
                    }
                }
            }
        }
        stage("Start Data Replication from source to target redis") {
            steps {
                dir('redis_migration/riot-standalone-3.2.2-linux-x86_64/bin') {
                    script {
                        if (params.ENABLE_TYPE_BASED_REPLICATION) {
                            sh "set +x && ./riot -h ${params.SOURCE_HOST} -p ${params.SOURCE_PORT} --db ${params.DB} --tls replicate ${params.OPTIONAL_FLAGS} --type --host ${params.TARGET_HOST} --port ${params.TARGET_PORT} --user \$(echo '${env.REDIS_SECRETS}' | jq -r '.Username') --pass \$(echo '${env.REDIS_SECRETS}' | jq -r '.Password') --tls --mode ${params.REPLICATION_MODE}"
                        } else {
                            sh "set +x && ./riot -h ${params.SOURCE_HOST} -p ${params.SOURCE_PORT} --db ${params.DB} --tls replicate ${params.OPTIONAL_FLAGS} --host ${params.TARGET_HOST} --port ${params.TARGET_PORT} --user \$(echo '${env.REDIS_SECRETS}' | jq -r '.Username') --pass \$(echo '${env.REDIS_SECRETS}' | jq -r '.Password') --tls --mode ${params.REPLICATION_MODE}"
                        }
                    }
                }
            }
        }
    }
    post {
        always {
            cleanWs(
                notFailBuild: true
            )
        }
    }
}
