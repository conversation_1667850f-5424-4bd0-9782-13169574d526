@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    options {
        buildDiscarder(logRotator(numToKeepStr: '15'))
    }
    parameters {
        string(name: 'VERSION', defaultValue: 'stable', description: 'Binary Version to Execute')
        choice(name: 'ENV', choices: ['demo', 'qa', 'staging', 'uat'], description: 'Environment in which to execute the script')
        string(name: 'SERVICE', defaultValue: '', description: 'Name of the service for which to run the Redis commands')
        choice(name: 'MODE', choices: ['read', 'modify'], description: 'The level of permissions in which to execute the script')
        string(name: 'FILENAME', defaultValue: '', description: 'Name of the file from which to execute the redis commands')
        booleanParam(name: 'DECODE_PROTO', defaultValue: false, description: 'Are the values stored in redis, encoded proto messages, that need to be decoded')
        string(name: 'PROTO_TYPE', defaultValue: '', description: 'Set this value to the type of proto that you want to decode, when using DECODE_PROTO option')
    }
    environment {
        ENV = "${params.ENV}"
        CONFIG_DIR = "${env.WORKSPACE}/gamma/scripts/redis_cmd_executor/config"
        GOOS = 'linux'
        GOARCH = 'amd64'
        CGO_ENABLED = 0
    }
    stages {
        stage('Verify passed params') {
            steps {
                script {
                    if (params.SERVICE == '' || params.FILENAME == '') {
                        currentBuild.result = "ABORTED"
                        throw new Exception("No service name provided for which dynamic config is to be updated")
                    }
                    if (params.DECODE_PROTO == true && params.PROTO_TYPE == '') {
                        currentBuild.result = "ABORTED"
                        throw new Exception("No proto type is provided for decoding the proto format")
                    }
                }
            }
        }
        stage('Get Gamma Repo') {
            steps {
                gitCheckout(repoName: "gamma", gitBranch: "master", githubOrg: "epifi", sparseCheckoutPath: "scripts/redis_cmd_executor")
            }
        }
        stage('Fetch binary and copy configs') {
            steps {
                dir('gamma') {
                    script {
                        dir('certs') {
                            assumeRole()
                        }
                    }
                    script {
                        echo "Fetching pre-built binary"
                        sh """
                        set +x
                            . ${WORKSPACE}/.env
                        set -x
                        chmod 777 ${env.WORKSPACE}/scripts/platform/gamma/helper.sh
                        ${env.WORKSPACE}/scripts/platform/gamma/helper.sh fetch_binary epifi-jenkins-job-binaries redis_cmd_executor ${params.VERSION}
                        """
                    }
                }
            }
        }
        stage('Execute script') {
            steps {
                dir('gamma') {
                    script {
                        if (params.DECODE_PROTO == true) {
                            sh "ENVIRONMENT=${env.ENV} redis_cmd_executor/redis_cmd_executor_bin -rootdir=${env.WORKSPACE}/gamma/scripts/redis_cmd_executor -service=${params.SERVICE} -execmode=${params.MODE} -filename=${params.FILENAME} -decode-proto -proto-type=${params.PROTO_TYPE}"
                        } else {
                            sh "ENVIRONMENT=${env.ENV} redis_cmd_executor/redis_cmd_executor_bin -rootdir=${env.WORKSPACE}/gamma/scripts/redis_cmd_executor -service=${params.SERVICE} -execmode=${params.MODE} -filename=${params.FILENAME}"
                        }
                    }
                }
            }
        }
    }
}
