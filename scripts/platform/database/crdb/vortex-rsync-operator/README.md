# Vortex Remote Sync

`VortexRemoteSync` is a workflow that takes care of syncing the data from a given PGDB compatible database to a choice 
sink. Currently, only kinesis and PGDB are supported as a choice of sync.

This pipeline helps to interact with vortex remote sync workflow. This can include but not limited to:
* Starting workflow
* Sending various signals to intimate workflow about a manual step
* Gracefully stopping workflow.