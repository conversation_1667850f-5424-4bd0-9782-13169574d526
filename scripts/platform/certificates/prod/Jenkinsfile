@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    options {
        buildDiscarder(logRotator(numToKeepStr: '10'))
    }
    parameters {
        string(name: 'INFRA_BRANCH', defaultValue: 'master', description: 'Custom Infra Branch to update s3 from')
        choice(name: 'ENV', choices: ['prod'], description: 'Environment in which to execute the script')
        string(name: 'CN', description: 'Name of the CN which is to be created (eg: test.epifi.in)')
        string(name: 'KEY_PAIR_NAME', defaultValue: '', description: 'Name of the key pair which is to be created (eg: test-app)')
        booleanParam(name: 'REFRESH', defaultValue: false, description: '<PERSON><PERSON>, ignore this variable! This is for DevOps to refresh Jenkins<PERSON>le.')
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true}
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-${params.ENV}-REFRESH:${params.REFRESH}"
                    currentBuild.description = "#${BUILD_NUMBER}-${params.ENV}-REFRESH:${params.REFRESH}"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Get Infra Repo') {
            steps {
                   gitCheckout(repoName: "infra", gitBranch: "${INFRA_BRANCH}", githubOrg: "epifi")
            }
        }
        stage('Verify CN & KEY_PAIR_NAME') {
            steps {
                script {
                    if (params.CN == '' || params.KEY_PAIR_NAME == '') {
                        currentBuild.result = "ABORTED"
                        throw new Exception("No CN/KeyPairName provided")
                    } else {
                        currentBuild.displayName = "#${BUILD_NUMBER}-${params.ENV}-${params.CN}-${params.KEY_PAIR_NAME}"
                    }
                }
            }
        }
        stage('Run self signed certificate script') {
            steps {
                script {
                    dir('infra/jenkins/scripts') {
                        sh "chmod 777 ${env.WORKSPACE}/infra/jenkins/scripts/generate_keypair.sh"
                        sh "./generate_keypair.sh ${params.ENV} ${params.CN} ${params.KEY_PAIR_NAME}"
                    }
                }
            }
        }
    }
}
