@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
    }
    parameters {
        string(name: 'Version',defaultValue: '0.0.1', description: 'Binary Version to Execute')
        string(name: 'USERNAMES', defaultValue: '', description: 'comma seperated usernames(emails) to grant permissions')
        extendedChoice(
            defaultValue: '',
            description: 'Select roles to add/remove for the given users',
            multiSelectDelimiter: ',',
            name: 'ROLES',
            value: 'quest-view,quest-edit,quest-approve',
            quoteValue: false,
            saveJSONParameterToFile: false,
            type: 'PT_CHECKBOX',
            visibleItemCount: 10)
        string(name: 'CLIENT_ID', defaultValue: 'jarvis-prod', description: 'client id [not uuid] within the configured realm to grant access')
        booleanParam(name: 'REMOVE', defaultValue: false, description: 'set remove to true to remove given roles from the given users')
    }
    environment {
        ENV = "prod"
    }
    stages {
     stage('Fetch Binary') {
              steps {
                  dir('app'){
                      script{
                          sh "aws s3 cp s3://epifi-gamma-binaries-prod/keycloak/manage_role/linux/amd64/${version} ./manage_role.zip"
                          sh "unzip -u manage_role.zip"
                      }
                 }
            }
      }
     stage("Execute Binary") {
           steps {
                dir('app') {
                     script {
                          sh """
                          cd keycloak/manage_role
                          chmod 700 keycloak/manage_role_bin
                          ENVIRONMENT=${ENV} ./keycloak/manage_role_bin -usernames=${params.USERNAMES} -remove=${params.REMOVE} -roles=${params.ROLES} -clientid=${params.CLIENT_ID}
                          """
                     }
                }
           }
     }
    }
}

