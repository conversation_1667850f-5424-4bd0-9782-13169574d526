pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    options {
        buildDiscarder(logRotator(numToKeepStr: '25'))
    }
    parameters {
        string(name: 'USERNAMES', defaultValue: '', description: 'comma seperated usernames(emails) to grant permissions')
        string(name: 'R<PERSON><PERSON>', defaultValue: '', description: 'comma seperated roles to add/remove for the given users')
        string(name: 'CLIENT_ID', defaultValue: 'jarvis-deploy', description: 'client id [not uuid] within the configured realm to grant access')
        booleanParam(name: 'REMOVE', defaultValue: false, description: 'set remove to true to remove given roles from the given users')
    }
    environment {
        GOMODCACHE = "/var/cache/go/master/mod"
        GOCACHE = "/var/cache/go/master/go-build"
        GOPROXY = "https://goproxy.pointz.in"
    }
    stages {
        stage('Get Gamma Repo') {
            steps {
                dir('gamma') {
                    deleteDir()
                    checkout([
                        $class: 'GitSCM',
                        branches: [[name: "master"]],
                        extensions: [[
                            $class: 'CloneOption',
                            shallow: true,
                            depth:   1,
                            timeout: 30
                        ]],
                        userRemoteConfigs: [[
                            url:           'https://github.com/epiFi/gamma.git',
                            credentialsId: 'github-app-epifi'
                        ]]
                    ])
                }
            }
        }
        stage('Execute script') {
            steps {
                script {
                    dir('gamma') {
                        sh '''make build-script target="keycloak/manage_role"'''
                    }
                }
                script {
                    dir('gamma/output/keycloak/manage_role') {
                        sh "ENVIRONMENT=deploy ./keycloak/manage_role_bin -usernames=${params.USERNAMES} -remove=${params.REMOVE} -roles=${params.ROLES} -clientid=${params.CLIENT_ID}"
                    }
                }
            }
        }
    }
}
