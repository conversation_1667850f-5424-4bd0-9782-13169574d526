@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label "${params.TENANT == 'epifi' ? 'jenkins-cloud-worker' : 'stockguardian-deployment'}" }
    tools {
        go 'Go stable'
    }
    options {
        buildDiscarder(logRotator(numToKeepStr: '25'))
    }
    parameters {
        string(name: 'IMAGE_NAME', defaultValue: '', description: 'Name of the image to be built')
        string(name: "SOURCE_IMAGE_TAG", defaultValue:"", description: "source image tag available in Docker Hub")
        string(name: "ECR_REPO_NAME", defaultValue:"", description: "ECR Repo Name")
        string(name: 'DESTINATION_IMAGE_TAGS', defaultValue:"", description: "Comma separated list of destination image tags")
        choice(name: 'TENANT', choices: ['epifi', 'stockguardian'], description: 'Tenant name')
    }
    environment {
        SOURCE_IMAGE_TAG="${params.SOURCE_IMAGE_TAG}"
        IMAGE_NAME="${params.IMAGE_NAME}"
        ECR_REPO_NAME="${params.ECR_REPO_NAME}"
        DESTINATION_IMAGE_TAGS="${params.DESTINATION_IMAGE_TAGS}"
        ECR_URL="${params.TENANT == 'epifi' ? '854002675954.dkr.ecr.ap-south-1.amazonaws.com' : '329599620259.dkr.ecr.ap-south-1.amazonaws.com'}"
    }
    stages {
        stage('Upload Image to ECR') {
            steps {
                script {
                    def destTags = DESTINATION_IMAGE_TAGS.split(',').collect { it.trim() }
                    def cleanImageName = IMAGE_NAME.replaceAll('/', '-')
                    def defaultImageTag = cleanImageName + "-" + SOURCE_IMAGE_TAG
                    if (!destTags.contains(defaultImageTag)) {
                        destTags.add(defaultImageTag)
                    }
                    sh """
                        set +x
                        docker pull ${IMAGE_NAME}:${SOURCE_IMAGE_TAG}
                        aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ${ECR_URL}
                    """
                    destTags.each { imageTag ->
                         echo "Processing tag: ${imageTag}"
                         sh """
                             set +x
                             echo "Tagging image: ${IMAGE_NAME}:${SOURCE_IMAGE_TAG} -> ${ECR_URL}/${ECR_REPO_NAME}:${imageTag}"
                             docker tag ${IMAGE_NAME}:${SOURCE_IMAGE_TAG} ${ECR_URL}/${ECR_REPO_NAME}:${imageTag}

                             echo "Pushing image to ECR: ${ECR_URL}/${ECR_REPO_NAME}:${imageTag}"
                             docker push ${ECR_URL}/${ECR_REPO_NAME}:${imageTag}
                             set -x
                         """
                     }
                }
            }
        }
    }
}
