@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    options {
            ansiColor('xterm')
            disableConcurrentBuilds()
            buildDiscarder(logRotator(numToKeepStr: 'X', artifactNumToKeepStr: 'X'))
        }
    parameters {
        choice(name: 'ENV', choices: ['deploy'], description: 'Environment in which to execute the script')
        choice(name: 'REPO_NAME', choices: ['gamma', 'be-common'], description: 'Repo in which Dock<PERSON><PERSON><PERSON> is present')
        string(name: 'BUILD_BRANCH', defaultValue: 'master', description: 'Name of the branch from which pipeline executes scripts')
        string(name: 'DOCKER_FILE_PATH', defaultValue: '', description: 'Path to docker file relative to {repo}/build')
        string(name: 'ECR_REPO', defaultValue: '', description: 'Path to docker file relative to {repo}/build')
        string(name: 'IMAGE_VERSION', defaultValue: 'latest', description: 'Path to docker file relative to {repo}/build')
        booleanParam(name: 'REFRESH', defaultValue: false, description: 'Use this to refresh jenkins job post pipeline changes')
    }
    environment {
        ENV = "${params.ENV}"
        BUILD_BRANCH = "${params.BUILD_BRANCH}"
        GOMODCACHE = "/var/cache/go/master/mod"
        GOCACHE = "/var/cache/go/master/go-build"
        ECR_ENDPOINT = "632884248997.dkr.ecr.ap-south-1.amazonaws.com"
        ECR_IMAGE_PATH = "${ECR_ENDPOINT}/${params.ECR_REPO}:${params.IMAGE_VERSION}"
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true}
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-${ENV}-REFRESH:${params.REFRESH}"
                    currentBuild.description = "#${BUILD_NUMBER}-${ENV}-REFRESH:${params.REFRESH}"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Validate inputs') {
            steps {
                script {
                    if (params.DOCKER_FILE_PATH == "" || params.ECR_REPO == "" ) {
                        error('invalid input parameters')
                    }
                }
            }
        }
        stage('Repo clone') {
            steps {
                // performs shallow clone of repo by default
                // to perform deep clone pass `shallow: false` explicitly
                gitCheckout(repoName: "${params.REPO_NAME}", gitBranch: "${BUILD_BRANCH}", githubOrg: 'epifi')
            }
        }
        stage('Execute') {
            steps {
                dir("${params.REPO_NAME}") {
                   script {
                    dockerECRLogin(ecrEndpoint: "${ECR_ENDPOINT}")
                    sh """

                        echo "pushing image to ${env.ECR_IMAGE_PATH}"
                        docker build -f ./build/${params.DOCKER_FILE_PATH} . -t ${env.ECR_IMAGE_PATH}
                        docker push ${env.ECR_IMAGE_PATH}
                    """
                   }
                }
            }
        }
    }
    post {
        always {
            dir("${params.REPO_NAME}") {
                sh "docker image rm -f ${env.ECR_IMAGE_PATH} || true"
            }
        }
    }
}
