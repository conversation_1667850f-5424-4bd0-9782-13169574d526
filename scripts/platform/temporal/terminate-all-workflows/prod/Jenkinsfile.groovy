@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '10', artifactNumToKeepStr: '10'))
        timestamps()
    }
    parameters {
        string(name: 'NAMESPACE', defaultValue: '', description: 'temporal namespace where workflows needs to be terminated')
    	string(name: 'REASON', defaultValue: '', description: '')
    	string(name: 'WORKFLOW_NAME', defaultValue: '', description: 'Name of the workflow type whose executions are to be terminated')
    	string(name: 'RATE', defaultValue: '25', description: 'Rate of workflow termination')
    	string(name: 'RATE_DURATION', defaultValue: '10s', description: 'duration to apply the rate')
        string(name: 'VERSION', defaultValue: '1.0.0', description: 'Binary Version to Execute')
    }
    environment {
        ENVIRONMENT = "prod"
        DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP = "true"
    }
    stages {
        stage('Fetch binary') {
            steps {
                dir('gamma') {
                    script {
                        echo "Fetching pre-built binary"
                        sh "aws s3 cp s3://epifi-gamma-binaries-prod/temporal/terminate_all_workflows/linux/amd64/${params.VERSION} ./terminate_all_workflows.zip"
                        sh "unzip -u terminate_all_workflows.zip"
                    }
                }
            }
        }
        stage('Execute script') {
            steps {
                dir('gamma/temporal/terminate_all_workflows') {
                    script {
                        sh "temporal/terminate_all_workflows_bin -namespace=$NAMESPACE -reason=\"$REASON\" -workflow-name=$WORKFLOW_NAME -rate=$RATE -rate-duration=$RATE_DURATION"
                    }
                }
            }
        }
    }
}
