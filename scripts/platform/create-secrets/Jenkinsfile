@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'optimized-worker' }

    parameters {
        choice(name: 'ENV', choices: ['qa', 'staging','uat','data-dev','stockguardian-qa'], description: 'AWS account alias to deploy to')
        string(name: 'SECRET_NAME', description: 'Name of the secret')
        string(name: 'SERVICE', description: 'Service using the secret')
        string(name: 'BU_NAME', description: 'Business unit owning the secret')
        password(name: 'SECRET_VALUE', defaultValue: '', description: 'Enter your secret securely')
        string(name: 'INFRA_BRANCH', defaultValue:'master',description: 'INfra Branch')
    }
    environment {
        ENV="${params.ENV}"
        SECRET_NAME="${params.SECRET_NAME}"
        SERVICE="${params.SERVICE}"
        BU_NAME="${params.BU_NAME}"
        SECRET_VALUE="${params.SECRET_VALUE}"
        INFRA_BRANCH="${params.INFRA_BRANCH}"
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true }
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-Refresh"
                    currentBuild.description = "#${BUILD_NUMBER}-Refresh"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Clone Repo') {
            steps {
                script {
                    gitCheckout(repoName: "infra", gitBranch: "${INFRA_BRANCH}", githubOrg: "epifi", cleanCheckout: true)
                }
            }
        }
        stage('Assume Role') {
            steps {
                script {
                    assumeRole()
                }
            }
        }
        stage('Create secret') {
            steps {
                dir ('infra/scripts/secrets/create') {
                    script {
                        sh """
                            set +x
                            . ${env.WORKSPACE}/.env
                            python3 main.py \\
                            --env ${ENV} \\
                            --secret_name ${SECRET_NAME} \\
                            --service ${SERVICE} \\
                            --bu_name ${BU_NAME} \\
                            --secret_value '${SECRET_VALUE}'
                            set -x
                        """
                    }
                }
            }
        }

}

}