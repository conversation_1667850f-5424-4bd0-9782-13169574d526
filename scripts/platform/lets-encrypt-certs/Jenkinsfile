@Library("epifi-jenkins-libraries") _
pipeline {
        agent { label 'jenkins-cloud-worker' }
        options {
            ansiColor('xterm')
            disableConcurrentBuilds()
            buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '20'))
        }
        environment {
            ENV = "${params.ENV}"
            CHANNEL_NAME = "#alerts-infra-prod"
            JOB_DETAILS = "P1: Lets encrypt cert job failed. Get upp and fix right now !!!!!!!"
        }
        stages {
            stage('Clone Infrastructure') {
                steps {
                    dir('infra') {
                        gitCheckout(repoName: 'infra', gitBranch: 'master', githubOrg: 'epifi', cleanCheckout: true, shallow: false)
                    }
                }
            }
            stage('Assume Role') {
                steps {
                    script {
                        assumeRole()
                    }
                }
            }
            stage('Run Terraform Code') {
                steps {
                    dir("infra/infra/terraform/acme/${ENV}") {
                        script {
                            sh "set +x && . ${WORKSPACE}/.env && mkdir -p $HOME/.terraform.d/plugin-cache"
                            sh "set +x && . ${WORKSPACE}/.env && export TF_PLUGIN_CACHE_DIR=$HOME/.terraform.d/plugin-cache"
                            sh "set +x && . ${WORKSPACE}/.env && ls && terraform init"
                            sh "set +x && . ${WORKSPACE}/.env && terraform apply -input=false -auto-approve"
                        }
                    }
                }
            }
        }
        post {
            failure {
                script{
                    sendNotifications(
                        channelName: "${CHANNEL_NAME}", 
                        ENV: "${ENV}",
                        jobDetails: "${JOB_DETAILS}",
                    )
                currentBuild.setKeepLog(true)
               }
            }
        aborted {
            script{
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "LETSENCRYPT JOB ABORTED",
                )
                currentBuild.setKeepLog(true)
            }
        }
    }
}
