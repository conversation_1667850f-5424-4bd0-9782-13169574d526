@Library("epifi-jenkins-libraries") _
pipeline {
    agent any
    options {
        ansiColor('xterm')
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '20'))
    }
    parameters {
        string(name: 'TARGETS', defaultValue: '', description: 'Enter comma separated target\'s module name you want to modify or create. Ex: deploy-service1,deploy-service2')
        string(name: 'INFRA_BRANCH', defaultValue: 'master', description: 'Branch for Infra Repo to be used')
        choice(name: 'ENV', choices: ['staging','qa','uat','data-dev','deploy'], description: 'Select the environment for resource')
        choice(name: 'ACTION', choices: ['plan','apply'], description: 'Choose the terraform action')
        booleanParam(name: 'REFRESH', defaultValue: false, description: 'Devs, ignore this variable! This is for DevOps to refresh Jenkinsfile.')
    }
    environment {
        ENV = "${params.ENV}"
        INFRA_BRANCH = "${params.INFRA_BRANCH}"
    }
    stages {
        stage('Refresh') {
            steps {
                script {
                    if (params.REFRESH == true) {
                        echo 'Jenkins file was loaded....... finish build now'
                        currentBuild.displayName = "#${BUILD_NUMBER}-Refresh"
                        currentBuild.description = "#${BUILD_NUMBER}-Refresh"
                        currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                        sleep(5)
                    }
                }
            }
        }

        stage('Assume Role') {
            steps {
                script {
                    assumeRole()
                }
            }
        }
        stage('Infra clone') {
            steps {
                dir('infra') {
                   gitCheckout(repoName: "infra", gitBranch: "${INFRA_BRANCH}", githubOrg: "epifi")
                }
            }
        }
        stage("Terraform Elasticache Plan") {
            steps {
                script {
                    if (params.ACTION=="plan") {
                        targets_elasticache = [:]
                        "$TARGETS".split(',').each {
                            targets_elasticache["${it}"] = {
                                stage("Plan elasticache Changes for ${it}") {
                                    dir("${env.WORKSPACE}/infra/infra/terraform/elasticache_v2/${ENV}") {
                                        script {
                                            sh 'rm -rf .terraform'
                                            sh "terraform init -backend-config=s3-backend-${ENV}.conf"
                                            sh "terraform plan -var-file=../../env/${ENV}.tf "
                                        }
                                    }
                                }
                            }
                        }
                        parallel targets_elasticache
                    }
                }
            }
        }
        stage("Terraform elasticache Apply") { 
            steps {
                script {
                    if (params.ACTION=="apply") {
                        targets_elasticache = [:]
                        "$TARGETS".split(',').each {
                            targets_elasticache["${it}"] = {
                                stage("Apply elasticache Changes for ${it}") {
                                    dir("${env.WORKSPACE}/infra/infra/terraform/elasticache_v2/${ENV}") {
                                        script {
                                            sh 'rm -rf .terraform'
                                            sh "terraform init -backend-config=s3-backend-${ENV}.conf"
                                            sh "terraform apply --auto-approve -var-file=../../env/${ENV}.tf "
                                        }
                                    }
                                }
                            }
                        }
                        parallel targets_elasticache
                    }
                }
            }
        }
    }
    post { 
        always { 
            cleanWs(
                notFailBuild: true
            )
        }
    }
}
