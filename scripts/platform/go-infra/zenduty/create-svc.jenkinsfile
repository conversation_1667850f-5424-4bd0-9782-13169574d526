pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(
            logRotator(
                numToKeepStr: '20', 
                artifactNumToKeepStr: '10'
            )
        )
    }
    parameters {
        string(
            name: "team", 
            description: "Name of the team. For eg. pay, infra, ds, data-platform etc.",
            trim: true
        )
        string(
            name: "level1handler",
            description: "User account (email) of user handling level 1 escalation. Typically the team (group) account in our case for eg. <EMAIL>",
            trim: true
        )
        string(
            name: "level2handler",
            description: "User account (email) of user handling level 2 escalation. Typically the BU lead's personal account eg. <EMAIL>",
            trim: true
        )
    }
    stages {
        stage('Download') {
            steps {
                sh "aws s3 cp --no-progress s3://epifi-go-infra-bin/zenduty/linux/amd64/2.3.1 ./zenduty"
                sh "chmod u+x ./zenduty"
            }
        }
        stage('Run') {
            steps {
                sh "./zenduty -name \"${params.team}\" -lev1 \"${params.level1handler}\" -lev2 \"${params.level2handler}\""
            }
        }
    }
}
