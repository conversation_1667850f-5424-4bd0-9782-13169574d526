@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    options {
        buildDiscarder(logRotator(numToKeepStr: '25'))
    }
    parameters {
        string(name: 'VERSION', defaultValue: 'stable', description: 'Binary Version to Execute')
        string(name: "S3_BUCKET_PATH", defaultValue:"", description: "Enter s3 bucket path where you stored the non-prod secret")
    }
    environment {
        ENV = "prod"
        ENDPOINT_CONFIG_DIR = './config'
    }
    stages {
        stage('Get Gamma Repo') {
            steps {
                gitCheckout(repoName: "gamma", gitBranch: "master", githubOrg: "epifi")
            }
        }
        stage('Fetch binary') {
            steps {
                dir('gamma/output') {
                    script {
                        echo "Fetching pre-built binary"
                        sh """
                        chmod 777 ${env.WORKSPACE}/scripts/platform/gamma/helper.sh
                        ${env.WORKSPACE}/scripts/platform/gamma/helper.sh fetch_binary epifi-gamma-binaries-prod platform/leaked_secrets/compare_secrets ${params.VERSION}
                        """
                    }
                }
            }
        }
        stage('Execute script') {
            steps {
                script {
                     dir('gamma') {
                        script {
                            sh """
                            #!/bin/bash
                            OUTPUT_DIR=./output/platform/leaked_secrets/compare_secrets/platform/leaked_secrets/ ./scripts/platform/leaked_secrets/pre-execution.sh
                            """
                        }
                     }
                    dir('gamma/output') {
                          script {
                             sh """
                             set +x
                             cd platform/leaked_secrets/compare_secrets/platform/leaked_secrets/
                             aws s3 cp ${params.S3_BUCKET_PATH} ./secrets --recursive
                             ./compare_secrets_bin --config-dir='./config' --csv-dir='./secrets'
                             set -x
                             """
                          }
                    }
                }
            }
        }
    }
}
