@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    options {
        buildDiscarder(logRotator(numToKeepStr: '25'))
    }
    parameters {
        string(name: 'VERSION', defaultValue: 'stable', description: 'Binary Version to Execute')
        choice(name: 'ENV', choices: ['qa', 'staging', 'uat'], description: 'Environment in which to execute the script')
        string(name: "S3_BUCKET_PATH", defaultValue: '', description: "Enter s3 bucket path where you want to store non prod secrets which is used by compare secrets script used in prod")
    }
    environment {
        ENV = "${params.ENV}"
        ENDPOINT_CONFIG_DIR = './config'
    }
    stages {
        stage('Get Gamma Repo') {
            steps {
                gitCheckout(repoName: "gamma", gitBranch: "master", githubOrg: "epifi")
            }
        }
        stage('Fetch binary') {
            steps {
                dir('gamma/output') {
                    script {
                        dir('certs') {
                            assumeRole()
                        }
                    }
                    script {
                        echo "Fetching pre-built binary"
                        sh """
                        set +x
                            . ${WORKSPACE}/.env
                        set -x
                        chmod 777 ${env.WORKSPACE}/scripts/platform/gamma/helper.sh
                        ${env.WORKSPACE}/scripts/platform/gamma/helper.sh fetch_binary epifi-jenkins-job-binaries platform/leaked_secrets/store_non_prod_secrets  ${params.VERSION}
                        ls
                        """
                    }
                }
            }
        }
        stage('Execute script') {
            steps {
                script {
                     dir('gamma') {
                        script {
                            sh """
                            #!/bin/bash
                            OUTPUT_DIR=./output/platform/leaked_secrets/store_non_prod_secrets/platform/leaked_secrets ./scripts/platform/leaked_secrets/pre-execution.sh
                            """
                        }
                     }
                    dir('gamma/output') {
                         script {
                             sh """
                             set +x
                             cd platform/leaked_secrets/store_non_prod_secrets/platform/leaked_secrets
                             ./store_non_prod_secrets_bin --config-dir='./config'
                             aws s3 cp output/secrets ${params.S3_BUCKET_PATH} --recursive
                             set -x
                             """
                         }
                    }
                }
            }
        }
    }
}
