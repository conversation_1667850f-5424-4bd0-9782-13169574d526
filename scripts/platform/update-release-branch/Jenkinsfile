@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    parameters {
            choice(name: 'ENV', choices: ['qa', 'prod'], description: 'environment for which want to update release branch')
            string(name: 'RELEASE_BRANCH', defaultValue: '', description: 'release branch name')
    }
    stages {
         stage('Validate input params') {
            steps {
                script {
                    def releaseBranchPattern = /^epifi\/m[0-9]{3}-rc[1-9]{1}-[A-Za-z]{3}-[0-9]{2}$/
                       if (!params.RELEASE_BRANCH.matches(releaseBranchPattern) ) {
                            error('The release branch does not match the required pattern /^epifi\\/m[0-9]{3}-rc[1-9]{1}-[A-Za-z]{3}-[0-9]{2}$.')
                       }
                }

            }
         }
         stage('Update release_branch and Push to S3') {
           steps {
               script {
                   sh "aws s3 cp s3://epifi-deploy-platform/release_branch.json release_branch.json"

                   def jsonFile = readFile 'release_branch.json'
                   def json = readJSON text: jsonFile

                   json[params.ENV] = params.RELEASE_BRANCH
                   writeFile file: 'release_branch.json', text: groovy.json.JsonOutput.toJson(json)
                   sh "echo update release_branch.json"
                   sh "cat release_branch.json"
                   sh "aws s3 cp release_branch.json s3://epifi-deploy-platform/release_branch.json"
               }
           }
         }

    }
}
