@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools {
        go 'Go stable'
    }
    options {
        buildDiscarder(logRotator(numToKeepStr: '25'))
    }
    parameters {
        choice(name: 'SOURCE', choices: ['prod'], description: 'Environment from which to copy the secret')
        choice(name: 'DEST', choices: ['data-prod'],  description: 'Environment from which to copy the secret')
        string(name: 'SECRET_NAME', defaultValue: '', description: 'Secret name to be copied')
        booleanParam(name: 'REFRESH', defaultValue: false, description: '<PERSON><PERSON>, ignore this variable! This is for <PERSON>O<PERSON> to refresh Jenkins<PERSON>le.')
    }
    environment {
        SOURCE = "${params.SOURCE}"
        DEST = "${params.DEST}"
        SECRET_NAME = "${params.SECRET_NAME}"
        GOPROXY = "https://goproxy.epifi.in"
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true}
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-REFRESH"
                    currentBuild.description = "#${BUILD_NUMBER}REFRESH"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Repo clone') {
            steps {
                gitCheckout(repoName: 'go-infra', gitBranch: "master", githubOrg: 'epifi')
            }
        }
        stage('Copy secrets') {
            steps {
                dir("go-infra") {
                    sh """
                     make build-script target=copy_secret
                     ./bin/copy_secret --src-env=${SOURCE} --dest-env=${DEST} --secret-name=${SECRET_NAME}
                    """
                }
            }
        }
    }
}
