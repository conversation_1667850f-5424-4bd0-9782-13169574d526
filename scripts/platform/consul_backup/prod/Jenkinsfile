@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'alpha' }
    triggers{
       cron('H 1 * * *')
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '10', artifactNumToKeepStr: '10'))
    }
    parameters {
        booleanParam(
            name: 'REFRESH', defaultValue: false, 
            description: '<PERSON><PERSON>, ignore this variable! This is for DevOps to refresh Jenkins<PERSON>le.'
        )
    }
    environment {
        ENV = 'prod'
        CHANNEL_NAME = '#alerts-infra-prod'
        JOB_DETAILS="Consul_backup"
    }
    stages {
        stage ('Refresh') {
            when {
                expression { 
                    params.REFRESH == true
                }
            }
            steps {
                buildName "#${BUILD_NUMBER}-${ENV}"
                echo "Jenkins pipeline script refreshed. Finishing exceution now.."
                script {
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Consul_backup') {
            steps {
                dir("consul"){
                    sh """
                        #!/bin/bash

                        today=\$(date +%Y%m%d)
                        token=\$(aws secretsmanager get-secret-value --secret-id epifi/consul/token/consul --query SecretString --output text | jq -r .token)
                        CONSUL_HTTP_SSL_VERIFY=false consul snapshot save -token=\$token -http-addr=https://prod-consul.pointz.in prod-\$today.snap
                        aws s3 cp prod-\$today.snap s3://epifi-prod-consul-backup/prod/
                    """
                }
            }
        }
    }
    post {
        failure {
            script {
                echo "build failed"
                def integrationKey = "c5db7a73-a029-4a16-b558-16e444fe8c45"
                def jobName = "Consul Backup"
                def team = "devops"
                def severity = "p0"

                sendJobFailureAlert(integrationKey: "${integrationKey}", jobName: "${jobName}", team: "${team}", severity: "${severity}")
                
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
        aborted {
            script{
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
    }
}