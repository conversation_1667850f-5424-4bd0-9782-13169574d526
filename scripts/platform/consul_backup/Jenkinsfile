@Library("epifi-jenkins-libraries") _
pipeline {
    agent {
        label 'optimized-worker'
    }
    triggers {
        parameterizedCron('''
            H 20 * * *  %ROLE_NAME=staging
            H 20 * * *  %ROLE_NAME=qa
            H 20 * * *  %ROLE_NAME=uat
        ''')
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(
          logRotator(
            numToKeepStr: '20',
            artifactNumToKeepStr: '20'
          )
        )
        timeout(
          time: 15,
          unit: 'MINUTES'
        )
        timestamps()
    }
    parameters {
        choice(
          name: 'ROLE_NAME',
          choices: [
            'staging',
            'qa',
            'uat'
          ],
          description: 'Role identifier for execution, to be selected as per the concerned env'
        )
        booleanParam(
          name: 'REFRESH',
          defaultValue: false,
          description: 'Refresh pipeline script from SCM'
        )
    }
    environment {
        ENV = "${params.ROLE_NAME}"
        CONSUL_HTTP_SSL_VERIFY = 'false'
        CHANNEL_NAME = '#alerts-infra'
        JOB_DETAILS = "${params.ROLE_NAME} consul back-up"
    }
    stages {
        stage ('Refresh') {
            when {
                expression { 
                    params.REFRESH == true
                }
            }
            steps {
                buildName "#${BUILD_NUMBER}-REFRESH"
                echo "Jenkins pipeline script refreshed. Exiting.."
                script {
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Assume Role') {
            steps {
                script {
                    assumeRole()
                }
            }
        }
        stage('Back-up') {
            steps {
                buildName "#${BUILD_NUMBER}-${params.ROLE_NAME}"
                sh """
                  set +x
                  . ${env.WORKSPACE}/.env
                  export TOKEN=\$(
                    aws secretsmanager get-secret-value \
                      --secret-id epifi/consul/token/consul \
                      --query SecretString \
                      --output text \
                      | jq -r .token
                  )

                  export TS=\$(date +'%Y%m%d-%H%MZ')
                  consul snapshot save \
                    -token=\${TOKEN} \
                    -http-addr=https://${params.ROLE_NAME}-consul.pointz.in \
                    consul-${params.ROLE_NAME}-\${TS}.snap
                  set -x

                  aws s3 cp \
                    consul-${params.ROLE_NAME}-\${TS}.snap \
                    s3://epifi-consul-backup/${params.ROLE_NAME}/
                """
            }
        }
    }
    post {
        failure {
            script {
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
        aborted {
            script{
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
    }
}