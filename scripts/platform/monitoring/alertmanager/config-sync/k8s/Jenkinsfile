@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(
            numToKeepStr: '20', 
            artifactNumToKeepStr: '10'
        ))
        timeout(time: 15, unit: 'MINUTES')
    }
    triggers {
        cron('@hourly')
    }
    parameters {
        string (
            name: 'INFRA_BRANCH', defaultValue: 'master', 
            description: 'Branch for Infra Repo to be used'
        )
        string (
            name: 'MONITORING_BRANCH', defaultValue: 'master', 
            description: 'Branch for Infra Repo to be used'
        )
        booleanParam (
            name: 'REFRESH', defaultValue: false, 
            description: 'Refresh Jenkins<PERSON>le pipeline script from SCM'
        )
    }
    environment {
        INFRA_BRANCH = "${params.INFRA_BRANCH}"
        MONITORING_BRANCH = "${params.MONITORING_BRANCH}"
        CHANNEL_NAME = '#alerts-infra'
        JOB_DETAILS="Alertmanager Config Sync - Kubernetes"
        ENV='Deploy'
    }
    stages {
        stage ('Refresh') {
            when {
                expression { 
                    params.REFRESH == true
                }
            }
            steps {
                buildName "#${BUILD_NUMBER}-${ENV}-${MONITORING_BRANCH}"
                echo "Jenkins pipeline script refreshed. Finishing exceution now.."
                script {
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Apply') {
            steps {
                sh "aws eks update-kubeconfig --name=epifi-deploy"
                // temporary: until directory permissions fixed in AMI
                sh "sudo chown ubuntu:ubuntu /home/<USER>/.config"
                sh "helm repo add prometheus-community https://prometheus-community.github.io/helm-charts -n monitoring"

                // fetch base helm config values from k8-infra repo
                gitCheckout (
                    repoName: 'k8-infra', 
                    gitBranch: "${INFRA_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )

                dir ("k8-infra/monitoring/alertmanager/deploy") {
                    // fetch alertmanager config from monitoring repo
                    gitCheckout (
                        repoName: 'monitoring', 
                        gitBranch:  "${MONITORING_BRANCH}", 
                        githubOrg: 'epifi', 
                        cleanCheckout: true, 
                        shallow: false
                    )
                    sh """
                        helm upgrade --install --atomic epifi-deploy-alertmanager \
                          prometheus-community/alertmanager --version 0.26.1 \
                          --values values.yaml \
                          --values ./monitoring/alertmanager/deploy-k8s/alertmanager.yml \
                          -n monitoring
                    """
                }
            }
        }
    }
    post {
        failure {
            script {
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
        aborted {
            script {
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
    }
}
