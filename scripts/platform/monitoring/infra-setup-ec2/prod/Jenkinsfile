@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '20'))
        timeout(time: 15, unit: 'MINUTES')
    }
    parameters {
        string(
            name: 'INFRA_BRANCH', defaultValue: 'master', 
            description: 'Specify custom infra repo branch'
        )
        choice(
            name: 'ACTION',
            choices: [
                'plan',
                'apply'
            ],
            description: 'Select terraform action'
        )
        choice(
            name: 'COMPONENT',
            choices: [
                'grafana',
                'alertmanager',
                // note: this applies to both prometheus instances: "prod" & "datasec"
                'prometheus',
                'victoriametrics'
            ],
            description: 'Select component'
        )
        booleanParam(
            name: 'REFRESH', defaultValue: false,
            description: 'Refresh Jenkinsfile pipeline script from SCM'
        )
    }
    environment {
        ENV = 'prod'
        INFRA_BRANCH = "${params.INFRA_BRANCH}"
    }
    stages {
        stage ('Refresh') {
            when {
                expression { 
                    params.REFRESH == true
                }
            }
            steps {
                buildName "#${BUILD_NUMBER}-REFRESH"
                echo "Jenkins pipeline script refreshed. Exiting.."
                script {
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Code fetch') {
            steps {
                buildName "#${BUILD_NUMBER}-${params.COMPONENT}-${INFRA_BRANCH}"
                gitCheckout(
                    repoName: 'infra', 
                    gitBranch: "${INFRA_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )
            }
        }
        stage('Plan') {
            when {
                expression {
                    params.ACTION == "plan"
                }
            }
            steps {
                dir("${env.WORKSPACE}/infra/terraform/monitoring/${params.COMPONENT}") {
                    sh """
                      terraform init -backend-config=s3-backend-prod.conf
                      terraform plan -var-file=../../env/prod.tf
                    """
                }
            }
        }
        stage('Apply') {
            when {
                expression {
                    params.ACTION == "apply"
                }
            }
            steps {
                dir("${env.WORKSPACE}/infra/terraform/monitoring/${params.COMPONENT}") {
                    sh """
                      terraform init -backend-config=s3-backend-prod.conf
                      terraform apply -auto-approve -var-file=../../env/prod.tf
                    """
                }
            }
        }
    }
}
