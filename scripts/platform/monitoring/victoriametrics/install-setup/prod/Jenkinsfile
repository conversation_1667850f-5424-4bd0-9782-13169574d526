@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '20'))
        timeout(time: 15, unit: 'MINUTES')
    }
    parameters {
        string(
            name: 'INFRA_BRANCH', defaultValue: 'master', 
            description: 'Branch for Infra Repo to be used'
        )
        choice(
            name: 'OPERATION',
            choices: [
              'upgrade',
              'new_install'
            ],
            description: 'Select whether it is an upgrade operation or new installation'
        )
        booleanParam(
            name: 'REFRESH', defaultValue: false,
            description: 'Refresh Jenkinsfile pipeline script from SCM'
        )
    }
    environment {
        ENV = 'prod'
        INFRA_BRANCH = "${params.INFRA_BRANCH}"
        CHANNEL_NAME = '#alerts-infra'
        JOB_DETAILS = "VictoriaMetrics Install & Setup"
    }
    stages {
        stage ('Refresh') {
            when {
                expression { 
                    params.REFRESH == true
                }
            }
            steps {
                buildName "#${BUILD_NUMBER}-${ENV}-${INFRA_BRANCH}"
                echo "Jenkins pipeline script refreshed. Finishing exceution now.."
                script {
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Repo Clone') {
            steps {
                gitCheckout(
                    repoName: 'infra', 
                    gitBranch: "${INFRA_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )
            }
        }
        stage('Deployment') {
            steps {
                dir("${env.WORKSPACE}/infra/ansible/monitoring") {
                    sh '''
                        aws secretsmanager get-secret-value --secret-id monitoring_stack_private_key | jq -r '.SecretString' > monitoring.pem
                        chmod 600 monitoring.pem
                        pwd
                        ls -a
                    '''
                    sh "ansible-playbook victoriametrics.yml -e env=${ENV} -e operation=${params.OPERATION}"
                }
            }
        }
    }
    post {
        failure {
            script{
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
        aborted {
            script{
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
    }
}
