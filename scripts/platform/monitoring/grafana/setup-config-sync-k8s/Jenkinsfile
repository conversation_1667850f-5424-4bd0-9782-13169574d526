@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(
            numToKeepStr: '20', 
            artifactNumToKeepStr: '10'
        ))
        timeout(time: 15, unit: 'MINUTES')
    }
    parameters {
        string (
            name: 'INFRA_BRANCH', defaultValue: 'master', 
            description: 'Branch for k8-infra repo to be used'
        )
        booleanParam (
            name: 'REFRESH', defaultValue: false, 
            description: 'Refresh Jenkinsfile pipeline script from SCM'
        )
    }
    environment {
        INFRA_BRANCH = "${params.INFRA_BRANCH}"
        CHANNEL_NAME = '#alerts-infra'
        JOB_DETAILS="Kubernetes Grafana Config Sync"
        ENV='deploy'
    }
    stages {
        stage ('Refresh') {
            when {
                expression { 
                    params.REFRESH == true
                }
            }
            steps {
                buildName "#${BUILD_NUMBER}-${ENV}-${INF<PERSON>_<PERSON>ANCH}"
                echo "<PERSON> pipeline script refreshed. Finishing execution now.."
                script {
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Repo Clone') {
            steps {
                gitCheckout (
                    repoName: 'k8-infra', 
                    gitBranch: "${INFRA_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )
            }
        }
        stage('Apply') {
            steps {
                sh "aws eks update-kubeconfig --name=epifi-deploy"
                // temporary: until directory permissions fixed in AMI
                sh "sudo chown ubuntu:ubuntu /home/<USER>/.config"
                sh "helm repo add grafana https://grafana.github.io/helm-charts -n monitoring"

                dir ("k8-infra/monitoring/grafana/deploy/") {
                    sh """
                        helm upgrade --install --atomic grafana \
                          grafana/grafana --version 7.0.19 \
                          --values values.yaml -n monitoring
                    """
                }
            }
        }
    }
    post {
        failure {
            script {
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
        aborted {
            script {
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
    }
}