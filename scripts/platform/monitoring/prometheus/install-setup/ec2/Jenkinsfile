@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '20'))
        timeout(time: 15, unit: 'MINUTES')
    }
    parameters {
        string(
            name: 'INFRA_BRANCH', defaultValue: 'master', 
            description: 'Branch for Infra Repo to be used'
        )
        booleanParam(
            name: 'INSTALL_CONFIG', defaultValue: true,
            description: 'Specify if Prometheus service config and alert rules are to be installed'
        )
        booleanParam(
            name: 'REFRESH', defaultValue: false,
            description: 'Refresh Jenkinsfile pipeline script from SCM'
        )
    }
    environment {
        ENV = 'deploy'
        INFRA_BRANCH = "${params.INFRA_BRANCH}"
        CHANNEL_NAME = '#alerts-infra'
        JOB_DETAILS="Prometheus Install & Setup - EC2"
        MONITORING_REPO="${env.WORKSPACE}/monitoring"
    }
    stages {
        stage ('Refresh') {
            when {
                expression { 
                    params.REFRESH == true
                }
            }
            steps {
                buildName "#${BUILD_NUMBER}-${ENV}-${INFRA_BRANCH}"
                echo "Jenkins pipeline script refreshed. Finishing exceution now.."
                script {
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Repo Clone') {
            steps {
                gitCheckout(
                    repoName: 'infra', 
                    gitBranch: "${INFRA_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )
                gitCheckout(
                    repoName: 'monitoring',
                    gitBranch: 'master',
                    githubOrg: 'epifi',
                    cleanCheckout: true,
                    shallow: false
                )
            }
        }
        stage('Deployment') {
            steps {
                dir("${env.WORKSPACE}/infra/ansible/monitoring") {
                    sh '''
                        aws secretsmanager get-secret-value --secret-id monitoring_stack_private_key | jq -r '.SecretString' > monitoring.pem
                        chmod 600 monitoring.pem
                        pwd
                        ls -a
                    '''
                    sh """
                      ansible-playbook prometheus.yml \
                        -e env=${ENV} -e install_config=${params.INSTALL_CONFIG} \
                        -e monitoring_repo=${env.WORKSPACE}/monitoring
                    """
                }
            }
        }
    }
    post {
        failure {
            script{
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
        aborted {
            script{
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
    }
}
