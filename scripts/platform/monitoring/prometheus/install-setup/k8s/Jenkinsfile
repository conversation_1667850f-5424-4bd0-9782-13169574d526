@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(
            logRotator(
                numToKeepStr: '20', 
                artifactNumToKeepStr: '10'
            )
        )
        timeout(time: 15, unit: 'MINUTES')
    }
    parameters {
        string (
            name: 'INFRA_BRANCH', defaultValue: 'master',
            description: 'Branch for k8-infra repo to be used'
        )
        choice(
          name: 'CLUSTER_NAME',
          choices: [
            'epifi-deploy',
            'epifi-deploy-runners',
            'epifi-staging',
            'epifi-qa',
            'epifi-uat',
            'epifi-data-dev'
          ],
          description: 'Target k8s cluster name'
        )
        choice(
          name: 'ROLE_NAME',
          choices: [
            'DEPLOY',
            'STAGING',
            'QA',
            'UAT',
            'DATA_DEV'
          ],
          description: 'Role identifier for execution, to be selected as per the concerned env'
        )
        booleanParam (
            name: 'REFRES<PERSON>', defaultValue: false, 
            description: 'Refresh Jenkinsfile pipeline script from SCM'
        )
    }
    environment {
        INFRA_BRANCH = "${params.INFRA_BRANCH}"
        CHANNEL_NAME = '#alerts-infra'
        JOB_DETAILS = "Kubernetes Prometheus Setup"
        ENV = 'deploy'
        APP_CHART_VERSION = "55.5.1"
    }
    stages {
        stage ('Refresh') {
            when {
                expression { 
                    params.REFRESH == true
                }
            }
            steps {
                buildName "#${BUILD_NUMBER}-${ENV}-${INFRA_BRANCH}"
                echo "Jenkins pipeline script refreshed. Finishing exceution now.."
                script {
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Deploy - Non-prod') {
            when {
                beforeAgent true
                anyOf {
                    environment name: 'CLUSTER_NAME', value: 'epifi-staging'
                    environment name: 'CLUSTER_NAME', value: 'epifi-deploy'
                    environment name: 'CLUSTER_NAME', value: 'epifi-qa'
                    environment name: 'CLUSTER_NAME', value: 'epifi-uat'
                    environment name: 'CLUSTER_NAME', value: 'epifi-deploy-runners'
                }
            }
            agent { label 'jenkins-cloud-worker' }
            steps {

                buildName "#${BUILD_NUMBER}-${CLUSTER_NAME}-${INFRA_BRANCH}"
                gitCheckout(
                    repoName: 'k8-infra', 
                    gitBranch: "${INFRA_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )

                echo "Assume role and apply changes.."

                dir ("${WORKSPACE}/k8-infra/monitoring/prometheus/${params.CLUSTER_NAME}") {
                    sh """
                      aws sts assume-role --region ap-south-1 --role-arn \${${ROLE_NAME}_ACCOUNT_ROLE} \
                          --role-session-name ${CLUSTER_NAME}-k8s-prom-deployment > .access.json
                      set +x
                      export AWS_ACCESS_KEY_ID=\$(jq -re .Credentials.AccessKeyId .access.json)
                      export AWS_SECRET_ACCESS_KEY=\$(jq -re .Credentials.SecretAccessKey .access.json)
                      export AWS_SESSION_TOKEN=\$(jq -re .Credentials.SessionToken .access.json)
                      set -x
                      aws eks list-clusters
                      aws eks update-kubeconfig --name=${CLUSTER_NAME}
                      kubectl config current-context

                      helm repo add -n monitoring \
                        prometheus-community https://prometheus-community.github.io/helm-charts
                      helm repo update

                      helm upgrade --install --atomic prometheus \
                        prometheus-community/kube-prometheus-stack --version ${APP_CHART_VERSION} \
                        --values values.yaml -n monitoring
                    """
                }

            }
        }
        
        stage('Deploy - Data Dev') {
            when {
                beforeAgent true
                environment name: 'CLUSTER_NAME', value: 'epifi-data-dev'
            }
            agent {
                label 'jenkins-data-dev-slave-node'
            }
            steps {

                buildName "#${BUILD_NUMBER}-${CLUSTER_NAME}-${INFRA_BRANCH}"
                gitCheckout(
                    repoName: 'k8-infra', 
                    gitBranch: "${INFRA_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )

                echo "Setting up k8s client config and applying changes.."
                sh """
                  aws eks list-clusters
                  aws eks update-kubeconfig --name=${CLUSTER_NAME}
                  kubectl config current-context
                """

                dir ("${WORKSPACE}/k8-infra/monitoring/prometheus/${params.CLUSTER_NAME}") {
                    sh """
                      helm repo add -n monitoring \
                        prometheus-community https://prometheus-community.github.io/helm-charts
                      helm repo update

                      helm upgrade --install --atomic prometheus \
                        prometheus-community/kube-prometheus-stack --version ${APP_CHART_VERSION} \
                        --values values.yaml -n monitoring
                    """
                }

            }
        }
    }
    post {
        failure {
            script {
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
        aborted {
            script {
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
    }
}