@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(
            logRotator(
                numToKeepStr: '20', 
                artifactNumToKeepStr: '10'
            )
        )
        timeout(time: 15, unit: 'MINUTES')
    }
    parameters {
        string (
            name: 'INFRA_BRANCH', defaultValue: 'master',
            description: 'Branch for k8-infra repo to be used'
        )
        choice(
          name: 'CLUSTER_NAME',
          choices: [
            'epifi-prod',
            'epifi-data-prod'
          ],
          description: 'Target k8s cluster name'
        )
        booleanParam (
            name: 'REFRESH', defaultValue: false, 
            description: 'Refresh Jenkinsfile pipeline script from SCM'
        )
    }
    environment {
        INFRA_BRANCH = "${params.INFRA_BRANCH}"
        CHANNEL_NAME = '#alerts-infra-prod'
        JOB_DETAILS = "Kubernetes Prometheus Setup"
        ENV = 'prod'
        APP_CHART_VERSION = "55.5.1"
    }
    stages {
        stage ('Refresh') {
            when {
                expression { 
                    params.REFRESH == true
                }
            }
            steps {
                buildName "#${BUILD_NUMBER}-${ENV}-${INFRA_BRANCH}"
                echo "Jenkins pipeline script refreshed. Finishing exceution now.."
                script {
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Deploy - Prod') {
            when {
                beforeAgent true
                environment name: 'CLUSTER_NAME', value: 'epifi-prod'
            }
            agent { label 'jenkins-cloud-worker' }
            steps {

                buildName "#${BUILD_NUMBER}-${CLUSTER_NAME}-${INFRA_BRANCH}"
                gitCheckout(
                    repoName: 'k8-infra', 
                    gitBranch: "${INFRA_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )

                dir ("${WORKSPACE}/k8-infra/monitoring/prometheus/${params.CLUSTER_NAME}") {
                    sh """
                      aws eks list-clusters
                      aws eks update-kubeconfig --name=${CLUSTER_NAME}
                      kubectl config current-context

                      helm repo add -n monitoring \
                        prometheus-community https://prometheus-community.github.io/helm-charts
                      helm repo update

                      helm upgrade --install --atomic prometheus \
                        prometheus-community/kube-prometheus-stack --version ${APP_CHART_VERSION} \
                        --values values.yaml -n monitoring
                    """
                }

            }
        }
        
        stage('Deploy - Data Prod') {
            when {
                beforeAgent true
                environment name: 'CLUSTER_NAME', value: 'epifi-data-prod'
            }
            agent {
                label 'epifi-data-prod-jenkins-cloud-nodes'
            }
            steps {

                buildName "#${BUILD_NUMBER}-${CLUSTER_NAME}-${INFRA_BRANCH}"
                gitCheckout(
                    repoName: 'k8-infra', 
                    gitBranch: "${INFRA_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )

                dir ("${WORKSPACE}/k8-infra/monitoring/prometheus/${params.CLUSTER_NAME}") {
                    sh """
                      aws eks list-clusters
                      aws eks update-kubeconfig --name=${CLUSTER_NAME}
                      kubectl config current-context

                      helm repo add -n monitoring \
                        prometheus-community https://prometheus-community.github.io/helm-charts
                      helm repo update

                      helm upgrade --install --atomic prometheus \
                        prometheus-community/kube-prometheus-stack --version ${APP_CHART_VERSION} \
                        --values values.yaml -n monitoring
                    """
                }

            }
        }
    }
    post {
        failure {
            script {
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
        aborted {
            script {
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
    }
}