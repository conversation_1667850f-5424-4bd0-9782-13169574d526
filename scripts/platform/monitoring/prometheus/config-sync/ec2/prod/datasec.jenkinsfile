@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    triggers{
       cron('H/30 * * * *')
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '10', artifactNumToKeepStr: '10'))
        timeout(time: 15, unit: 'MINUTES')
    }
    parameters {
        string(
            name: 'INFRA_BRANCH', defaultValue: 'master', 
            description: 'Branch for Infra Repo to be used'
        )
        string(
            name: 'MONITORING_BRANCH', defaultValue: 'master', 
            description: 'Branch for Infra Repo to be used'
        )
        string(
            name: 'PROMETHEUS_VERSION', defaultValue: '2.24.0'
        )
        choice(
            name: 'ENV', choices: ['prod'], 
            description: 'AWS account alias to deploy to'
        )
        booleanParam(
            name: 'REFRESH', defaultValue: false, 
            description: '<PERSON><PERSON>, ignore this variable! This is for DevOps to refresh Jenkins<PERSON>le.'
        )
    }
    environment {
        ENV = "${params.ENV}"
        INFRA_BRANCH = "${params.INFRA_BRANCH}"
        MONITORING_BRANCH = "${params.MONITORING_BRANCH}"
        CHANNEL_NAME = '#alerts-infra-prod'
        JOB_DETAILS="Prometheus Config Sync - Datasec EC2"
    }
    stages {
        stage ('Refresh') {
            when {
                expression { 
                    params.REFRESH == true
                }
            }
            steps {
                buildName "#${BUILD_NUMBER}-${ENV}-${MONITORING_BRANCH}"
                echo "Jenkins pipeline script refreshed. Finishing exceution now.."
                script {
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Repo Clone') {
            steps {
                gitCheckout(
                    repoName: 'infra', 
                    gitBranch: "${INFRA_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )
                gitCheckout(
                    repoName: 'monitoring', 
                    gitBranch:  "${MONITORING_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )
            }
        }
        stage('Deployment') {
            steps {
                dir("${env.WORKSPACE}/infra/ansible/monitoring"){
                    sh '''
                        aws secretsmanager get-secret-value --secret-id monitoring_stack_private_key | jq -r '.SecretString' > monitoring.pem
                        chmod 600 monitoring.pem
                        pwd
                        ls -a
                    '''
                    sh 'ansible-playbook ./prometheus-security-config-sync.yml -e "WORKSPACE=$WORKSPACE/monitoring" -e env=${ENV} -e prometheus_version=$PROMETHEUS_VERSION'
                }
            }
        }
    }
    post {
        failure {
            script{
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
        aborted {
            script{
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
    }
}
