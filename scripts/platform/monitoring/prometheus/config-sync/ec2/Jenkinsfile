@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    triggers{
       parameterizedCron('''
            H(21-30) H/2 * * * %PROMETHEUS_NAME_ID=deploy
        ''')
    }
    options {
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '20'))
        timeout(time: 15, unit: 'MINUTES')
    }
    parameters {
        string(
            name: 'INFRA_BRANCH', defaultValue: 'master', 
            description: 'Branch for Infra Repo to be used'
        )
        string(
            name: 'MONITORING_BRANCH', defaultValue: 'master', 
            description: 'Branch for Monitoring Repo to be used'
        )
        choice(
            name: 'PROMETHEUS_NAME_ID',
            choices: [
                'deploy'
            ],
            description: 'Target Prometheus name'
        )
        booleanParam(
            name: 'REFRESH', defaultValue: false, 
            description: 'Refresh Jenkinsfile pipeline script from SCM'
        )
    }
    environment {
        ENV = 'deploy'
        PROMETHEUS_NAME_ID = 'deploy'
        INFRA_BRANCH = "${params.INFRA_BRANCH}"
        MONITORING_BRANCH = "${params.MONITORING_BRANCH}"
        CHANNEL_NAME = '#alerts-infra'
        JOB_DETAILS="Prometheus Config Sync - EC2"
    }
    stages {
        stage ('Refresh') {
            when {
                expression { 
                    params.REFRESH == true
                }
            }
            steps {
                buildName "#${BUILD_NUMBER}-${ENV}-${MONITORING_BRANCH}"
                echo "Jenkins pipeline script refreshed. Finishing exceution now.."
                script {
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Repo Clone') {
            steps {
                gitCheckout(
                    repoName: 'infra', 
                    gitBranch: "${INFRA_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )
                gitCheckout(
                    repoName: 'monitoring', 
                    gitBranch:  "${MONITORING_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )
            }
        }
        stage('Deployment') {
            steps {
                dir("${env.WORKSPACE}/infra/ansible/monitoring"){
                    sh '''
                        aws secretsmanager get-secret-value --secret-id monitoring_stack_private_key | jq -r '.SecretString' > monitoring.pem
                        chmod 600 monitoring.pem
                        pwd
                        ls -a
                    '''
                    sh 'ansible-playbook prometheus-config-sync.yml -e "monitoring_repo=$WORKSPACE/monitoring" -e env=${ENV} -e prometheus_name_id=${PROMETHEUS_NAME_ID}'
                }
            }
        }
    }
    post {
        failure {
            script{
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
        aborted {
            script{
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ENV}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
    }
}
