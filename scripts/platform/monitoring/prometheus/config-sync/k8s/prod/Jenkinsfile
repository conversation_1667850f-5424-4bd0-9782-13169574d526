@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    triggers {
        parameterizedCron('''
            H(20-30) H/2 * * * %CLUSTER_NAME=epifi-prod
            H(31-40) H/2 * * * %CLUSTER_NAME=epifi-data-prod
        ''')
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '10', artifactNumToKeepStr: '10'))
        timeout(time: 15, unit: 'MINUTES')
    }
    parameters {
        choice(
            name: 'CLUSTER_NAME',
            choices: [
                'epifi-prod',
                'epifi-data-prod'
            ],
            description: 'Target k8s cluster name'
        )
        string(
            name: 'INFRA_BRANCH', defaultValue: 'master',
            description: 'custom k8-infra repo branch specifier'
        )
        string(
            name: 'MONITORING_BRANCH', defaultValue: 'master',
            description: 'custom monitoring repo branch specifier'
        )
        booleanParam(
            name: 'REFRESH', defaultValue: false, 
            description: 'Refresh Jenkinsfile pipeline script from SCM'
        )
    }
    
    environment {
        CLUSTER_NAME = "${params.CLUSTER_NAME}"
        MONITORNG_BRANCH = "${params.MONITORING_BRANCH}"
        INFRA_BRANCH = "${params.INFRA_BRANCH}"
        CHANNEL_NAME = '#alerts-infra-prod'
        JOB_DETAILS= "Kubernetes Prometheus running on cluster: ${CLUSTER_NAME}"
    }
    stages {
        stage ('Refresh') {
            when {
                expression { 
                    params.REFRESH == true
                }
            }
            steps {
                buildName "#${BUILD_NUMBER}-${CLUSTER_NAME}-${MONITORING_BRANCH}"
                echo "Jenkins pipeline script refreshed. Finishing exceution now.."
                script {
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Deploy - Prod') {
            when {
                beforeAgent true
                environment name: 'CLUSTER_NAME', value: 'epifi-prod'
            }
            agent { label 'jenkins-cloud-worker' }
            steps {
                gitCheckout(
                    repoName: 'k8-infra', 
                    gitBranch: "${INFRA_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )
                gitCheckout(
                    repoName: 'monitoring', 
                    gitBranch:  "${MONITORING_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )
                sh """
                    aws eks list-clusters
                    aws eks update-kubeconfig --name=${CLUSTER_NAME}
                    kubectl apply -f ./monitoring/prometheus/k8s/${CLUSTER_NAME}/service-monitors \
                      -n monitoring
                    for prom_rule_fname in \$(find './monitoring/prometheus/k8s/${CLUSTER_NAME}/rules/' -type f -name '*.yml' -printf '%f\n' | cut -d '.' -f 1); do
                        echo \${prom_rule_fname}-prometheus-rules
                        helm upgrade -i \${prom_rule_fname}-prometheus-rules --set service=\${prom_rule_fname} ./k8-infra/monitoring/prometheus-rules -f ./monitoring/prometheus/k8s/${CLUSTER_NAME}/rules/\${prom_rule_fname}.yml
                    done
                """
            }
        }
        stage('Deploy - Data-prod') {
            when {
                beforeAgent true
                environment name: 'CLUSTER_NAME', value: 'epifi-data-prod'
            }
            agent {
                label 'epifi-data-prod-jenkins-cloud-nodes'
            }
            steps {
                gitCheckout(
                    repoName: 'k8-infra', 
                    gitBranch: "${INFRA_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )
                gitCheckout(
                    repoName: 'monitoring', 
                    gitBranch:  "${MONITORING_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )
                sh """
                    aws eks list-clusters
                    aws eks update-kubeconfig --name=${CLUSTER_NAME}
                    kubectl apply -f ./monitoring/prometheus/k8s/${CLUSTER_NAME}/service-monitors \
                      -n monitoring
                    for prom_rule_fname in \$(find './monitoring/prometheus/k8s/${CLUSTER_NAME}/rules/' -type f -name '*.yml' -printf '%f\n' | cut -d '.' -f 1); do
                        echo \${prom_rule_fname}-prometheus-rules
                        helm upgrade -i \${prom_rule_fname}-prometheus-rules --set service=\${prom_rule_fname} ./k8-infra/monitoring/prometheus-rules -f ./monitoring/prometheus/k8s/${CLUSTER_NAME}/rules/\${prom_rule_fname}.yml
                    done
                """
            }
        }
    }
    post {
        failure {
            script{
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${CLUSTER_NAME}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
        aborted {
            script{
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${CLUSTER_NAME}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
    }
}
