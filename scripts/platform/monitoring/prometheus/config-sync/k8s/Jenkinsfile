@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    triggers {
        parameterizedCron('''
              H(0-4) */2 * * *  %ROLE_NAME=DEPLOY;CLUSTER_NAME=epifi-deploy
              H(5-9) */2 * * *  %ROLE_NAME=QA;CLUSTER_NAME=epifi-qa
            H(10-14) */2 * * *  %ROLE_NAME=STAGING;CLUSTER_NAME=epifi-staging
            H(15-19) */2 * * *  %ROLE_NAME=UAT;CLUSTER_NAME=epifi-uat
            H(20-24) */2 * * *  %ROLE_NAME=DATA_DEV;CLUSTER_NAME=epifi-data-dev
            H(25-29) */2 * * *  %ROLE_NAME=DEPLOY;CLUSTER_NAME=epifi-deploy-runners
        ''')
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '10'))
        timeout(time: 15, unit: 'MINUTES')
    }
    parameters {
        string(
          name: 'INFRA_BRANCH', defaultValue: 'master',
          description: 'custom k8-infra repo branch specifier'
        )
        string(
          name: 'MONITORING_BRANCH', defaultValue: 'master',
          description: 'custom monitoring repo branch specifier'
        )
        choice(
          name: 'CLUSTER_NAME',
          choices: [
            'epifi-deploy',
            'epifi-deploy-runners',
            'epifi-staging',
            'epifi-qa',
            'epifi-uat',
            'epifi-data-dev'
          ],
          description: 'Target k8s cluster name'
        )
        choice(
          name: 'ROLE_NAME',
          choices: [
            'DEPLOY',
            'STAGING',
            'QA',
            'UAT',
            'DATA_DEV'
          ],
          description: 'Role identifier for execution, to be selected as per the concerned env'
        )
        booleanParam(
            name: 'REFRESH', defaultValue: false, 
            description: 'Refresh Jenkinsfile pipeline script from SCM'
        )
    }
    environment {
        MONITORING_BRANCH = "${params.MONITORING_BRANCH}"
        INFRA_BRANCH = "${params.INFRA_BRANCH}"
        ROLE_NAME = "${params.ROLE_NAME}"
        CLUSTER_NAME = "${params.CLUSTER_NAME}"
        CHANNEL_NAME = '#alerts-infra'
        JOB_DETAILS="Kubernetes Prometheus running on cluster: ${CLUSTER_NAME}"
    }
    stages {
        stage ('Refresh') {
            when {
                expression { 
                    params.REFRESH == true
                }
            }
            steps {
                buildName "#${BUILD_NUMBER}-REFRESH"
                echo "Jenkins pipeline script refreshed. Finishing exceution now.."
                script {
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Sync - Non-prod') {
            when {
                beforeAgent true
                anyOf {
                    environment name: 'CLUSTER_NAME', value: 'epifi-staging'
                    environment name: 'CLUSTER_NAME', value: 'epifi-deploy'
                    environment name: 'CLUSTER_NAME', value: 'epifi-qa'
                    environment name: 'CLUSTER_NAME', value: 'epifi-uat'
                    environment name: 'CLUSTER_NAME', value: 'epifi-deploy-runners'
                }
            }
            agent { label 'jenkins-cloud-worker' }
            steps {

                buildName "#${BUILD_NUMBER}-${CLUSTER_NAME}-${MONITORING_BRANCH}"

                gitCheckout(
                    repoName: 'k8-infra', 
                    gitBranch: "${INFRA_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )
                gitCheckout(
                    repoName: 'monitoring', 
                    gitBranch:  "${MONITORING_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )

                echo "Syncing ServiceMonitors.."
                script {
                  if(fileExists("./monitoring/prometheus/k8s/${CLUSTER_NAME}/service-monitors")) {
                    sh """
                      aws sts assume-role --region ap-south-1 --role-arn \${${ROLE_NAME}_ACCOUNT_ROLE} \
                        --role-session-name ${CLUSTER_NAME}-prom-k8s-rule-sync > ${WORKSPACE}/.access.json
                      set +x
                      export AWS_ACCESS_KEY_ID=\$(jq -re .Credentials.AccessKeyId .access.json)
                      export AWS_SECRET_ACCESS_KEY=\$(jq -re .Credentials.SecretAccessKey .access.json)
                      export AWS_SESSION_TOKEN=\$(jq -re .Credentials.SessionToken .access.json)
                      set -x
                      aws eks list-clusters
                      aws eks update-kubeconfig --name=${CLUSTER_NAME}
                      kubectl apply -f ./monitoring/prometheus/k8s/${CLUSTER_NAME}/service-monitors \
                        -n monitoring
                    """
                  } else {
                    echo "No ServiceMonitor configs found. Skipping.."
                  }
                }

                echo "Syncing alert rules.."
                sh """
                  aws sts assume-role --region ap-south-1 --role-arn \${${ROLE_NAME}_ACCOUNT_ROLE} \
                    --role-session-name ${CLUSTER_NAME}-prom-k8s-rule-sync > ${WORKSPACE}/.access.json
                  set +x
                  export AWS_ACCESS_KEY_ID=\$(jq -re .Credentials.AccessKeyId .access.json)
                  export AWS_SECRET_ACCESS_KEY=\$(jq -re .Credentials.SecretAccessKey .access.json)
                  export AWS_SESSION_TOKEN=\$(jq -re .Credentials.SessionToken .access.json)
                  set -x
                  aws eks list-clusters
                  aws eks update-kubeconfig --name=${CLUSTER_NAME}
                  for prom_rule_fname in \$(find './monitoring/prometheus/k8s/${CLUSTER_NAME}/rules/' -type f -name '*.yml' -printf '%f\n' | cut -d '.' -f 1); do
                      echo \${prom_rule_fname}-prometheus-rules
                      helm upgrade -i \${prom_rule_fname}-prometheus-rules --set service=\${prom_rule_fname} ./k8-infra/monitoring/prometheus-rules -f ./monitoring/prometheus/k8s/${CLUSTER_NAME}/rules/\${prom_rule_fname}.yml
                  done
                """

            }
        }
        stage('Sync - Data-dev') {
            when {
                beforeAgent true
                environment name: 'CLUSTER_NAME', value: 'epifi-data-dev'
            }
            agent {
                label 'jenkins-data-dev-slave-node'
            }
            steps {

                buildName "#${BUILD_NUMBER}-${CLUSTER_NAME}-${MONITORING_BRANCH}"

                gitCheckout(
                    repoName: 'k8-infra', 
                    gitBranch: "${INFRA_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )
                gitCheckout(
                    repoName: 'monitoring', 
                    gitBranch:  "${MONITORING_BRANCH}", 
                    githubOrg: 'epifi', 
                    cleanCheckout: true, 
                    shallow: false
                )

                echo "Syncing ServiceMonitors.."
                script {
                  if(fileExists("./monitoring/prometheus/k8s/${CLUSTER_NAME}/service-monitors")) {
                    sh """
                      aws eks update-kubeconfig --name=${CLUSTER_NAME}
                      kubectl apply -f ./monitoring/prometheus/k8s/${CLUSTER_NAME}/service-monitors \
                        -n monitoring
                    """
                  } else {
                    echo "No ServiceMonitor configs found. Skipping.."
                  }
                }

                echo "Syncing alert rules.."
                sh """
                  aws eks update-kubeconfig --name=${CLUSTER_NAME}
                  for prom_rule_fname in \$(find './monitoring/prometheus/k8s/${CLUSTER_NAME}/rules/' -type f -name '*.yml' -printf '%f\n' | cut -d '.' -f 1); do
                    echo \${prom_rule_fname}-prometheus-rules
                    helm upgrade -i \${prom_rule_fname}-prometheus-rules --set service=\${prom_rule_fname} ./k8-infra/monitoring/prometheus-rules -f ./monitoring/prometheus/k8s/${CLUSTER_NAME}/rules/\${prom_rule_fname}.yml
                  done
                """

            }
        }
    }
    post {
        failure {
            script{
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ROLE_NAME}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
        aborted {
            script{
                sendNotifications(
                    channelName: "${CHANNEL_NAME}", 
                    ENV: "${ROLE_NAME}",
                    jobDetails: "${JOB_DETAILS}", 
                )
                currentBuild.setKeepLog(true)
            }
        }
    }
}
