#!/bin/bash

set -e
set -o pipefail

env=$1
user=$2


mkdir -p certs private-ca

download_ca() {
    echo "downloading the cert/key authority pair from secretsmanager"
    aws secretsmanager get-secret-value --secret-id ${env}/cockroach/ca.key | jq -r '.SecretString | fromjson | ."ca.key"' | tr ' ' '\n' | sed -z 's/BEGIN\nRSA\nPRIVATE\nKEY/BEGIN RSA PRIVATE KEY/g' | sed -z 's/END\nRSA\nPRIVATE\nKEY/END RSA PRIVATE KEY/g' > ./private-ca/ca.key
    aws secretsmanager get-secret-value --secret-id ${env}/cockroach/ca.crt | jq -r '.SecretString' | tr ' ' '\n' | sed -z 's/BEGIN\nCERTIFICATE/BEGIN CERTIFICATE/g' | sed -z 's/END\nCERTIFICATE/END CERTIFICATE/g' > ./certs/ca.crt
}

user_generate() {
  #. Creating the certificate for user.
  echo "generating the key for { $user } user."
  /usr/local/bin/cockroach cert create-client $user --certs-dir=./certs/ --ca-key=./private-ca/ca.key
}

upload_client_secrets() {
    echo "uploading secrets on secret manager."
    aws secretsmanager create-secret --name ${env}/cockroach/client.${user}.crt --description "client.${user}.crt for ${env} environment cockroachdb" --secret-string file://./certs/client.${user}.crt
    aws secretsmanager create-secret --name ${env}/cockroach/client.${user}.key --description "client.${user}.key for ${env} environment cockroachdb" --secret-string file://./certs/client.${user}.key
    rm -rf certs private-ca
}

secrets_list="$(aws secretsmanager list-secrets | jq -r ".SecretList[].Name")"
if [[ "${secrets_list[@]}" =~ "${env}/cockroach/ca.key" ]] && [[ "${secrets_list[@]}" =~ "${env}/cockroach/ca.crt" ]]; then
  echo "CA Secrets already exits, Creating Client Secrets"
  download_ca
  user_generate
  upload_client_secrets
else
  echo "CA Secrets does not exits, Please create a CA first"
  echo "Client Secret Creation failed"
  exit 1
fi

