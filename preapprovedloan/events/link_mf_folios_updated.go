// nolint: gocritic
package events

import (
	"time"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/be-common/pkg/events"

	"github.com/fatih/structs"
	"github.com/google/uuid"
)

const (
	UpdateTypeEmail = "UpdateTypeEmail"
	UpdateTypePhone = "UpdateTypePhone"
)

type MfFoliosUpdateCountEvent struct {
	ActorId                        string
	ProspectId                     string
	SessionId                      string
	EventId                        string
	Timestamp                      time.Time
	Vendor                         string
	LoanProgram                    string
	EventType                      string
	PreviousUnlinkedFolioIsinCount int
	NewUnlinkedFolioIsinCount      int
	UpdateType                     string
}

func NewMfFoliosUpdateCountEvent(lr *palPb.LoanRequest, updateType string, previousUnlinkedFolioIsinCount, newUnlinkedFolioIsinCount int) *MfFoliosUpdateCountEvent {
	return &MfFoliosUpdateCountEvent{
		ActorId:                        lr.GetActorId(),
		EventId:                        uuid.New().String(),
		Timestamp:                      time.Now(),
		Vendor:                         lr.GetVendor().String(),
		LoanProgram:                    lr.GetLoanProgram().String(),
		EventType:                      events.EventTrack,
		PreviousUnlinkedFolioIsinCount: previousUnlinkedFolioIsinCount,
		NewUnlinkedFolioIsinCount:      newUnlinkedFolioIsinCount,
		UpdateType:                     updateType,
	}
}

func (c *MfFoliosUpdateCountEvent) GetEventType() string {
	return c.EventType
}

func (c *MfFoliosUpdateCountEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *MfFoliosUpdateCountEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *MfFoliosUpdateCountEvent) GetEventId() string {
	return c.EventId
}

func (c *MfFoliosUpdateCountEvent) GetUserId() string {
	return c.ActorId
}

func (c *MfFoliosUpdateCountEvent) GetProspectId() string {
	return c.ProspectId
}

func (c *MfFoliosUpdateCountEvent) GetEventName() string {
	return EventMfFoliosUpdateCount
}
