package wrapper

import (
	"context"

	"github.com/epifi/gamma/preapprovedloan/calculator/types"
	"github.com/epifi/gamma/preapprovedloan/helper/calculators"
	"github.com/epifi/gamma/preapprovedloan/helper/calculators/provider/basecalculator"
)

type Provider struct {
}

func NewProvider() *Provider {
	return &Provider{}
}

var _ types.CalculatorProvider = &Provider{}

func (p *Provider) GetCalculator(
	ctx context.Context,
	req *types.Request,
) (types.Calculator, error) {
	loanOffer := req.GetLoanOffer()
	base := &basecalculator.Calculator{LoanOffer: loanOffer}
	legacyCalculator := calculators.GetCalculator(ctx, calculators.GetCalculatorProviderRequest{
		Vendor:      loanOffer.GetVendor(),
		LoanProgram: loanOffer.GetLoanProgram(),
		Offer:       loanOffer,
	}, base)
	return &calculator{
		req:                req,
		originalCalculator: legacyCalculator,
	}, nil
}
